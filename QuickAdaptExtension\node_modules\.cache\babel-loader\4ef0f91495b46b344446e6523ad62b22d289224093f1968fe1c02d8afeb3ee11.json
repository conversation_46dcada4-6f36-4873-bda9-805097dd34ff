{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\RTEsection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, forwardRef, useCallback, useMemo } from \"react\";\nimport { Box, IconButton } from \"@mui/material\";\nimport JoditEditor from \"jodit-react\";\nimport useDrawerStore from \"../../../store/drawerStore\";\nimport { copyicon, editicon, grythreedot, deletestep } from \"../../../assets/icons/icons\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RTEsection = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  textBoxRef,\n  isBanner,\n  handleDeleteRTESection,\n  index,\n  guidePopUpRef,\n  onClone,\n  isCloneDisabled\n}, ref) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    rtesContainer,\n    updateRTEContainer,\n    setIsUnSavedChanges,\n    cloneRTEContainer,\n    clearRteDetails,\n    selectedTemplate,\n    selectedTemplateTour,\n    announcementGuideMetaData,\n    toolTipGuideMetaData,\n    handleAnnouncementRTEValue,\n    handleTooltipRTEValue,\n    createWithAI,\n    currentStep,\n    ensureAnnouncementRTEContainer,\n    toolbarVisibleRTEId,\n    setToolbarVisibleRTEId\n  } = useDrawerStore();\n\n  // Individual state management for each RTE (excluding toolbarVisibleRTEId which is now global)\n  const [editingRTEId, setEditingRTEId] = useState(null);\n  const [hoveredRTEId, setHoveredRTEId] = useState(null);\n  const [contentState, setContentState] = useState({});\n\n  // Cursor position preservation\n  const cursorPositionsRef = useRef(new Map());\n  const contentRef = useRef(\"\");\n\n  // Map to store individual refs for each RTE\n  const editorRefs = useRef(new Map());\n  const containerRefs = useRef(new Map());\n\n  // Helper function to get or create editor ref for specific RTE\n  const getEditorRef = rteId => {\n    if (!editorRefs.current.has(rteId)) {\n      editorRefs.current.set(rteId, /*#__PURE__*/React.createRef());\n    }\n    return editorRefs.current.get(rteId);\n  };\n\n  // Helper function to get or create container ref for specific RTE\n  const getContainerRef = rteId => {\n    if (!containerRefs.current.has(rteId)) {\n      containerRefs.current.set(rteId, /*#__PURE__*/React.createRef());\n    }\n    return containerRefs.current.get(rteId);\n  };\n\n  // Helper functions for content state management\n  const isContentEmpty = (content, rteId) => {\n    if (!content) return true;\n\n    // Remove HTML tags and check if there's actual text content\n    const textContent = content.replace(/<[^>]*>/g, '').trim();\n    if (!textContent) return true;\n\n    // Check if it's just default placeholder content\n    const defaultContent = ['<p><br></p>', '<p></p>', '<br>', ''];\n    if (defaultContent.includes(content.trim())) return true;\n    return false;\n  };\n  const isContentScrollable = rteId => {\n    const editorRef = getEditorRef(rteId);\n    if (editorRef !== null && editorRef !== void 0 && editorRef.current) {\n      try {\n        var _editor;\n        const workplace = (_editor = editorRef.current.editor) === null || _editor === void 0 ? void 0 : _editor.workplace;\n        if (workplace) {\n          return workplace.scrollHeight > workplace.clientHeight;\n        }\n      } catch (error) {\n        // Silently handle any errors\n      }\n    }\n    return false;\n  };\n\n  // Update content state for dynamic icon positioning\n  const updateContentState = React.useCallback((content, rteId) => {\n    const isEmpty = isContentEmpty(content, rteId);\n    const isScrollable = isContentScrollable(rteId);\n    setContentState(prev => ({\n      ...prev,\n      [rteId]: {\n        isEmpty,\n        isScrollable\n      }\n    }));\n  }, []);\n\n  // Handle clicks outside the editor - now works with individual RTEs\n  useEffect(() => {\n    const handleClickOutside = event => {\n      var _document$querySelect, _document$querySelect2, _document$querySelect3, _document$querySelect4;\n      if (!toolbarVisibleRTEId) return; // No RTE toolbar is currently visible\n\n      const isInsideJoditPopupContent = event.target.closest(\".jodit-popup__content\") !== null;\n      const isInsideAltTextPopup = event.target.closest(\".jodit-ui-input\") !== null;\n      const isInsidePopup = (_document$querySelect = document.querySelector(\".jodit-popup\")) === null || _document$querySelect === void 0 ? void 0 : _document$querySelect.contains(event.target);\n      const isInsideJoditPopup = (_document$querySelect2 = document.querySelector(\".jodit-wysiwyg\")) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.contains(event.target);\n      const isInsideWorkplacePopup = isInsideJoditPopup || ((_document$querySelect3 = document.querySelector(\".jodit-dialog__panel\")) === null || _document$querySelect3 === void 0 ? void 0 : _document$querySelect3.contains(event.target));\n      const isSelectionMarker = event.target.id.startsWith(\"jodit-selection_marker_\");\n      const isLinkPopup = (_document$querySelect4 = document.querySelector(\".jodit-ui-input__input\")) === null || _document$querySelect4 === void 0 ? void 0 : _document$querySelect4.contains(event.target);\n      const isInsideToolbarButton = event.target.closest(\".jodit-toolbar-button__button\") !== null;\n      const isInsertButton = event.target.closest(\"button[aria-pressed='false']\") !== null;\n\n      // Get the container ref for the currently visible toolbar RTE\n      const currentContainerRef = getContainerRef(toolbarVisibleRTEId);\n\n      // Check if the target is inside the currently active RTE or related elements\n      if (currentContainerRef !== null && currentContainerRef !== void 0 && currentContainerRef.current && !currentContainerRef.current.contains(event.target) &&\n      // Click outside the current editor container\n      !isInsidePopup &&\n      // Click outside the popup\n      !isInsideJoditPopup &&\n      // Click outside the WYSIWYG editor\n      !isInsideWorkplacePopup &&\n      // Click outside the workplace popup\n      !isSelectionMarker &&\n      // Click outside selection markers\n      !isLinkPopup &&\n      // Click outside link input popup\n      !isInsideToolbarButton &&\n      // Click outside the toolbar button\n      !isInsertButton && !isInsideJoditPopupContent && !isInsideAltTextPopup) {\n        setEditingRTEId(null); // Close the currently editing RTE\n        setToolbarVisibleRTEId(null); // Also hide toolbar when clicking outside\n      }\n    };\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => document.removeEventListener(\"mousedown\", handleClickOutside);\n  }, [toolbarVisibleRTEId]);\n\n  // Handle clicks outside the editor - enhanced to properly handle Jodit popups\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (!toolbarVisibleRTEId) return; // No RTE toolbar is currently visible\n\n      const target = event.target;\n\n      // Comprehensive Jodit popup detection\n      const joditPopupSelectors = ['.jodit-popup', '.jodit-popup__content', '.jodit-dialog', '.jodit-dialog__panel', '.jodit-dialog__content', '.jodit-ui-input', '.jodit-ui-input__input', '.jodit-ui-button', '.jodit-toolbar-button', '.jodit-toolbar-button__button', '.jodit-color-picker', '.jodit-color-picker__palette', '.jodit-filebrowser', '.jodit-filebrowser__dialog', '.jodit-link-dialog', '.jodit-image-dialog', '.jodit-table-dialog', '.jodit-form', '.jodit-form__group', '.jodit-dropdown', '.jodit-dropdown__content', '.jodit-list', '.jodit-list__item'];\n\n      // Check if click is inside any Jodit popup/dialog\n      const isInsideJoditPopup = joditPopupSelectors.some(selector => {\n        const element = document.querySelector(selector);\n        return element && element.contains(target);\n      });\n\n      // Additional checks for dynamic Jodit elements\n      const isJoditElement = target.closest('.jodit-popup, .jodit-dialog, .jodit-ui-input, .jodit-color-picker, .jodit-filebrowser, .jodit-dropdown, .jodit-form') !== null;\n      const isJoditToolbarButton = target.closest('.jodit-toolbar-button') !== null;\n      const isSelectionMarker = target.id && target.id.startsWith(\"jodit-selection_marker_\");\n      const isJoditWorkplace = target.closest('.jodit-wysiwyg, .jodit-workplace') !== null;\n\n      // Get the container ref for the currently visible toolbar RTE\n      const currentContainerRef = getContainerRef(toolbarVisibleRTEId);\n\n      // Only close toolbar if click is truly outside all RTE and Jodit elements\n      if (currentContainerRef !== null && currentContainerRef !== void 0 && currentContainerRef.current && !currentContainerRef.current.contains(target) &&\n      // Click outside the current editor container\n      !isInsideJoditPopup &&\n      // Click outside any Jodit popup\n      !isJoditElement &&\n      // Click outside any Jodit element\n      !isJoditToolbarButton &&\n      // Click outside toolbar buttons\n      !isSelectionMarker &&\n      // Click outside selection markers\n      !isJoditWorkplace // Click outside the editor workplace\n      ) {\n        setEditingRTEId(null); // Close the currently editing RTE\n        setToolbarVisibleRTEId(null); // Also hide toolbar when clicking outside\n      }\n    };\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => document.removeEventListener(\"mousedown\", handleClickOutside);\n  }, [toolbarVisibleRTEId]);\n\n  // Preserve cursor position when toolbar visibility changes\n  useEffect(() => {\n    if (toolbarVisibleRTEId) {\n      const editorRef = getEditorRef(toolbarVisibleRTEId);\n      if (editorRef !== null && editorRef !== void 0 && editorRef.current) {\n        const editor = editorRef.current.editor;\n        if (editor) {\n          // Save current cursor position before any changes\n          const selection = editor.selection;\n          if (selection) {\n            const range = selection.range;\n            if (range) {\n              cursorPositionsRef.current.set(toolbarVisibleRTEId, {\n                start: range.startOffset,\n                end: range.endOffset\n              });\n            }\n          }\n\n          // Focus the editor after toolbar shows\n          setTimeout(() => {\n            editor.focus();\n\n            // Restore cursor position if available\n            const savedPosition = cursorPositionsRef.current.get(toolbarVisibleRTEId);\n            if (savedPosition && selection) {\n              try {\n                const range = editor.editorDocument.createRange();\n                const textNode = editor.editor.firstChild;\n                if (textNode) {\n                  var _textNode$textContent, _textNode$textContent2;\n                  range.setStart(textNode, Math.min(savedPosition.start, ((_textNode$textContent = textNode.textContent) === null || _textNode$textContent === void 0 ? void 0 : _textNode$textContent.length) || 0));\n                  range.setEnd(textNode, Math.min(savedPosition.end, ((_textNode$textContent2 = textNode.textContent) === null || _textNode$textContent2 === void 0 ? void 0 : _textNode$textContent2.length) || 0));\n                  selection.selectRange(range);\n                }\n              } catch (e) {\n                // Fallback: just focus without setting position\n                console.debug('Cursor position restoration failed, using fallback');\n              }\n            }\n          }, 100);\n        }\n      }\n    }\n  }, [toolbarVisibleRTEId]);\n  const handleUpdate = useCallback((newContent, rteId, containerId) => {\n    contentRef.current = newContent;\n\n    // Check if this is an AI-created guide\n    const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n    const isAITour = createWithAI && selectedTemplate === \"Tour\";\n    const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\n    const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\n    const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\n    console.log(\"RTEsection handleUpdate:\", {\n      createWithAI,\n      selectedTemplate,\n      selectedTemplateTour,\n      isAIAnnouncement,\n      isAITour,\n      isTourBanner,\n      containerId,\n      newContent: newContent.substring(0, 50) + \"...\"\n    });\n    if (isAIAnnouncement) {\n      const currentStepIndex = currentStep - 1;\n      if (isTourAnnouncement) {\n        var _toolTipGuideMetaData, _toolTipGuideMetaData2;\n        // For Tour+Announcement, use toolTipGuideMetaData\n        const tooltipContainer = (_toolTipGuideMetaData = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData === void 0 ? void 0 : (_toolTipGuideMetaData2 = _toolTipGuideMetaData.containers) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.find(container => container.id === containerId && container.type === \"rte\");\n        if (tooltipContainer) {\n          // Use the tooltip-specific handler for tour announcements\n          handleTooltipRTEValue(containerId, newContent);\n        }\n      } else {\n        var _announcementGuideMet, _announcementGuideMet2;\n        // For pure Announcements, use announcementGuideMetaData\n        const announcementContainer = (_announcementGuideMet = announcementGuideMetaData[currentStepIndex]) === null || _announcementGuideMet === void 0 ? void 0 : (_announcementGuideMet2 = _announcementGuideMet.containers) === null || _announcementGuideMet2 === void 0 ? void 0 : _announcementGuideMet2.find(container => container.id === containerId && container.type === \"rte\");\n        if (announcementContainer) {\n          // Use the announcement-specific handler\n          handleAnnouncementRTEValue(containerId, newContent);\n        }\n      }\n    } else if (isAITour && (isTourBanner || isTourTooltip)) {\n      var _toolTipGuideMetaData3, _toolTipGuideMetaData4;\n      // For AI Tour with Banner, Tooltip, or Hotspot steps, use toolTipGuideMetaData\n      const currentStepIndex = currentStep - 1;\n      const tooltipContainer = (_toolTipGuideMetaData3 = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData3 === void 0 ? void 0 : (_toolTipGuideMetaData4 = _toolTipGuideMetaData3.containers) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : _toolTipGuideMetaData4.find(container => container.id === containerId && container.type === \"rte\");\n      if (tooltipContainer) {\n        // Use the tooltip-specific handler for all tour step types\n        handleTooltipRTEValue(containerId, newContent);\n        console.log(`Used handleTooltipRTEValue for ${selectedTemplateTour} step in AI tour`);\n      } else {\n        var _toolTipGuideMetaData5, _toolTipGuideMetaData6;\n        console.warn(`No tooltip container found for ${selectedTemplateTour} step`, {\n          currentStepIndex,\n          containerId,\n          availableContainers: (_toolTipGuideMetaData5 = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData5 === void 0 ? void 0 : (_toolTipGuideMetaData6 = _toolTipGuideMetaData5.containers) === null || _toolTipGuideMetaData6 === void 0 ? void 0 : _toolTipGuideMetaData6.map(c => ({\n            id: c.id,\n            type: c.type\n          }))\n        });\n      }\n    } else {\n      // For non-AI content or other cases, use the regular RTE container system\n      updateRTEContainer(containerId, rteId, newContent);\n      console.log(\"Used updateRTEContainer for non-AI content\");\n    }\n    setIsUnSavedChanges(true);\n\n    // Update content state for dynamic icon positioning\n    updateContentState(newContent, rteId);\n  }, [createWithAI, selectedTemplate, selectedTemplateTour, currentStep, toolTipGuideMetaData, announcementGuideMetaData, handleTooltipRTEValue, handleAnnouncementRTEValue, updateRTEContainer, setIsUnSavedChanges, updateContentState]);\n  const handleCloneContainer = containerId => {\n    // Check if cloning is disabled due to section limits\n    if (isCloneDisabled) {\n      return; // Don't clone if limit is reached\n    }\n\n    // Call the clone function from the store\n    cloneRTEContainer(containerId);\n\n    // Call the onClone callback if provided\n    if (onClone) {\n      onClone();\n    }\n  };\n  const handleDeleteSection = (containerId, rteId) => {\n    // Check if this is an AI-created announcement\n    const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n    if (isAIAnnouncement) {\n      // For AI announcements, we need to remove from announcementGuideMetaData\n      // This would require a new function in the store, for now just call the existing one\n      clearRteDetails(containerId, rteId);\n    } else {\n      // For banners and non-AI content, use the regular clear function\n      clearRteDetails(containerId, rteId);\n    }\n\n    // Call the handleDeleteRTESection callback to update section counts\n    handleDeleteRTESection(index);\n  };\n  const handlePaste = event => {\n    event.preventDefault();\n    const clipboardData = event.clipboardData;\n    const pastedText = clipboardData.getData(\"text/plain\");\n    const pastedHtml = clipboardData.getData(\"text/html\");\n    if (pastedHtml) {\n      const isRTEContent = pastedHtml.includes(\"<!--RTE-->\");\n      if (isRTEContent) {\n        insertContent(pastedHtml);\n      } else {\n        insertContent(pastedHtml);\n      }\n    } else {\n      insertContent(pastedText);\n    }\n  };\n  const insertContent = content => {\n    if (toolbarVisibleRTEId) {\n      const editorRef = getEditorRef(toolbarVisibleRTEId);\n      if (editorRef !== null && editorRef !== void 0 && editorRef.current) {\n        const editor = editorRef.current.editor;\n        editor.selection.insertHTML(content);\n      }\n    }\n  };\n  const [isRtlDirection, setIsRtlDirection] = useState(false);\n  useEffect(() => {\n    const dir = document.body.getAttribute(\"dir\") || \"ltr\";\n    setIsRtlDirection(dir.toLowerCase() === \"rtl\");\n  }, []);\n\n  // Stable base config parts that don't change - memoized once\n  const baseConfigParts = useMemo(() => ({\n    buttons: ['bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush', 'font', 'fontsize', 'link', {\n      name: 'more',\n      iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\n      list: ['source', 'image', 'video', 'table', 'align', 'undo', 'redo', '|', 'hr', 'eraser', 'copyformat', 'symbol', 'fullsize', 'print', 'superscript', 'subscript', '|', 'outdent', 'indent', 'paragraph']\n    }],\n    link: {\n      followOnDblClick: false,\n      processVideoLink: true,\n      processPastedLink: true,\n      openInNewTabCheckbox: true,\n      noFollowCheckbox: false,\n      modeClassName: 'input'\n    },\n    dialog: {\n      zIndex: 100001\n    },\n    popup: {\n      zIndex: 100002\n    },\n    controls: {\n      font: {\n        list: {\n          \"Poppins, sans-serif\": \"Poppins\",\n          \"Roboto, sans-serif\": \"Roboto\",\n          \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\n          \"Open Sans, sans-serif\": \"Open Sans\",\n          \"Calibri, sans-serif\": \"Calibri\",\n          \"Century Gothic, sans-serif\": \"Century Gothic\"\n        }\n      }\n    }\n  }), []);\n\n  // Per-RTE config cache to minimize recreation\n  const configCacheRef = useRef(new Map());\n\n  // Smart config creation - only recreate when toolbar state actually changes\n  const getConfigForRTE = useCallback(rteId => {\n    const isToolbarVisible = toolbarVisibleRTEId === rteId;\n    const cached = configCacheRef.current.get(rteId);\n\n    // If we have a cached config and toolbar state hasn't changed, reuse it\n    if (cached && cached.lastToolbarState === isToolbarVisible) {\n      return cached.config;\n    }\n\n    // Create new config only when toolbar state changes\n    const newConfig = {\n      readonly: false,\n      direction: isRtlDirection ? 'rtl' : 'ltr',\n      language: 'en',\n      placeholder: 'Enter your text here',\n      toolbarSticky: false,\n      toolbarAdaptive: false,\n      // Dynamic toolbar visibility - this triggers the display change\n      toolbar: isToolbarVisible,\n      showCharsCounter: false,\n      showWordsCounter: false,\n      showXPathInStatusbar: false,\n      statusbar: false,\n      pastePlain: true,\n      askBeforePasteHTML: false,\n      askBeforePasteFromWord: false,\n      height: 'auto',\n      // Dynamic height based on toolbar visibility\n      minHeight: isToolbarVisible ? 150 : 28,\n      maxHeight: 180,\n      autofocus: false,\n      popupRoot: document.body,\n      zIndex: 100000,\n      globalFullSize: false,\n      cursorAfterAutofocus: 'end',\n      events: {\n        onPaste: handlePaste\n      },\n      // Spread stable parts to minimize object creation\n      ...baseConfigParts\n    };\n\n    // Cache the new config with its toolbar state\n    configCacheRef.current.set(rteId, {\n      config: newConfig,\n      lastToolbarState: isToolbarVisible\n    });\n    return newConfig;\n  }, [isRtlDirection, handlePaste, baseConfigParts, toolbarVisibleRTEId]);\n\n  // Determine which containers to use based on guide type\n  const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n  const isAITour = createWithAI && selectedTemplate === \"Tour\";\n  const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\n  const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\n  const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\n  const currentStepIndex = currentStep - 1;\n  let containersToRender = [];\n  if (isAIAnnouncement && !isTourAnnouncement) {\n    // For pure AI announcements (not in tours), use announcementGuideMetaData\n    containersToRender = ensureAnnouncementRTEContainer(currentStepIndex, false);\n  } else if (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\n    var _toolTipGuideMetaData7;\n    // For AI Tour with any step type (Banner, Tooltip, Hotspot, or Announcement), use toolTipGuideMetaData\n    if ((_toolTipGuideMetaData7 = toolTipGuideMetaData[currentStepIndex]) !== null && _toolTipGuideMetaData7 !== void 0 && _toolTipGuideMetaData7.containers) {\n      containersToRender = toolTipGuideMetaData[currentStepIndex].containers.filter(c => c.type === \"rte\");\n      console.log(`RTEsection: Using toolTipGuideMetaData containers for ${selectedTemplateTour} step ${currentStepIndex}:`, {\n        totalContainers: toolTipGuideMetaData[currentStepIndex].containers.length,\n        rteContainers: containersToRender.length,\n        rteData: containersToRender.map(c => ({\n          id: c.id,\n          rteBoxValue: c.rteBoxValue\n        }))\n      });\n    } else {\n      console.warn(`RTEsection: No toolTipGuideMetaData found for ${selectedTemplateTour} step ${currentStepIndex}`);\n      containersToRender = [];\n    }\n  } else {\n    // For non-AI content, use rtesContainer\n    containersToRender = rtesContainer;\n  }\n\n  // Initialize content state for all RTEs\n  React.useEffect(() => {\n    containersToRender.forEach(item => {\n      let rteText = \"\";\n      let rteId = \"\";\n      let id = \"\";\n      if (isAIAnnouncement && !isTourAnnouncement || isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\n        rteText = item.rteBoxValue || \"\";\n        rteId = item.id;\n        id = item.id;\n      } else {\n        var _item$rtes, _item$rtes$, _item$rtes2, _item$rtes2$;\n        rteText = ((_item$rtes = item.rtes) === null || _item$rtes === void 0 ? void 0 : (_item$rtes$ = _item$rtes[0]) === null || _item$rtes$ === void 0 ? void 0 : _item$rtes$.text) || \"\";\n        rteId = (_item$rtes2 = item.rtes) === null || _item$rtes2 === void 0 ? void 0 : (_item$rtes2$ = _item$rtes2[0]) === null || _item$rtes2$ === void 0 ? void 0 : _item$rtes2$.id;\n        id = item.id;\n      }\n      if (id && rteId) {\n        updateContentState(rteText, rteId);\n      }\n    });\n  }, [containersToRender, isAIAnnouncement, isTourAnnouncement, isAITour, isTourBanner, isTourTooltip, updateContentState]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: containersToRender.map(item => {\n      let rteText = \"\";\n      let rteId = \"\";\n      let id = \"\";\n      if (isAIAnnouncement && !isTourAnnouncement || isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\n        // For AI announcements and AI tour steps (banner, tooltip, hotspot, announcement), get data from container\n        // Both announcementGuideMetaData and toolTipGuideMetaData use the same structure for RTE containers\n        rteText = item.rteBoxValue || \"\";\n        rteId = item.id;\n        id = item.id;\n      } else {\n        var _item$rtes3, _item$rtes3$, _item$rtes4, _item$rtes4$;\n        // For non-AI content, get data from rtesContainer\n        rteText = ((_item$rtes3 = item.rtes) === null || _item$rtes3 === void 0 ? void 0 : (_item$rtes3$ = _item$rtes3[0]) === null || _item$rtes3$ === void 0 ? void 0 : _item$rtes3$.text) || \"\";\n        rteId = (_item$rtes4 = item.rtes) === null || _item$rtes4 === void 0 ? void 0 : (_item$rtes4$ = _item$rtes4[0]) === null || _item$rtes4$ === void 0 ? void 0 : _item$rtes4$.id;\n        id = item.id;\n      }\n      if (!id) return null;\n      const currentContainerRef = getContainerRef(id);\n      const currentEditorRef = getEditorRef(id);\n\n      // Get config for this specific RTE instance\n      const config = getConfigForRTE(id);\n\n      // Dynamic CSS for this specific RTE to prevent scrollbar conflicts and fix popup z-index\n      const dynamicCSS = `\n                        /* Ensure Jodit popups appear above all other elements */\n                        .jodit-popup,\n                        .jodit-dialog,\n                        .jodit-color-picker,\n                        .jodit-filebrowser,\n                        .jodit-dropdown {\n                            z-index: 100002 !important;\n                        }\n\n                        .jodit-popup__content,\n                        .jodit-dialog__content,\n                        .jodit-ui-input,\n                        .jodit-form {\n                            z-index: 100003 !important;\n                        }\n                        /* Hide the add new line button */\n                        .jodit-add-new-line {\n                            display: none !important;\n                        }\n                        /* Scoped styles for RTE ${id} */\n                        #rte-${id} .jodit-wysiwyg {\n                            color: #000000 !important;\n                            line-height: 1.4 !important;\n                            padding: 8px !important;\n                        }\n                        /* Height and scrolling behavior - scoped to this specific RTE */\n                        #rte-${id} .jodit-workplace {\n                            min-height: ${toolbarVisibleRTEId === id ? '90px' : '28px'} !important;\n                            max-height: 180px !important;\n                            overflow-y: auto !important;\n                            line-height: 1.4 !important;\n                            background-color: ${toolbarVisibleRTEId === id ? '#ffffff !important' : 'inherit'};\n                        }\n                        #rte-${id} .jodit-container {\n                            border: none !important;\n                            background-color: ${toolbarVisibleRTEId === id ? '#ffffff !important' : 'inherit'};\n                        }\n                        /* Enhanced scrollbar styling */\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar {\n                            width: 6px !important;\n                        }\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar-track {\n                            background: #f1f1f1 !important;\n                            border-radius: 3px !important;\n                        }\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar-thumb {\n                            background: #c1c1c1 !important;\n                            border-radius: 3px !important;\n                        }\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar-thumb:hover {\n                            background: #a8a8a8 !important;\n                        }\n                            \t#rte-${id} .jodit-wysiwyg p {\n                            color: #000000 !important;\n                            margin: 0 0 4px 0 !important;\n                            padding: 0 !important;\n                            line-height: 1.4 !important;\n\t\t\t}\n                    `;\n      return /*#__PURE__*/_jsxDEV(Box, {\n        ref: currentContainerRef,\n        onMouseEnter: () => setHoveredRTEId(id),\n        onMouseLeave: () => setHoveredRTEId(null),\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          position: \"relative\",\n          \"&:hover .rte-action-icons\": {\n            opacity: \"1 !important\",\n            visibility: \"visible !important\"\n          },\n          \"&.qadpt-rte:hover .rte-action-icons\": {\n            opacity: \"1 !important\",\n            visibility: \"visible !important\"\n          },\n          \"& .jodit-status-bar-link\": {\n            display: \"none !important\"\n          },\n          \"& .jodit-editor\": {\n            fontFamily: \"'Roboto', sans-serif !important\"\n          },\n          \".jodit-editor span\": {\n            fontFamily: \"'Roboto', sans-serif !important\"\n          },\n          \".jodit-toolbar-button button\": {\n            minWidth: \"29px !important\"\n          },\n          \".jodit-react-container\": {\n            width: \"100%\",\n            whiteSpace: \"pre-wrap\",\n            wordBreak: \"break-word\"\n          },\n          // Fix Jodit dialog positioning - target correct classes\n          \".jodit.jodit-dialog\": {\n            position: \"fixed !important\",\n            zIndex: \"100001 !important\",\n            top: \"50% !important\",\n            left: \"50% !important\",\n            transform: \"translate(-50%, -50%) !important\"\n          },\n          \".jodit-dialog .jodit-dialog__panel\": {\n            position: \"relative !important\",\n            top: \"auto !important\",\n            left: \"auto !important\",\n            transform: \"none !important\",\n            maxWidth: \"400px !important\",\n            background: \"white !important\",\n            border: \"1px solid #ccc !important\",\n            borderRadius: \"4px !important\",\n            boxShadow: \"0 4px 12px rgba(0,0,0,0.15) !important\"\n          },\n          // Fix for link dialog specifically\n          \".jodit-dialog_alert\": {\n            position: \"fixed !important\",\n            zIndex: \"100001 !important\",\n            top: \"50% !important\",\n            left: \"50% !important\",\n            transform: \"translate(-50%, -50%) !important\"\n          }\n        },\n        className: \"qadpt-rte\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: \"absolute\",\n            // Complex positioning logic:\n            // 1. When toolbar visible: top-right of jodit-workplace (below toolbar)\n            // 2. When no content: positioned well to the left of edit icon to avoid overlap\n            // 3. When has content: top-right corner\n            top: toolbarVisibleRTEId === id ? \"43px\" : selectedTemplate === \"Banner\" ? \"7px\" : \"4px\",\n            right: \"15px\",\n            left: \"auto\",\n            // transform: toolbarVisibleRTEId === id ? \"none\" : ((contentState[id]?.isEmpty ?? isContentEmpty(rteText, id)) ? \"translateY(-50%)\" : \"none\"),\n\n            display: \"flex\",\n            gap: \"4px\",\n            zIndex: 1003,\n            alignItems: \"center\"\n          },\n          className: \"rte-action-icons\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: hoveredRTEId === id ? \"none\" : \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              background: \"#ffffff\"\n            },\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              title: translate(\"More Options\"),\n              sx: {\n                width: \"24px\",\n                height: \"24px\",\n                transition: \"all 0.2s ease-in-out\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: grythreedot\n                },\n                style: {\n                  height: '12px',\n                  width: '12px',\n                  display: \"flex\",\n                  alignItems: \"center\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 770,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 750,\n            columnNumber: 37\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: hoveredRTEId === id ? \"flex\" : \"none\",\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              background: \"#ffffff\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: e => {\n                e.stopPropagation();\n                setToolbarVisibleRTEId(toolbarVisibleRTEId === id ? null : id);\n              },\n              title: translate(\"Toggle Toolbar\"),\n              sx: {\n                width: \"24px\",\n                height: \"24px\",\n                \"& span\": {\n                  filter: \"brightness(0) saturate(100%) invert(50%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(50%) contrast(100%)\" // Grey color\n                },\n                \"&:hover\": {\n                  transform: \"scale(1.1)\",\n                  \"& span\": {\n                    filter: \"brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(214deg) brightness(104%) contrast(97%)\" // Blue color\n                  }\n                },\n                transition: \"all 0.2s ease-in-out\",\n                \"& svg\": {\n                  height: \"12px\",\n                  width: \"12px\"\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: editicon\n                },\n                style: {\n                  height: '12px',\n                  width: '12px',\n                  display: \"flex\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 816,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 787,\n              columnNumber: 33\n            }, this), selectedTemplate !== \"Banner\" && selectedTemplateTour === \"Banner\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => handleCloneContainer(item.id),\n                disabled: isCloneDisabled,\n                title: isCloneDisabled ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\"),\n                sx: {\n                  width: \"24px\",\n                  height: \"24px\",\n                  backgroundColor: \"rgba(255, 255, 255, 0.95)\",\n                  \"& span\": {\n                    filter: \"brightness(0) saturate(100%) invert(50%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(50%) contrast(100%)\" // Grey color\n                  },\n                  \"&:hover\": {\n                    \"& span\": {\n                      filter: \"brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(174deg) brightness(104%) contrast(97%)\" // Primary color\n                    },\n                    transform: \"scale(1.1)\"\n                  },\n                  \"&:disabled\": {\n                    opacity: 0.5,\n                    cursor: \"not-allowed\"\n                  },\n                  transition: \"all 0.2s ease-in-out\",\n                  svg: {\n                    height: \"12px\",\n                    width: \"12px\"\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  dangerouslySetInnerHTML: {\n                    __html: copyicon\n                  },\n                  style: {\n                    height: '12px',\n                    width: '12px',\n                    display: \"flex\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 856,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 825,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => handleDeleteSection(item.id, rteId),\n                title: translate(\"Delete Section\"),\n                sx: {\n                  width: \"24px\",\n                  height: \"24px\",\n                  \"& span\": {\n                    filter: \"brightness(0) saturate(100%) invert(50%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(50%) contrast(100%)\" // Grey color\n                  },\n                  \"&:hover\": {\n                    transform: \"scale(1.1)\",\n                    \"& span\": {\n                      filter: \"brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%)\" // Red color\n                    }\n                  },\n                  transition: \"all 0.2s ease-in-out\",\n                  svg: {\n                    height: \"13px\",\n                    width: \"13px\"\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  dangerouslySetInnerHTML: {\n                    __html: deletestep\n                  },\n                  style: {\n                    height: '13px',\n                    width: '13px',\n                    display: \"flex\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 891,\n                  columnNumber: 38\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 866,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 778,\n            columnNumber: 37\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 724,\n          columnNumber: 33\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          id: `rte-${id}`,\n          style: {\n            width: \"100%\",\n            position: \"relative\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"style\", {\n            children: dynamicCSS\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 909,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(JoditEditor, {\n            ref: currentEditorRef,\n            value: rteText,\n            config: config,\n            onChange: newContent => handleUpdate(newContent, rteId, id)\n          }, `rte-stable-${id}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 912,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 905,\n          columnNumber: 29\n        }, this)]\n      }, id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 659,\n        columnNumber: 25\n      }, this);\n    })\n  }, void 0, false);\n}, \"8OMrg6Lt1Wif3mrtdhy8Euu8msw=\", false, function () {\n  return [useTranslation, useDrawerStore];\n})), \"8OMrg6Lt1Wif3mrtdhy8Euu8msw=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c2 = RTEsection;\nexport default RTEsection;\nvar _c, _c2;\n$RefreshReg$(_c, \"RTEsection$forwardRef\");\n$RefreshReg$(_c2, \"RTEsection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "forwardRef", "useCallback", "useMemo", "Box", "IconButton", "JoditEditor", "useDrawerStore", "copyicon", "editicon", "g<PERSON><PERSON><PERSON><PERSON>", "deletestep", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RTEsection", "_s", "_c", "textBoxRef", "isBanner", "handleDeleteRTESection", "index", "guidePopUpRef", "onClone", "isCloneDisabled", "ref", "t", "translate", "rtesContainer", "updateRTEContainer", "setIsUnSavedChanges", "cloneRTEContainer", "clearRteDetails", "selectedTemplate", "selectedTemplateTour", "announcementGuideMetaData", "toolTipGuideMetaData", "handleAnnouncementRTEValue", "handleTooltipRTEValue", "createWithAI", "currentStep", "ensureAnnouncementRTEContainer", "toolbarVisibleRTEId", "setToolbarVisibleRTEId", "editingRTEId", "setEditingRTEId", "hoveredRTEId", "setHoveredRTEId", "contentState", "setContentState", "cursorPositionsRef", "Map", "contentRef", "editor<PERSON><PERSON><PERSON>", "containerRefs", "getEditorRef", "rteId", "current", "has", "set", "createRef", "get", "getContainerRef", "isContentEmpty", "content", "textContent", "replace", "trim", "defaultContent", "includes", "isContentScrollable", "editor<PERSON><PERSON>", "_editor", "workplace", "editor", "scrollHeight", "clientHeight", "error", "updateContentState", "isEmpty", "isScrollable", "prev", "handleClickOutside", "event", "_document$querySelect", "_document$querySelect2", "_document$querySelect3", "_document$querySelect4", "isInsideJoditPopupContent", "target", "closest", "isInsideAltTextPopup", "isInsidePopup", "document", "querySelector", "contains", "isInsideJoditPopup", "isInsideWorkplacePopup", "isSelectionMarker", "id", "startsWith", "isLinkPopup", "isInsideToolbarButton", "isInsertButton", "currentContainerRef", "addEventListener", "removeEventListener", "joditPopupSelectors", "some", "selector", "element", "isJoditElement", "isJoditToolbarButton", "isJoditWorkplace", "selection", "range", "start", "startOffset", "end", "endOffset", "setTimeout", "focus", "savedPosition", "editorDocument", "createRange", "textNode", "<PERSON><PERSON><PERSON><PERSON>", "_textNode$textContent", "_textNode$textContent2", "setStart", "Math", "min", "length", "setEnd", "selectRange", "e", "console", "debug", "handleUpdate", "newContent", "containerId", "isAIAnnouncement", "isAITour", "isTourAnnouncement", "isTourBanner", "isTourTooltip", "log", "substring", "currentStepIndex", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "tooltipContainer", "containers", "find", "container", "type", "_announcementGuideMet", "_announcementGuideMet2", "announcementC<PERSON>r", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "warn", "availableContainers", "map", "c", "handleCloneContainer", "handleDeleteSection", "handlePaste", "preventDefault", "clipboardData", "pastedText", "getData", "pastedHtml", "isRTEContent", "insertContent", "insertHTML", "isRtlDirection", "setIsRtlDirection", "dir", "body", "getAttribute", "toLowerCase", "baseConfigParts", "buttons", "name", "iconURL", "list", "link", "followOnDblClick", "processVideoLink", "processPastedLink", "openInNewTabCheckbox", "noFollowCheckbox", "modeClassName", "dialog", "zIndex", "popup", "controls", "font", "configCacheRef", "getConfigForRTE", "isToolbarVisible", "cached", "lastToolbarState", "config", "newConfig", "readonly", "direction", "language", "placeholder", "toolbarSticky", "toolbarAdaptive", "toolbar", "showCharsCounter", "showWordsCounter", "showXPathInStatusbar", "statusbar", "paste<PERSON>lain", "askBeforePasteHTML", "askBeforePasteFromWord", "height", "minHeight", "maxHeight", "autofocus", "popupRoot", "globalFullSize", "cursorAfterAutofocus", "events", "onPaste", "containersToRender", "_toolTipGuideMetaData7", "filter", "totalContainers", "rteContainers", "rteData", "rteBoxValue", "for<PERSON>ach", "item", "rteText", "_item$rtes", "_item$rtes$", "_item$rtes2", "_item$rtes2$", "rtes", "text", "children", "_item$rtes3", "_item$rtes3$", "_item$rtes4", "_item$rtes4$", "currentEditorRef", "dynamicCSS", "onMouseEnter", "onMouseLeave", "sx", "display", "alignItems", "position", "opacity", "visibility", "fontFamily", "min<PERSON><PERSON><PERSON>", "width", "whiteSpace", "wordBreak", "top", "left", "transform", "max<PERSON><PERSON><PERSON>", "background", "border", "borderRadius", "boxShadow", "className", "style", "right", "gap", "justifyContent", "size", "title", "transition", "dangerouslySetInnerHTML", "__html", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "stopPropagation", "disabled", "backgroundColor", "cursor", "svg", "value", "onChange", "_c2", "$RefreshReg$"], "sources": ["E:/Code/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/RTEsection.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, forwardRef, useCallback, useMemo } from \"react\";\r\nimport { Box, TextField, IconButton } from \"@mui/material\";\r\nimport JoditEditor from \"jodit-react\";\r\nimport useDrawerStore from \"../../../store/drawerStore\";\r\nimport { copyicon, editicon, grythreedot,deletestep} from \"../../../assets/icons/icons\";\r\nimport { selectedtemp } from \"../../drawer/Drawer\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\ninterface RTEsectionProps {\r\n    textBoxRef: React.MutableRefObject<HTMLDivElement | null>;\r\n    guidePopUpRef: React.MutableRefObject<HTMLDivElement | null>;\r\n    isBanner: boolean;\r\n    handleDeleteRTESection: (params: number) => void;\r\n    index: number;\r\n    onClone?: () => void;\r\n    isCloneDisabled?: boolean;\r\n}\r\n\r\nconst RTEsection: React.FC<RTEsectionProps> = forwardRef(\r\n    ({ textBoxRef, isBanner, handleDeleteRTESection, index, guidePopUpRef, onClone, isCloneDisabled }, ref) => {\r\n        const { t: translate } = useTranslation();\r\n        const {\r\n            rtesContainer,\r\n            updateRTEContainer,\r\n            setIsUnSavedChanges,\r\n            cloneRTEContainer,\r\n            clearRteDetails,\r\n            selectedTemplate,\r\n            selectedTemplateTour,\r\n            announcementGuideMetaData,\r\n            toolTipGuideMetaData,\r\n            handleAnnouncementRTEValue,\r\n            handleTooltipRTEValue,\r\n            createWithAI,\r\n            currentStep,\r\n            ensureAnnouncementRTEContainer,\r\n            toolbarVisibleRTEId,\r\n            setToolbarVisibleRTEId\r\n        } = useDrawerStore();\r\n\r\n        // Individual state management for each RTE (excluding toolbarVisibleRTEId which is now global)\r\n        const [editingRTEId, setEditingRTEId] = useState<string | null>(null);\r\n        const [hoveredRTEId, setHoveredRTEId] = useState<string | null>(null);\r\n        const [contentState, setContentState] = useState<{[key: string]: {isEmpty: boolean, isScrollable: boolean}}>({});\r\n\r\n        // Cursor position preservation\r\n        const cursorPositionsRef = useRef<Map<string, { start: number, end: number }>>(new Map());\r\n\r\n\r\n        const contentRef = useRef<string>(\"\");\r\n\r\n\r\n\r\n        // Map to store individual refs for each RTE\r\n        const editorRefs = useRef<Map<string, React.RefObject<any>>>(new Map());\r\n        const containerRefs = useRef<Map<string, React.RefObject<HTMLDivElement>>>(new Map());\r\n\r\n        // Helper function to get or create editor ref for specific RTE\r\n        const getEditorRef = (rteId: string) => {\r\n            if (!editorRefs.current.has(rteId)) {\r\n                editorRefs.current.set(rteId, React.createRef());\r\n            }\r\n            return editorRefs.current.get(rteId);\r\n        };\r\n\r\n        // Helper function to get or create container ref for specific RTE\r\n        const getContainerRef = (rteId: string) => {\r\n            if (!containerRefs.current.has(rteId)) {\r\n                containerRefs.current.set(rteId, React.createRef());\r\n            }\r\n            return containerRefs.current.get(rteId);\r\n        };\r\n\r\n        // Helper functions for content state management\r\n        const isContentEmpty = (content: string, rteId: string) => {\r\n            if (!content) return true;\r\n\r\n            // Remove HTML tags and check if there's actual text content\r\n            const textContent = content.replace(/<[^>]*>/g, '').trim();\r\n            if (!textContent) return true;\r\n\r\n            // Check if it's just default placeholder content\r\n            const defaultContent = ['<p><br></p>', '<p></p>', '<br>', ''];\r\n            if (defaultContent.includes(content.trim())) return true;\r\n\r\n            return false;\r\n        };\r\n\r\n        const isContentScrollable = (rteId: string) => {\r\n            const editorRef = getEditorRef(rteId);\r\n            if (editorRef?.current) {\r\n                try {\r\n                    const workplace = (editorRef.current as any).editor?.workplace;\r\n                    if (workplace) {\r\n                        return workplace.scrollHeight > workplace.clientHeight;\r\n                    }\r\n                } catch (error) {\r\n                    // Silently handle any errors\r\n                }\r\n            }\r\n            return false;\r\n        };\r\n\r\n        // Update content state for dynamic icon positioning\r\n        const updateContentState = React.useCallback((content: string, rteId: string) => {\r\n            const isEmpty = isContentEmpty(content, rteId);\r\n            const isScrollable = isContentScrollable(rteId);\r\n\r\n            setContentState(prev => ({\r\n                ...prev,\r\n                [rteId]: { isEmpty, isScrollable }\r\n            }));\r\n        }, []);\r\n\r\n        // Handle clicks outside the editor - now works with individual RTEs\r\n        useEffect(() => {\r\n            const handleClickOutside = (event: MouseEvent) => {\r\n                if (!toolbarVisibleRTEId) return; // No RTE toolbar is currently visible\r\n\r\n                const isInsideJoditPopupContent = (event.target as HTMLElement).closest(\".jodit-popup__content\") !== null;\r\n                const isInsideAltTextPopup = (event.target as HTMLElement).closest(\".jodit-ui-input\") !== null;\r\n                const isInsidePopup = document.querySelector(\".jodit-popup\")?.contains(event.target as Node);\r\n                const isInsideJoditPopup = document.querySelector(\".jodit-wysiwyg\")?.contains(event.target as Node);\r\n                const isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(\".jodit-dialog__panel\")?.contains(event.target as Node);\r\n                const isSelectionMarker = (event.target as HTMLElement).id.startsWith(\"jodit-selection_marker_\");\r\n                const isLinkPopup = document.querySelector(\".jodit-ui-input__input\")?.contains(event.target as Node);\r\n                const isInsideToolbarButton = (event.target as HTMLElement).closest(\".jodit-toolbar-button__button\") !== null;\r\n                const isInsertButton = (event.target as HTMLElement).closest(\"button[aria-pressed='false']\") !== null;\r\n\r\n                // Get the container ref for the currently visible toolbar RTE\r\n                const currentContainerRef = getContainerRef(toolbarVisibleRTEId);\r\n\r\n                // Check if the target is inside the currently active RTE or related elements\r\n                if (\r\n                    currentContainerRef?.current &&\r\n                    !currentContainerRef.current.contains(event.target as Node) && // Click outside the current editor container\r\n                    !isInsidePopup && // Click outside the popup\r\n                    !isInsideJoditPopup && // Click outside the WYSIWYG editor\r\n                    !isInsideWorkplacePopup && // Click outside the workplace popup\r\n                    !isSelectionMarker && // Click outside selection markers\r\n                    !isLinkPopup && // Click outside link input popup\r\n                    !isInsideToolbarButton && // Click outside the toolbar button\r\n                    !isInsertButton &&\r\n                    !isInsideJoditPopupContent &&\r\n                    !isInsideAltTextPopup\r\n                ) {\r\n                    setEditingRTEId(null); // Close the currently editing RTE\r\n                    setToolbarVisibleRTEId(null); // Also hide toolbar when clicking outside\r\n                }\r\n            };\r\n\r\n            document.addEventListener(\"mousedown\", handleClickOutside);\r\n            return () => document.removeEventListener(\"mousedown\", handleClickOutside);\r\n        }, [toolbarVisibleRTEId]);\r\n\r\n        // Handle clicks outside the editor - enhanced to properly handle Jodit popups\r\n        useEffect(() => {\r\n            const handleClickOutside = (event: MouseEvent) => {\r\n                if (!toolbarVisibleRTEId) return; // No RTE toolbar is currently visible\r\n\r\n                const target = event.target as HTMLElement;\r\n\r\n                // Comprehensive Jodit popup detection\r\n                const joditPopupSelectors = [\r\n                    '.jodit-popup',\r\n                    '.jodit-popup__content',\r\n                    '.jodit-dialog',\r\n                    '.jodit-dialog__panel',\r\n                    '.jodit-dialog__content',\r\n                    '.jodit-ui-input',\r\n                    '.jodit-ui-input__input',\r\n                    '.jodit-ui-button',\r\n                    '.jodit-toolbar-button',\r\n                    '.jodit-toolbar-button__button',\r\n                    '.jodit-color-picker',\r\n                    '.jodit-color-picker__palette',\r\n                    '.jodit-filebrowser',\r\n                    '.jodit-filebrowser__dialog',\r\n                    '.jodit-link-dialog',\r\n                    '.jodit-image-dialog',\r\n                    '.jodit-table-dialog',\r\n                    '.jodit-form',\r\n                    '.jodit-form__group',\r\n                    '.jodit-dropdown',\r\n                    '.jodit-dropdown__content',\r\n                    '.jodit-list',\r\n                    '.jodit-list__item'\r\n                ];\r\n\r\n                // Check if click is inside any Jodit popup/dialog\r\n                const isInsideJoditPopup = joditPopupSelectors.some(selector => {\r\n                    const element = document.querySelector(selector);\r\n                    return element && element.contains(target);\r\n                });\r\n\r\n                // Additional checks for dynamic Jodit elements\r\n                const isJoditElement = target.closest('.jodit-popup, .jodit-dialog, .jodit-ui-input, .jodit-color-picker, .jodit-filebrowser, .jodit-dropdown, .jodit-form') !== null;\r\n                const isJoditToolbarButton = target.closest('.jodit-toolbar-button') !== null;\r\n                const isSelectionMarker = target.id && target.id.startsWith(\"jodit-selection_marker_\");\r\n                const isJoditWorkplace = target.closest('.jodit-wysiwyg, .jodit-workplace') !== null;\r\n\r\n                // Get the container ref for the currently visible toolbar RTE\r\n                const currentContainerRef = getContainerRef(toolbarVisibleRTEId);\r\n\r\n                // Only close toolbar if click is truly outside all RTE and Jodit elements\r\n                if (\r\n                    currentContainerRef?.current &&\r\n                    !currentContainerRef.current.contains(target) && // Click outside the current editor container\r\n                    !isInsideJoditPopup && // Click outside any Jodit popup\r\n                    !isJoditElement && // Click outside any Jodit element\r\n                    !isJoditToolbarButton && // Click outside toolbar buttons\r\n                    !isSelectionMarker && // Click outside selection markers\r\n                    !isJoditWorkplace // Click outside the editor workplace\r\n                ) {\r\n                    setEditingRTEId(null); // Close the currently editing RTE\r\n                    setToolbarVisibleRTEId(null); // Also hide toolbar when clicking outside\r\n                }\r\n            };\r\n\r\n            document.addEventListener(\"mousedown\", handleClickOutside);\r\n            return () => document.removeEventListener(\"mousedown\", handleClickOutside);\r\n        }, [toolbarVisibleRTEId]);\r\n\r\n        // Preserve cursor position when toolbar visibility changes\r\n        useEffect(() => {\r\n            if (toolbarVisibleRTEId) {\r\n                const editorRef = getEditorRef(toolbarVisibleRTEId);\r\n                if (editorRef?.current) {\r\n                    const editor = (editorRef.current as any).editor;\r\n                    if (editor) {\r\n                        // Save current cursor position before any changes\r\n                        const selection = editor.selection;\r\n                        if (selection) {\r\n                            const range = selection.range;\r\n                            if (range) {\r\n                                cursorPositionsRef.current.set(toolbarVisibleRTEId, {\r\n                                    start: range.startOffset,\r\n                                    end: range.endOffset\r\n                                });\r\n                            }\r\n                        }\r\n\r\n                        // Focus the editor after toolbar shows\r\n                        setTimeout(() => {\r\n                            editor.focus();\r\n\r\n                            // Restore cursor position if available\r\n                            const savedPosition = cursorPositionsRef.current.get(toolbarVisibleRTEId);\r\n                            if (savedPosition && selection) {\r\n                                try {\r\n                                    const range = editor.editorDocument.createRange();\r\n                                    const textNode = editor.editor.firstChild;\r\n                                    if (textNode) {\r\n                                        range.setStart(textNode, Math.min(savedPosition.start, textNode.textContent?.length || 0));\r\n                                        range.setEnd(textNode, Math.min(savedPosition.end, textNode.textContent?.length || 0));\r\n                                        selection.selectRange(range);\r\n                                    }\r\n                                } catch (e) {\r\n                                    // Fallback: just focus without setting position\r\n                                    console.debug('Cursor position restoration failed, using fallback');\r\n                                }\r\n                            }\r\n                        }, 100);\r\n                    }\r\n                }\r\n            }\r\n        }, [toolbarVisibleRTEId]);\r\n\r\n\r\n\r\n        const handleUpdate = useCallback((newContent: string, rteId: string, containerId: string) => {\r\n            contentRef.current = newContent;\r\n\r\n            // Check if this is an AI-created guide\r\n            const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n            const isAITour = createWithAI && selectedTemplate === \"Tour\";\r\n            const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\r\n            const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\r\n            const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\r\n\r\n            console.log(\"RTEsection handleUpdate:\", {\r\n                createWithAI,\r\n                selectedTemplate,\r\n                selectedTemplateTour,\r\n                isAIAnnouncement,\r\n                isAITour,\r\n                isTourBanner,\r\n                containerId,\r\n                newContent: newContent.substring(0, 50) + \"...\"\r\n            });\r\n\r\n            if (isAIAnnouncement) {\r\n                const currentStepIndex = currentStep - 1;\r\n\r\n                if (isTourAnnouncement) {\r\n                    // For Tour+Announcement, use toolTipGuideMetaData\r\n                    const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(\r\n                        (container: any) => container.id === containerId && container.type === \"rte\"\r\n                    );\r\n\r\n                    if (tooltipContainer) {\r\n                        // Use the tooltip-specific handler for tour announcements\r\n                        handleTooltipRTEValue(containerId, newContent);\r\n                    }\r\n                } else {\r\n                    // For pure Announcements, use announcementGuideMetaData\r\n                    const announcementContainer = announcementGuideMetaData[currentStepIndex]?.containers?.find(\r\n                        (container: any) => container.id === containerId && container.type === \"rte\"\r\n                    );\r\n\r\n                    if (announcementContainer) {\r\n                        // Use the announcement-specific handler\r\n                        handleAnnouncementRTEValue(containerId, newContent);\r\n                    }\r\n                }\r\n            } else if (isAITour && (isTourBanner || isTourTooltip)) {\r\n                // For AI Tour with Banner, Tooltip, or Hotspot steps, use toolTipGuideMetaData\r\n                const currentStepIndex = currentStep - 1;\r\n                const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(\r\n                    (container: any) => container.id === containerId && container.type === \"rte\"\r\n                );\r\n\r\n                if (tooltipContainer) {\r\n                    // Use the tooltip-specific handler for all tour step types\r\n                    handleTooltipRTEValue(containerId, newContent);\r\n                    console.log(`Used handleTooltipRTEValue for ${selectedTemplateTour} step in AI tour`);\r\n                } else {\r\n                    console.warn(`No tooltip container found for ${selectedTemplateTour} step`, {\r\n                        currentStepIndex,\r\n                        containerId,\r\n                        availableContainers: toolTipGuideMetaData[currentStepIndex]?.containers?.map(c => ({ id: c.id, type: c.type }))\r\n                    });\r\n                }\r\n            } else {\r\n                // For non-AI content or other cases, use the regular RTE container system\r\n                updateRTEContainer(containerId, rteId, newContent);\r\n                console.log(\"Used updateRTEContainer for non-AI content\");\r\n            }\r\n\r\n            setIsUnSavedChanges(true);\r\n\r\n            // Update content state for dynamic icon positioning\r\n            updateContentState(newContent, rteId);\r\n        }, [createWithAI, selectedTemplate, selectedTemplateTour, currentStep, toolTipGuideMetaData, announcementGuideMetaData, handleTooltipRTEValue, handleAnnouncementRTEValue, updateRTEContainer, setIsUnSavedChanges, updateContentState]);\r\n        const handleCloneContainer = (containerId: string) => {\r\n            // Check if cloning is disabled due to section limits\r\n            if (isCloneDisabled) {\r\n                return; // Don't clone if limit is reached\r\n            }\r\n\r\n            // Call the clone function from the store\r\n            cloneRTEContainer(containerId);\r\n\r\n            // Call the onClone callback if provided\r\n            if (onClone) {\r\n                onClone();\r\n            }\r\n        };\r\n        const handleDeleteSection = (containerId: string, rteId:string) => {\r\n            // Check if this is an AI-created announcement\r\n            const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n\r\n            if (isAIAnnouncement) {\r\n                // For AI announcements, we need to remove from announcementGuideMetaData\r\n                // This would require a new function in the store, for now just call the existing one\r\n                clearRteDetails(containerId, rteId);\r\n            } else {\r\n                // For banners and non-AI content, use the regular clear function\r\n                clearRteDetails(containerId, rteId);\r\n            }\r\n\r\n            // Call the handleDeleteRTESection callback to update section counts\r\n            handleDeleteRTESection(index);\r\n        };\r\n        const handlePaste = (event: React.ClipboardEvent<HTMLDivElement>) => {\r\n            event.preventDefault();\r\n\r\n            const clipboardData = event.clipboardData;\r\n            const pastedText = clipboardData.getData(\"text/plain\");\r\n            const pastedHtml = clipboardData.getData(\"text/html\");\r\n\r\n            if (pastedHtml) {\r\n                const isRTEContent = pastedHtml.includes(\"<!--RTE-->\");\r\n                if (isRTEContent) {\r\n                    insertContent(pastedHtml);\r\n                } else {\r\n                    insertContent(pastedHtml);\r\n                }\r\n            } else {\r\n                insertContent(pastedText);\r\n            }\r\n        };\r\n\r\n\r\n        const insertContent = (content: string) => {\r\n            if (toolbarVisibleRTEId) {\r\n                const editorRef = getEditorRef(toolbarVisibleRTEId);\r\n                if (editorRef?.current) {\r\n                    const editor = (editorRef.current as any).editor;\r\n                    editor.selection.insertHTML(content);\r\n                }\r\n            }\r\n        };\r\n\r\n\r\n        const [isRtlDirection, setIsRtlDirection] = useState<boolean>(false);\r\n        useEffect(() => {\r\n    const dir = document.body.getAttribute(\"dir\") || \"ltr\";\r\n    setIsRtlDirection(dir.toLowerCase() === \"rtl\");\r\n}, []);\r\n\r\n        // Stable base config parts that don't change - memoized once\r\n        const baseConfigParts = useMemo(() => ({\r\n            buttons: [\r\n                'bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush',\r\n                'font', 'fontsize', 'link',\r\n                {\r\n                    name: 'more',\r\n                    iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\r\n                    list: [\r\n                        'source',\r\n                        'image', 'video', 'table',\r\n                        'align', 'undo', 'redo', '|',\r\n                        'hr', 'eraser', 'copyformat',\r\n                        'symbol', 'fullsize', 'print', 'superscript', 'subscript', '|',\r\n                        'outdent', 'indent', 'paragraph',\r\n                    ]\r\n                }\r\n            ],\r\n            link: {\r\n                followOnDblClick: false,\r\n                processVideoLink: true,\r\n                processPastedLink: true,\r\n                openInNewTabCheckbox: true,\r\n                noFollowCheckbox: false,\r\n                modeClassName: 'input',\r\n            },\r\n            dialog: {\r\n                zIndex: 100001,\r\n            },\r\n            popup: {\r\n                zIndex: 100002,\r\n            },\r\n            controls: {\r\n                font: {\r\n                    list: {\r\n                        \"Poppins, sans-serif\": \"Poppins\",\r\n                        \"Roboto, sans-serif\": \"Roboto\",\r\n                        \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\r\n                        \"Open Sans, sans-serif\": \"Open Sans\",\r\n                        \"Calibri, sans-serif\": \"Calibri\",\r\n                        \"Century Gothic, sans-serif\": \"Century Gothic\",\r\n                    }\r\n                }\r\n            }\r\n        }), []);\r\n\r\n        // Per-RTE config cache to minimize recreation\r\n        const configCacheRef = useRef<Map<string, { config: any, lastToolbarState: boolean }>>(new Map());\r\n\r\n        // Smart config creation - only recreate when toolbar state actually changes\r\n        const getConfigForRTE = useCallback((rteId: string): any => {\r\n            const isToolbarVisible = toolbarVisibleRTEId === rteId;\r\n            const cached = configCacheRef.current.get(rteId);\r\n\r\n            // If we have a cached config and toolbar state hasn't changed, reuse it\r\n            if (cached && cached.lastToolbarState === isToolbarVisible) {\r\n                return cached.config;\r\n            }\r\n\r\n            // Create new config only when toolbar state changes\r\n            const newConfig = {\r\n                readonly: false,\r\n                direction: isRtlDirection ? 'rtl' : 'ltr',\r\n                language: 'en',\r\n                placeholder: 'Enter your text here',\r\n                toolbarSticky: false,\r\n                toolbarAdaptive: false,\r\n                // Dynamic toolbar visibility - this triggers the display change\r\n                toolbar: isToolbarVisible,\r\n                showCharsCounter: false,\r\n                showWordsCounter: false,\r\n                showXPathInStatusbar: false,\r\n                statusbar: false,\r\n                pastePlain: true,\r\n                askBeforePasteHTML: false,\r\n                askBeforePasteFromWord: false,\r\n                height: 'auto',\r\n                // Dynamic height based on toolbar visibility\r\n                minHeight: isToolbarVisible ? 150 : 28,\r\n                maxHeight: 180,\r\n                autofocus: false,\r\n                popupRoot: document.body,\r\n                zIndex: 100000,\r\n                globalFullSize: false,\r\n                cursorAfterAutofocus: 'end',\r\n                events: {\r\n                    onPaste: handlePaste,\r\n                },\r\n                // Spread stable parts to minimize object creation\r\n                ...baseConfigParts\r\n            };\r\n\r\n            // Cache the new config with its toolbar state\r\n            configCacheRef.current.set(rteId, {\r\n                config: newConfig,\r\n                lastToolbarState: isToolbarVisible\r\n            });\r\n\r\n            return newConfig;\r\n        }, [isRtlDirection, handlePaste, baseConfigParts, toolbarVisibleRTEId]);\r\n\r\n        // Determine which containers to use based on guide type\r\n        const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n        const isAITour = createWithAI && selectedTemplate === \"Tour\";\r\n        const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\r\n        const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\r\n        const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\r\n        const currentStepIndex = currentStep - 1;\r\n\r\n        let containersToRender: any[] = [];\r\n\r\n        if (isAIAnnouncement && !isTourAnnouncement) {\r\n            // For pure AI announcements (not in tours), use announcementGuideMetaData\r\n            containersToRender = ensureAnnouncementRTEContainer(currentStepIndex, false);\r\n        } else if (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\r\n            // For AI Tour with any step type (Banner, Tooltip, Hotspot, or Announcement), use toolTipGuideMetaData\r\n            if (toolTipGuideMetaData[currentStepIndex]?.containers) {\r\n                containersToRender = toolTipGuideMetaData[currentStepIndex].containers.filter(c => c.type === \"rte\");\r\n                console.log(`RTEsection: Using toolTipGuideMetaData containers for ${selectedTemplateTour} step ${currentStepIndex}:`, {\r\n                    totalContainers: toolTipGuideMetaData[currentStepIndex].containers.length,\r\n                    rteContainers: containersToRender.length,\r\n                    rteData: containersToRender.map(c => ({ id: c.id, rteBoxValue: c.rteBoxValue }))\r\n                });\r\n            } else {\r\n                console.warn(`RTEsection: No toolTipGuideMetaData found for ${selectedTemplateTour} step ${currentStepIndex}`);\r\n                containersToRender = [];\r\n            }\r\n        } else {\r\n            // For non-AI content, use rtesContainer\r\n            containersToRender = rtesContainer;\r\n        }\r\n\r\n        // Initialize content state for all RTEs\r\n        React.useEffect(() => {\r\n            containersToRender.forEach((item: any) => {\r\n                let rteText = \"\";\r\n                let rteId = \"\";\r\n                let id = \"\";\r\n\r\n                if ((isAIAnnouncement && !isTourAnnouncement) || (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement))) {\r\n                    rteText = item.rteBoxValue || \"\";\r\n                    rteId = item.id;\r\n                    id = item.id;\r\n                } else {\r\n                    rteText = item.rtes?.[0]?.text || \"\";\r\n                    rteId = item.rtes?.[0]?.id;\r\n                    id = item.id;\r\n                }\r\n\r\n                if (id && rteId) {\r\n                    updateContentState(rteText, rteId);\r\n                }\r\n            });\r\n        }, [containersToRender, isAIAnnouncement, isTourAnnouncement, isAITour, isTourBanner, isTourTooltip, updateContentState]);\r\n\r\n        return (\r\n            <>\r\n                {containersToRender.map((item: any) => {\r\n                    let rteText = \"\";\r\n                    let rteId = \"\";\r\n                    let id = \"\";\r\n\r\n                    if ((isAIAnnouncement && !isTourAnnouncement) || (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement))) {\r\n                        // For AI announcements and AI tour steps (banner, tooltip, hotspot, announcement), get data from container\r\n                        // Both announcementGuideMetaData and toolTipGuideMetaData use the same structure for RTE containers\r\n                        rteText = item.rteBoxValue || \"\";\r\n                        rteId = item.id;\r\n                        id = item.id;\r\n                    } else {\r\n                        // For non-AI content, get data from rtesContainer\r\n                        rteText = item.rtes?.[0]?.text || \"\";\r\n                        rteId = item.rtes?.[0]?.id;\r\n                        id = item.id;\r\n                    }\r\n\r\n                    if (!id) return null;\r\n\r\n\r\n                    const currentContainerRef = getContainerRef(id);\r\n                    const currentEditorRef = getEditorRef(id);\r\n\r\n                    // Get config for this specific RTE instance\r\n                    const config = getConfigForRTE(id);\r\n\r\n                    // Dynamic CSS for this specific RTE to prevent scrollbar conflicts and fix popup z-index\r\n                    const dynamicCSS = `\r\n                        /* Ensure Jodit popups appear above all other elements */\r\n                        .jodit-popup,\r\n                        .jodit-dialog,\r\n                        .jodit-color-picker,\r\n                        .jodit-filebrowser,\r\n                        .jodit-dropdown {\r\n                            z-index: 100002 !important;\r\n                        }\r\n\r\n                        .jodit-popup__content,\r\n                        .jodit-dialog__content,\r\n                        .jodit-ui-input,\r\n                        .jodit-form {\r\n                            z-index: 100003 !important;\r\n                        }\r\n                        /* Hide the add new line button */\r\n                        .jodit-add-new-line {\r\n                            display: none !important;\r\n                        }\r\n                        /* Scoped styles for RTE ${id} */\r\n                        #rte-${id} .jodit-wysiwyg {\r\n                            color: #000000 !important;\r\n                            line-height: 1.4 !important;\r\n                            padding: 8px !important;\r\n                        }\r\n                        /* Height and scrolling behavior - scoped to this specific RTE */\r\n                        #rte-${id} .jodit-workplace {\r\n                            min-height: ${toolbarVisibleRTEId === id ? '90px' : '28px'} !important;\r\n                            max-height: 180px !important;\r\n                            overflow-y: auto !important;\r\n                            line-height: 1.4 !important;\r\n                            background-color: ${toolbarVisibleRTEId === id ? '#ffffff !important' : 'inherit'};\r\n                        }\r\n                        #rte-${id} .jodit-container {\r\n                            border: none !important;\r\n                            background-color: ${toolbarVisibleRTEId === id ? '#ffffff !important' : 'inherit'};\r\n                        }\r\n                        /* Enhanced scrollbar styling */\r\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar {\r\n                            width: 6px !important;\r\n                        }\r\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar-track {\r\n                            background: #f1f1f1 !important;\r\n                            border-radius: 3px !important;\r\n                        }\r\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar-thumb {\r\n                            background: #c1c1c1 !important;\r\n                            border-radius: 3px !important;\r\n                        }\r\n                        #rte-${id} .jodit-workplace::-webkit-scrollbar-thumb:hover {\r\n                            background: #a8a8a8 !important;\r\n                        }\r\n                            \t#rte-${id} .jodit-wysiwyg p {\r\n                            color: #000000 !important;\r\n                            margin: 0 0 4px 0 !important;\r\n                            padding: 0 !important;\r\n                            line-height: 1.4 !important;\r\n\t\t\t}\r\n                    `;\r\n\r\n                    return (\r\n                        <Box\r\n                            key={id}\r\n                            ref={currentContainerRef}\r\n                            onMouseEnter={() => setHoveredRTEId(id)}\r\n                            onMouseLeave={() => setHoveredRTEId(null)}\r\n                            sx={{\r\n                                display: \"flex\",\r\n                                alignItems: \"center\",\r\n                                position: \"relative\",\r\n                                \"&:hover .rte-action-icons\": {\r\n                                    opacity: \"1 !important\",\r\n                                    visibility: \"visible !important\"\r\n                                },\r\n                                \"&.qadpt-rte:hover .rte-action-icons\": {\r\n                                    opacity: \"1 !important\",\r\n                                    visibility: \"visible !important\"\r\n                                },\r\n                                \"& .jodit-status-bar-link\": {\r\n                                    display: \"none !important\",\r\n                                },\r\n                                \"& .jodit-editor\": {\r\n                                    fontFamily: \"'Roboto', sans-serif !important\",\r\n                                },\r\n                                \".jodit-editor span\": {\r\n                                    fontFamily: \"'Roboto', sans-serif !important\",\r\n                                },\r\n                                \".jodit-toolbar-button button\": {\r\n                                    minWidth: \"29px !important\",\r\n                                },\r\n                                \".jodit-react-container\": {\r\n                                    width: \"100%\",\r\n                                    whiteSpace: \"pre-wrap\",\r\n                                    wordBreak: \"break-word\",\r\n                                },\r\n                                // Fix Jodit dialog positioning - target correct classes\r\n                                \".jodit.jodit-dialog\": {\r\n                                    position: \"fixed !important\",\r\n                                    zIndex: \"100001 !important\",\r\n                                    top: \"50% !important\",\r\n                                    left: \"50% !important\",\r\n                                    transform: \"translate(-50%, -50%) !important\"\r\n                                },\r\n                                \".jodit-dialog .jodit-dialog__panel\": {\r\n                                    position: \"relative !important\",\r\n                                    top: \"auto !important\",\r\n                                    left: \"auto !important\",\r\n                                    transform: \"none !important\",\r\n                                    maxWidth: \"400px !important\",\r\n                                    background: \"white !important\",\r\n                                    border: \"1px solid #ccc !important\",\r\n                                    borderRadius: \"4px !important\",\r\n                                    boxShadow: \"0 4px 12px rgba(0,0,0,0.15) !important\"\r\n                                },\r\n                                // Fix for link dialog specifically\r\n                                \".jodit-dialog_alert\": {\r\n                                    position: \"fixed !important\",\r\n                                    zIndex: \"100001 !important\",\r\n                                    top: \"50% !important\",\r\n                                    left: \"50% !important\",\r\n                                    transform: \"translate(-50%, -50%) !important\"\r\n                                }\r\n                            }}\r\n                            className=\"qadpt-rte\"\r\n                        >\r\n                            {/* Three-dot menu and action icons - Only show for non-Banner templates */}\r\n                                <div\r\n                                    style={{\r\n                                        position: \"absolute\",\r\n                                        // Complex positioning logic:\r\n                                        // 1. When toolbar visible: top-right of jodit-workplace (below toolbar)\r\n                                        // 2. When no content: positioned well to the left of edit icon to avoid overlap\r\n                                        // 3. When has content: top-right corner\r\n                                        top:\r\n                                                toolbarVisibleRTEId === id\r\n                                                ? \"43px\"\r\n                                                : selectedTemplate === \"Banner\"\r\n                                                    ? \"7px\"\r\n                                                    : \"4px\",\r\n                                        right:  \"15px\" ,\r\n                                        left:  \"auto\" ,\r\n                                        // transform: toolbarVisibleRTEId === id ? \"none\" : ((contentState[id]?.isEmpty ?? isContentEmpty(rteText, id)) ? \"translateY(-50%)\" : \"none\"),\r\n                                        \r\n                                        display: \"flex\",\r\n                                        gap: \"4px\",\r\n                                        zIndex: 1003,\r\n                                        alignItems: \"center\",\r\n                                       \r\n                                    }}\r\n                                    className=\"rte-action-icons\"\r\n                                >\r\n                                    {/* Three-dot menu - shown by default */}\r\n                                    <div\r\n                                        style={{\r\n                                            display: hoveredRTEId === id ? \"none\" : \"flex\",\r\n                                            alignItems: \"center\",\r\n                                        justifyContent: \"center\",\r\n                                        background: \"#ffffff\"\r\n\r\n                                        }}\r\n                                    >\r\n                                        <IconButton\r\n                                            size=\"small\"\r\n                                            title={translate(\"More Options\")}\r\n                                            sx={{\r\n                                                width: \"24px\",\r\n                                                height: \"24px\",\r\n                                               \r\n                                              \r\n                                                transition: \"all 0.2s ease-in-out\",\r\n                                            }}\r\n                                        >\r\n                                            <span\r\n                                                dangerouslySetInnerHTML={{ __html: grythreedot }}\r\n                                                style={{ height: '12px', width: '12px',display : \"flex\" ,alignItems : \"center\"}}\r\n                                            />\r\n                                        </IconButton>\r\n                                    </div>\r\n\r\n                                    {/* Individual action icons - shown on hover */}\r\n                                    <div\r\n                                        style={{\r\n                                            display: hoveredRTEId === id ? \"flex\" : \"none\",\r\n                                            alignItems: \"center\",\r\n                                            justifyContent: \"center\",\r\n                                            background: \"#ffffff\"\r\n                                        }}\r\n                                    >\r\n                                {/* Edit Icon */}\r\n                                <IconButton\r\n                                    size=\"small\"\r\n                                    onClick={(e) => {\r\n                                        e.stopPropagation();\r\n                                        setToolbarVisibleRTEId(toolbarVisibleRTEId === id ? null : id);\r\n                                    }}\r\n                                    title={translate(\"Toggle Toolbar\")}\r\n                                    sx={{\r\n                                        width: \"24px\",\r\n                                        height: \"24px\",\r\n                                      \r\n                                        \"& span\": {\r\n                                            filter: \"brightness(0) saturate(100%) invert(50%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(50%) contrast(100%)\", // Grey color\r\n                                        },\r\n                                        \"&:hover\": {\r\n                                          \r\n                                            \r\n                                            transform: \"scale(1.1)\",\r\n                                            \"& span\": {\r\n                                                filter: \"brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(214deg) brightness(104%) contrast(97%)\", // Blue color\r\n                                            }\r\n                                        },\r\n                                        transition: \"all 0.2s ease-in-out\",\r\n                                        \"& svg\": {\r\n                                            height: \"12px\",\r\n                                            width: \"12px\",\r\n                                        }\r\n                                    }}\r\n                                >\r\n                                    <span\r\n                                        dangerouslySetInnerHTML={{ __html: editicon }}\r\n                                        style={{ height: '12px', width: '12px' ,display : \"flex\"}}\r\n                                    />\r\n                                </IconButton>\r\n\r\n                                {/* Clone Icon */}\r\n                                    {selectedTemplate !== \"Banner\" && selectedTemplateTour === \"Banner\" && (\r\n                                        <>\r\n                                <IconButton\r\n                                    size=\"small\"\r\n                                    onClick={() => handleCloneContainer(item.id)}\r\n                                    disabled={isCloneDisabled}\r\n                                    title={isCloneDisabled ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\")}\r\n                                    sx={{\r\n                                        width: \"24px\",\r\n                                        height: \"24px\",\r\n                                        backgroundColor: \"rgba(255, 255, 255, 0.95)\",\r\n                                        \"& span\": {\r\n                                            filter: \"brightness(0) saturate(100%) invert(50%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(50%) contrast(100%)\", // Grey color\r\n                                        },\r\n                                        \"&:hover\": {\r\n                                          \r\n                                           \r\n                                            \"& span\": {\r\n                                                filter: \"brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(174deg) brightness(104%) contrast(97%)\", // Primary color\r\n                                            },\r\n                                             transform: \"scale(1.1)\",\r\n                                        },\r\n                                        \"&:disabled\": {\r\n                                            opacity: 0.5,\r\n                                            cursor: \"not-allowed\"\r\n                                        },\r\n                                        transition: \"all 0.2s ease-in-out\",\r\n                                        svg: {\r\n                                            height: \"12px\",\r\n                                            width: \"12px\",\r\n                                        },\r\n                                    }}\r\n                                >\r\n                                    <span\r\n                                        dangerouslySetInnerHTML={{ __html: copyicon }}\r\n                                        style={{\r\n                                            height: '12px',\r\n                                            width: '12px',display : \"flex\"\r\n                                        }}\r\n                                    />\r\n                                </IconButton>\r\n\r\n                                {/* Delete Icon */}\r\n                                <IconButton\r\n                                    size=\"small\"\r\n                                    onClick={() => handleDeleteSection(item.id, rteId)}\r\n                                    title={translate(\"Delete Section\")}\r\n                                    sx={{\r\n                                        width: \"24px\",\r\n                                        height: \"24px\",\r\n                                       \r\n                                        \"& span\": {\r\n                                            filter: \"brightness(0) saturate(100%) invert(50%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(50%) contrast(100%)\", // Grey color\r\n                                        },\r\n                                        \"&:hover\": {\r\n                                            \r\n                                            transform: \"scale(1.1)\", \r\n                                            \"& span\": {\r\n                                                filter: \"brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%)\", // Red color\r\n                                            }\r\n                                        },\r\n                                        transition: \"all 0.2s ease-in-out\",\r\n                                        svg: {\r\n                                            height: \"13px\",\r\n                                            width: \"13px\",\r\n                                        },\r\n                                    }}\r\n                                >\r\n                                     <span\r\n                                        dangerouslySetInnerHTML={{ __html: deletestep }}\r\n                                        style={{\r\n                                            height: '13px',\r\n                                            width: '13px',display : \"flex\"\r\n                                        }}\r\n                                    />\r\n                                    </IconButton>\r\n                                    </>\r\n                                    )}\r\n                            </div>\r\n                        </div>\r\n\r\n                            {/* Jodit Editor Container */}\r\n                            <div\r\n                                id={`rte-${id}`}\r\n                                style={{ width: \"100%\", position: \"relative\" }}\r\n                            >\r\n                                <style>\r\n                                    {dynamicCSS}\r\n                                </style>\r\n                                <JoditEditor\r\n                                    key={`rte-stable-${id}`}\r\n                                    ref={currentEditorRef}\r\n                                    value={rteText}\r\n                                    config={config}\r\n                                    onChange={(newContent) => handleUpdate(newContent, rteId, id)}\r\n                                />\r\n\r\n                            </div>\r\n                        </Box>\r\n                    );\r\n                })}\r\n            </>\r\n        );\r\n    }\r\n);\r\n\r\nexport default RTEsection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AAC5F,SAASC,GAAG,EAAaC,UAAU,QAAQ,eAAe;AAC1D,OAAOC,WAAW,MAAM,aAAa;AACrC,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAACC,UAAU,QAAO,6BAA6B;AAEvF,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAY/C,MAAMC,UAAqC,gBAAAC,EAAA,cAAGjB,UAAU,CAAAkB,EAAA,GAAAD,EAAA,CACpD,CAAC;EAAEE,UAAU;EAAEC,QAAQ;EAAEC,sBAAsB;EAAEC,KAAK;EAAEC,aAAa;EAAEC,OAAO;EAAEC;AAAgB,CAAC,EAAEC,GAAG,KAAK;EAAAT,EAAA;EACvG,MAAM;IAAEU,CAAC,EAAEC;EAAU,CAAC,GAAGjB,cAAc,CAAC,CAAC;EACzC,MAAM;IACFkB,aAAa;IACbC,kBAAkB;IAClBC,mBAAmB;IACnBC,iBAAiB;IACjBC,eAAe;IACfC,gBAAgB;IAChBC,oBAAoB;IACpBC,yBAAyB;IACzBC,oBAAoB;IACpBC,0BAA0B;IAC1BC,qBAAqB;IACrBC,YAAY;IACZC,WAAW;IACXC,8BAA8B;IAC9BC,mBAAmB;IACnBC;EACJ,CAAC,GAAGtC,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAA6D,CAAC,CAAC,CAAC;;EAEhH;EACA,MAAMsD,kBAAkB,GAAGpD,MAAM,CAA8C,IAAIqD,GAAG,CAAC,CAAC,CAAC;EAGzF,MAAMC,UAAU,GAAGtD,MAAM,CAAS,EAAE,CAAC;;EAIrC;EACA,MAAMuD,UAAU,GAAGvD,MAAM,CAAoC,IAAIqD,GAAG,CAAC,CAAC,CAAC;EACvE,MAAMG,aAAa,GAAGxD,MAAM,CAA+C,IAAIqD,GAAG,CAAC,CAAC,CAAC;;EAErF;EACA,MAAMI,YAAY,GAAIC,KAAa,IAAK;IACpC,IAAI,CAACH,UAAU,CAACI,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAE;MAChCH,UAAU,CAACI,OAAO,CAACE,GAAG,CAACH,KAAK,eAAE7D,KAAK,CAACiE,SAAS,CAAC,CAAC,CAAC;IACpD;IACA,OAAOP,UAAU,CAACI,OAAO,CAACI,GAAG,CAACL,KAAK,CAAC;EACxC,CAAC;;EAED;EACA,MAAMM,eAAe,GAAIN,KAAa,IAAK;IACvC,IAAI,CAACF,aAAa,CAACG,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAE;MACnCF,aAAa,CAACG,OAAO,CAACE,GAAG,CAACH,KAAK,eAAE7D,KAAK,CAACiE,SAAS,CAAC,CAAC,CAAC;IACvD;IACA,OAAON,aAAa,CAACG,OAAO,CAACI,GAAG,CAACL,KAAK,CAAC;EAC3C,CAAC;;EAED;EACA,MAAMO,cAAc,GAAGA,CAACC,OAAe,EAAER,KAAa,KAAK;IACvD,IAAI,CAACQ,OAAO,EAAE,OAAO,IAAI;;IAEzB;IACA,MAAMC,WAAW,GAAGD,OAAO,CAACE,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC;IAC1D,IAAI,CAACF,WAAW,EAAE,OAAO,IAAI;;IAE7B;IACA,MAAMG,cAAc,GAAG,CAAC,aAAa,EAAE,SAAS,EAAE,MAAM,EAAE,EAAE,CAAC;IAC7D,IAAIA,cAAc,CAACC,QAAQ,CAACL,OAAO,CAACG,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI;IAExD,OAAO,KAAK;EAChB,CAAC;EAED,MAAMG,mBAAmB,GAAId,KAAa,IAAK;IAC3C,MAAMe,SAAS,GAAGhB,YAAY,CAACC,KAAK,CAAC;IACrC,IAAIe,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEd,OAAO,EAAE;MACpB,IAAI;QAAA,IAAAe,OAAA;QACA,MAAMC,SAAS,IAAAD,OAAA,GAAID,SAAS,CAACd,OAAO,CAASiB,MAAM,cAAAF,OAAA,uBAAjCA,OAAA,CAAmCC,SAAS;QAC9D,IAAIA,SAAS,EAAE;UACX,OAAOA,SAAS,CAACE,YAAY,GAAGF,SAAS,CAACG,YAAY;QAC1D;MACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;QACZ;MAAA;IAER;IACA,OAAO,KAAK;EAChB,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGnF,KAAK,CAACK,WAAW,CAAC,CAACgE,OAAe,EAAER,KAAa,KAAK;IAC7E,MAAMuB,OAAO,GAAGhB,cAAc,CAACC,OAAO,EAAER,KAAK,CAAC;IAC9C,MAAMwB,YAAY,GAAGV,mBAAmB,CAACd,KAAK,CAAC;IAE/CP,eAAe,CAACgC,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACzB,KAAK,GAAG;QAAEuB,OAAO;QAAEC;MAAa;IACrC,CAAC,CAAC,CAAC;EACP,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnF,SAAS,CAAC,MAAM;IACZ,MAAMqF,kBAAkB,GAAIC,KAAiB,IAAK;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MAC9C,IAAI,CAAC7C,mBAAmB,EAAE,OAAO,CAAC;;MAElC,MAAM8C,yBAAyB,GAAIL,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,uBAAuB,CAAC,KAAK,IAAI;MACzG,MAAMC,oBAAoB,GAAIR,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,iBAAiB,CAAC,KAAK,IAAI;MAC9F,MAAME,aAAa,IAAAR,qBAAA,GAAGS,QAAQ,CAACC,aAAa,CAAC,cAAc,CAAC,cAAAV,qBAAA,uBAAtCA,qBAAA,CAAwCW,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MAC5F,MAAMO,kBAAkB,IAAAX,sBAAA,GAAGQ,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC,cAAAT,sBAAA,uBAAxCA,sBAAA,CAA0CU,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACnG,MAAMQ,sBAAsB,GAAGD,kBAAkB,MAAAV,sBAAA,GAAIO,QAAQ,CAACC,aAAa,CAAC,sBAAsB,CAAC,cAAAR,sBAAA,uBAA9CA,sBAAA,CAAgDS,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACnI,MAAMS,iBAAiB,GAAIf,KAAK,CAACM,MAAM,CAAiBU,EAAE,CAACC,UAAU,CAAC,yBAAyB,CAAC;MAChG,MAAMC,WAAW,IAAAd,sBAAA,GAAGM,QAAQ,CAACC,aAAa,CAAC,wBAAwB,CAAC,cAAAP,sBAAA,uBAAhDA,sBAAA,CAAkDQ,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACpG,MAAMa,qBAAqB,GAAInB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,+BAA+B,CAAC,KAAK,IAAI;MAC7G,MAAMa,cAAc,GAAIpB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,8BAA8B,CAAC,KAAK,IAAI;;MAErG;MACA,MAAMc,mBAAmB,GAAG1C,eAAe,CAACpB,mBAAmB,CAAC;;MAEhE;MACA,IACI8D,mBAAmB,aAAnBA,mBAAmB,eAAnBA,mBAAmB,CAAE/C,OAAO,IAC5B,CAAC+C,mBAAmB,CAAC/C,OAAO,CAACsC,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MAAI;MAC/D,CAACG,aAAa;MAAI;MAClB,CAACI,kBAAkB;MAAI;MACvB,CAACC,sBAAsB;MAAI;MAC3B,CAACC,iBAAiB;MAAI;MACtB,CAACG,WAAW;MAAI;MAChB,CAACC,qBAAqB;MAAI;MAC1B,CAACC,cAAc,IACf,CAACf,yBAAyB,IAC1B,CAACG,oBAAoB,EACvB;QACE9C,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;QACvBF,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC;MAClC;IACJ,CAAC;IAEDkD,QAAQ,CAACY,gBAAgB,CAAC,WAAW,EAAEvB,kBAAkB,CAAC;IAC1D,OAAO,MAAMW,QAAQ,CAACa,mBAAmB,CAAC,WAAW,EAAExB,kBAAkB,CAAC;EAC9E,CAAC,EAAE,CAACxC,mBAAmB,CAAC,CAAC;;EAEzB;EACA7C,SAAS,CAAC,MAAM;IACZ,MAAMqF,kBAAkB,GAAIC,KAAiB,IAAK;MAC9C,IAAI,CAACzC,mBAAmB,EAAE,OAAO,CAAC;;MAElC,MAAM+C,MAAM,GAAGN,KAAK,CAACM,MAAqB;;MAE1C;MACA,MAAMkB,mBAAmB,GAAG,CACxB,cAAc,EACd,uBAAuB,EACvB,eAAe,EACf,sBAAsB,EACtB,wBAAwB,EACxB,iBAAiB,EACjB,wBAAwB,EACxB,kBAAkB,EAClB,uBAAuB,EACvB,+BAA+B,EAC/B,qBAAqB,EACrB,8BAA8B,EAC9B,oBAAoB,EACpB,4BAA4B,EAC5B,oBAAoB,EACpB,qBAAqB,EACrB,qBAAqB,EACrB,aAAa,EACb,oBAAoB,EACpB,iBAAiB,EACjB,0BAA0B,EAC1B,aAAa,EACb,mBAAmB,CACtB;;MAED;MACA,MAAMX,kBAAkB,GAAGW,mBAAmB,CAACC,IAAI,CAACC,QAAQ,IAAI;QAC5D,MAAMC,OAAO,GAAGjB,QAAQ,CAACC,aAAa,CAACe,QAAQ,CAAC;QAChD,OAAOC,OAAO,IAAIA,OAAO,CAACf,QAAQ,CAACN,MAAM,CAAC;MAC9C,CAAC,CAAC;;MAEF;MACA,MAAMsB,cAAc,GAAGtB,MAAM,CAACC,OAAO,CAAC,qHAAqH,CAAC,KAAK,IAAI;MACrK,MAAMsB,oBAAoB,GAAGvB,MAAM,CAACC,OAAO,CAAC,uBAAuB,CAAC,KAAK,IAAI;MAC7E,MAAMQ,iBAAiB,GAAGT,MAAM,CAACU,EAAE,IAAIV,MAAM,CAACU,EAAE,CAACC,UAAU,CAAC,yBAAyB,CAAC;MACtF,MAAMa,gBAAgB,GAAGxB,MAAM,CAACC,OAAO,CAAC,kCAAkC,CAAC,KAAK,IAAI;;MAEpF;MACA,MAAMc,mBAAmB,GAAG1C,eAAe,CAACpB,mBAAmB,CAAC;;MAEhE;MACA,IACI8D,mBAAmB,aAAnBA,mBAAmB,eAAnBA,mBAAmB,CAAE/C,OAAO,IAC5B,CAAC+C,mBAAmB,CAAC/C,OAAO,CAACsC,QAAQ,CAACN,MAAM,CAAC;MAAI;MACjD,CAACO,kBAAkB;MAAI;MACvB,CAACe,cAAc;MAAI;MACnB,CAACC,oBAAoB;MAAI;MACzB,CAACd,iBAAiB;MAAI;MACtB,CAACe,gBAAgB,CAAC;MAAA,EACpB;QACEpE,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;QACvBF,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC;MAClC;IACJ,CAAC;IAEDkD,QAAQ,CAACY,gBAAgB,CAAC,WAAW,EAAEvB,kBAAkB,CAAC;IAC1D,OAAO,MAAMW,QAAQ,CAACa,mBAAmB,CAAC,WAAW,EAAExB,kBAAkB,CAAC;EAC9E,CAAC,EAAE,CAACxC,mBAAmB,CAAC,CAAC;;EAEzB;EACA7C,SAAS,CAAC,MAAM;IACZ,IAAI6C,mBAAmB,EAAE;MACrB,MAAM6B,SAAS,GAAGhB,YAAY,CAACb,mBAAmB,CAAC;MACnD,IAAI6B,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEd,OAAO,EAAE;QACpB,MAAMiB,MAAM,GAAIH,SAAS,CAACd,OAAO,CAASiB,MAAM;QAChD,IAAIA,MAAM,EAAE;UACR;UACA,MAAMwC,SAAS,GAAGxC,MAAM,CAACwC,SAAS;UAClC,IAAIA,SAAS,EAAE;YACX,MAAMC,KAAK,GAAGD,SAAS,CAACC,KAAK;YAC7B,IAAIA,KAAK,EAAE;cACPjE,kBAAkB,CAACO,OAAO,CAACE,GAAG,CAACjB,mBAAmB,EAAE;gBAChD0E,KAAK,EAAED,KAAK,CAACE,WAAW;gBACxBC,GAAG,EAAEH,KAAK,CAACI;cACf,CAAC,CAAC;YACN;UACJ;;UAEA;UACAC,UAAU,CAAC,MAAM;YACb9C,MAAM,CAAC+C,KAAK,CAAC,CAAC;;YAEd;YACA,MAAMC,aAAa,GAAGxE,kBAAkB,CAACO,OAAO,CAACI,GAAG,CAACnB,mBAAmB,CAAC;YACzE,IAAIgF,aAAa,IAAIR,SAAS,EAAE;cAC5B,IAAI;gBACA,MAAMC,KAAK,GAAGzC,MAAM,CAACiD,cAAc,CAACC,WAAW,CAAC,CAAC;gBACjD,MAAMC,QAAQ,GAAGnD,MAAM,CAACA,MAAM,CAACoD,UAAU;gBACzC,IAAID,QAAQ,EAAE;kBAAA,IAAAE,qBAAA,EAAAC,sBAAA;kBACVb,KAAK,CAACc,QAAQ,CAACJ,QAAQ,EAAEK,IAAI,CAACC,GAAG,CAACT,aAAa,CAACN,KAAK,EAAE,EAAAW,qBAAA,GAAAF,QAAQ,CAAC5D,WAAW,cAAA8D,qBAAA,uBAApBA,qBAAA,CAAsBK,MAAM,KAAI,CAAC,CAAC,CAAC;kBAC1FjB,KAAK,CAACkB,MAAM,CAACR,QAAQ,EAAEK,IAAI,CAACC,GAAG,CAACT,aAAa,CAACJ,GAAG,EAAE,EAAAU,sBAAA,GAAAH,QAAQ,CAAC5D,WAAW,cAAA+D,sBAAA,uBAApBA,sBAAA,CAAsBI,MAAM,KAAI,CAAC,CAAC,CAAC;kBACtFlB,SAAS,CAACoB,WAAW,CAACnB,KAAK,CAAC;gBAChC;cACJ,CAAC,CAAC,OAAOoB,CAAC,EAAE;gBACR;gBACAC,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;cACvE;YACJ;UACJ,CAAC,EAAE,GAAG,CAAC;QACX;MACJ;IACJ;EACJ,CAAC,EAAE,CAAC/F,mBAAmB,CAAC,CAAC;EAIzB,MAAMgG,YAAY,GAAG1I,WAAW,CAAC,CAAC2I,UAAkB,EAAEnF,KAAa,EAAEoF,WAAmB,KAAK;IACzFxF,UAAU,CAACK,OAAO,GAAGkF,UAAU;;IAE/B;IACA,MAAME,gBAAgB,GAAGtG,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;IACzH,MAAM4G,QAAQ,GAAGvG,YAAY,IAAIN,gBAAgB,KAAK,MAAM;IAC5D,MAAM8G,kBAAkB,GAAGD,QAAQ,IAAI5G,oBAAoB,KAAK,cAAc;IAC9E,MAAM8G,YAAY,GAAGF,QAAQ,IAAI5G,oBAAoB,KAAK,QAAQ;IAClE,MAAM+G,aAAa,GAAGH,QAAQ,KAAK5G,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAS,CAAC;IAE5GsG,OAAO,CAACU,GAAG,CAAC,0BAA0B,EAAE;MACpC3G,YAAY;MACZN,gBAAgB;MAChBC,oBAAoB;MACpB2G,gBAAgB;MAChBC,QAAQ;MACRE,YAAY;MACZJ,WAAW;MACXD,UAAU,EAAEA,UAAU,CAACQ,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG;IAC9C,CAAC,CAAC;IAEF,IAAIN,gBAAgB,EAAE;MAClB,MAAMO,gBAAgB,GAAG5G,WAAW,GAAG,CAAC;MAExC,IAAIuG,kBAAkB,EAAE;QAAA,IAAAM,qBAAA,EAAAC,sBAAA;QACpB;QACA,MAAMC,gBAAgB,IAAAF,qBAAA,GAAGjH,oBAAoB,CAACgH,gBAAgB,CAAC,cAAAC,qBAAA,wBAAAC,sBAAA,GAAtCD,qBAAA,CAAwCG,UAAU,cAAAF,sBAAA,uBAAlDA,sBAAA,CAAoDG,IAAI,CAC5EC,SAAc,IAAKA,SAAS,CAACvD,EAAE,KAAKyC,WAAW,IAAIc,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;QAED,IAAIJ,gBAAgB,EAAE;UAClB;UACAjH,qBAAqB,CAACsG,WAAW,EAAED,UAAU,CAAC;QAClD;MACJ,CAAC,MAAM;QAAA,IAAAiB,qBAAA,EAAAC,sBAAA;QACH;QACA,MAAMC,qBAAqB,IAAAF,qBAAA,GAAGzH,yBAAyB,CAACiH,gBAAgB,CAAC,cAAAQ,qBAAA,wBAAAC,sBAAA,GAA3CD,qBAAA,CAA6CJ,UAAU,cAAAK,sBAAA,uBAAvDA,sBAAA,CAAyDJ,IAAI,CACtFC,SAAc,IAAKA,SAAS,CAACvD,EAAE,KAAKyC,WAAW,IAAIc,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;QAED,IAAIG,qBAAqB,EAAE;UACvB;UACAzH,0BAA0B,CAACuG,WAAW,EAAED,UAAU,CAAC;QACvD;MACJ;IACJ,CAAC,MAAM,IAAIG,QAAQ,KAAKE,YAAY,IAAIC,aAAa,CAAC,EAAE;MAAA,IAAAc,sBAAA,EAAAC,sBAAA;MACpD;MACA,MAAMZ,gBAAgB,GAAG5G,WAAW,GAAG,CAAC;MACxC,MAAM+G,gBAAgB,IAAAQ,sBAAA,GAAG3H,oBAAoB,CAACgH,gBAAgB,CAAC,cAAAW,sBAAA,wBAAAC,sBAAA,GAAtCD,sBAAA,CAAwCP,UAAU,cAAAQ,sBAAA,uBAAlDA,sBAAA,CAAoDP,IAAI,CAC5EC,SAAc,IAAKA,SAAS,CAACvD,EAAE,KAAKyC,WAAW,IAAIc,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;MAED,IAAIJ,gBAAgB,EAAE;QAClB;QACAjH,qBAAqB,CAACsG,WAAW,EAAED,UAAU,CAAC;QAC9CH,OAAO,CAACU,GAAG,CAAC,kCAAkChH,oBAAoB,kBAAkB,CAAC;MACzF,CAAC,MAAM;QAAA,IAAA+H,sBAAA,EAAAC,sBAAA;QACH1B,OAAO,CAAC2B,IAAI,CAAC,kCAAkCjI,oBAAoB,OAAO,EAAE;UACxEkH,gBAAgB;UAChBR,WAAW;UACXwB,mBAAmB,GAAAH,sBAAA,GAAE7H,oBAAoB,CAACgH,gBAAgB,CAAC,cAAAa,sBAAA,wBAAAC,sBAAA,GAAtCD,sBAAA,CAAwCT,UAAU,cAAAU,sBAAA,uBAAlDA,sBAAA,CAAoDG,GAAG,CAACC,CAAC,KAAK;YAAEnE,EAAE,EAAEmE,CAAC,CAACnE,EAAE;YAAEwD,IAAI,EAAEW,CAAC,CAACX;UAAK,CAAC,CAAC;QAClH,CAAC,CAAC;MACN;IACJ,CAAC,MAAM;MACH;MACA9H,kBAAkB,CAAC+G,WAAW,EAAEpF,KAAK,EAAEmF,UAAU,CAAC;MAClDH,OAAO,CAACU,GAAG,CAAC,4CAA4C,CAAC;IAC7D;IAEApH,mBAAmB,CAAC,IAAI,CAAC;;IAEzB;IACAgD,kBAAkB,CAAC6D,UAAU,EAAEnF,KAAK,CAAC;EACzC,CAAC,EAAE,CAACjB,YAAY,EAAEN,gBAAgB,EAAEC,oBAAoB,EAAEM,WAAW,EAAEJ,oBAAoB,EAAED,yBAAyB,EAAEG,qBAAqB,EAAED,0BAA0B,EAAER,kBAAkB,EAAEC,mBAAmB,EAAEgD,kBAAkB,CAAC,CAAC;EACxO,MAAMyF,oBAAoB,GAAI3B,WAAmB,IAAK;IAClD;IACA,IAAIpH,eAAe,EAAE;MACjB,OAAO,CAAC;IACZ;;IAEA;IACAO,iBAAiB,CAAC6G,WAAW,CAAC;;IAE9B;IACA,IAAIrH,OAAO,EAAE;MACTA,OAAO,CAAC,CAAC;IACb;EACJ,CAAC;EACD,MAAMiJ,mBAAmB,GAAGA,CAAC5B,WAAmB,EAAEpF,KAAY,KAAK;IAC/D;IACA,MAAMqF,gBAAgB,GAAGtG,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;IAEzH,IAAI2G,gBAAgB,EAAE;MAClB;MACA;MACA7G,eAAe,CAAC4G,WAAW,EAAEpF,KAAK,CAAC;IACvC,CAAC,MAAM;MACH;MACAxB,eAAe,CAAC4G,WAAW,EAAEpF,KAAK,CAAC;IACvC;;IAEA;IACApC,sBAAsB,CAACC,KAAK,CAAC;EACjC,CAAC;EACD,MAAMoJ,WAAW,GAAItF,KAA2C,IAAK;IACjEA,KAAK,CAACuF,cAAc,CAAC,CAAC;IAEtB,MAAMC,aAAa,GAAGxF,KAAK,CAACwF,aAAa;IACzC,MAAMC,UAAU,GAAGD,aAAa,CAACE,OAAO,CAAC,YAAY,CAAC;IACtD,MAAMC,UAAU,GAAGH,aAAa,CAACE,OAAO,CAAC,WAAW,CAAC;IAErD,IAAIC,UAAU,EAAE;MACZ,MAAMC,YAAY,GAAGD,UAAU,CAACzG,QAAQ,CAAC,YAAY,CAAC;MACtD,IAAI0G,YAAY,EAAE;QACdC,aAAa,CAACF,UAAU,CAAC;MAC7B,CAAC,MAAM;QACHE,aAAa,CAACF,UAAU,CAAC;MAC7B;IACJ,CAAC,MAAM;MACHE,aAAa,CAACJ,UAAU,CAAC;IAC7B;EACJ,CAAC;EAGD,MAAMI,aAAa,GAAIhH,OAAe,IAAK;IACvC,IAAItB,mBAAmB,EAAE;MACrB,MAAM6B,SAAS,GAAGhB,YAAY,CAACb,mBAAmB,CAAC;MACnD,IAAI6B,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEd,OAAO,EAAE;QACpB,MAAMiB,MAAM,GAAIH,SAAS,CAACd,OAAO,CAASiB,MAAM;QAChDA,MAAM,CAACwC,SAAS,CAAC+D,UAAU,CAACjH,OAAO,CAAC;MACxC;IACJ;EACJ,CAAC;EAGD,MAAM,CAACkH,cAAc,EAAEC,iBAAiB,CAAC,GAAGvL,QAAQ,CAAU,KAAK,CAAC;EACpEC,SAAS,CAAC,MAAM;IACpB,MAAMuL,GAAG,GAAGvF,QAAQ,CAACwF,IAAI,CAACC,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK;IACtDH,iBAAiB,CAACC,GAAG,CAACG,WAAW,CAAC,CAAC,KAAK,KAAK,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC;;EAEE;EACA,MAAMC,eAAe,GAAGvL,OAAO,CAAC,OAAO;IACnCwL,OAAO,EAAE,CACL,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EACnE,MAAM,EAAE,UAAU,EAAE,MAAM,EAC1B;MACIC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,+DAA+D;MACxEC,IAAI,EAAE,CACF,QAAQ,EACR,OAAO,EAAE,OAAO,EAAE,OAAO,EACzB,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAC5B,IAAI,EAAE,QAAQ,EAAE,YAAY,EAC5B,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,EAC9D,SAAS,EAAE,QAAQ,EAAE,WAAW;IAExC,CAAC,CACJ;IACDC,IAAI,EAAE;MACFC,gBAAgB,EAAE,KAAK;MACvBC,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,IAAI;MACvBC,oBAAoB,EAAE,IAAI;MAC1BC,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE;IACnB,CAAC;IACDC,MAAM,EAAE;MACJC,MAAM,EAAE;IACZ,CAAC;IACDC,KAAK,EAAE;MACHD,MAAM,EAAE;IACZ,CAAC;IACDE,QAAQ,EAAE;MACNC,IAAI,EAAE;QACFZ,IAAI,EAAE;UACF,qBAAqB,EAAE,SAAS;UAChC,oBAAoB,EAAE,QAAQ;UAC9B,2BAA2B,EAAE,eAAe;UAC5C,uBAAuB,EAAE,WAAW;UACpC,qBAAqB,EAAE,SAAS;UAChC,4BAA4B,EAAE;QAClC;MACJ;IACJ;EACJ,CAAC,CAAC,EAAE,EAAE,CAAC;;EAEP;EACA,MAAMa,cAAc,GAAG3M,MAAM,CAA0D,IAAIqD,GAAG,CAAC,CAAC,CAAC;;EAEjG;EACA,MAAMuJ,eAAe,GAAG1M,WAAW,CAAEwD,KAAa,IAAU;IACxD,MAAMmJ,gBAAgB,GAAGjK,mBAAmB,KAAKc,KAAK;IACtD,MAAMoJ,MAAM,GAAGH,cAAc,CAAChJ,OAAO,CAACI,GAAG,CAACL,KAAK,CAAC;;IAEhD;IACA,IAAIoJ,MAAM,IAAIA,MAAM,CAACC,gBAAgB,KAAKF,gBAAgB,EAAE;MACxD,OAAOC,MAAM,CAACE,MAAM;IACxB;;IAEA;IACA,MAAMC,SAAS,GAAG;MACdC,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAE/B,cAAc,GAAG,KAAK,GAAG,KAAK;MACzCgC,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAE,sBAAsB;MACnCC,aAAa,EAAE,KAAK;MACpBC,eAAe,EAAE,KAAK;MACtB;MACAC,OAAO,EAAEX,gBAAgB;MACzBY,gBAAgB,EAAE,KAAK;MACvBC,gBAAgB,EAAE,KAAK;MACvBC,oBAAoB,EAAE,KAAK;MAC3BC,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE,IAAI;MAChBC,kBAAkB,EAAE,KAAK;MACzBC,sBAAsB,EAAE,KAAK;MAC7BC,MAAM,EAAE,MAAM;MACd;MACAC,SAAS,EAAEpB,gBAAgB,GAAG,GAAG,GAAG,EAAE;MACtCqB,SAAS,EAAE,GAAG;MACdC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAErI,QAAQ,CAACwF,IAAI;MACxBgB,MAAM,EAAE,MAAM;MACd8B,cAAc,EAAE,KAAK;MACrBC,oBAAoB,EAAE,KAAK;MAC3BC,MAAM,EAAE;QACJC,OAAO,EAAE7D;MACb,CAAC;MACD;MACA,GAAGe;IACP,CAAC;;IAED;IACAiB,cAAc,CAAChJ,OAAO,CAACE,GAAG,CAACH,KAAK,EAAE;MAC9BsJ,MAAM,EAAEC,SAAS;MACjBF,gBAAgB,EAAEF;IACtB,CAAC,CAAC;IAEF,OAAOI,SAAS;EACpB,CAAC,EAAE,CAAC7B,cAAc,EAAET,WAAW,EAAEe,eAAe,EAAE9I,mBAAmB,CAAC,CAAC;;EAEvE;EACA,MAAMmG,gBAAgB,GAAGtG,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;EACzH,MAAM4G,QAAQ,GAAGvG,YAAY,IAAIN,gBAAgB,KAAK,MAAM;EAC5D,MAAM8G,kBAAkB,GAAGD,QAAQ,IAAI5G,oBAAoB,KAAK,cAAc;EAC9E,MAAM8G,YAAY,GAAGF,QAAQ,IAAI5G,oBAAoB,KAAK,QAAQ;EAClE,MAAM+G,aAAa,GAAGH,QAAQ,KAAK5G,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAS,CAAC;EAC5G,MAAMkH,gBAAgB,GAAG5G,WAAW,GAAG,CAAC;EAExC,IAAI+L,kBAAyB,GAAG,EAAE;EAElC,IAAI1F,gBAAgB,IAAI,CAACE,kBAAkB,EAAE;IACzC;IACAwF,kBAAkB,GAAG9L,8BAA8B,CAAC2G,gBAAgB,EAAE,KAAK,CAAC;EAChF,CAAC,MAAM,IAAIN,QAAQ,KAAKE,YAAY,IAAIC,aAAa,IAAIF,kBAAkB,CAAC,EAAE;IAAA,IAAAyF,sBAAA;IAC1E;IACA,KAAAA,sBAAA,GAAIpM,oBAAoB,CAACgH,gBAAgB,CAAC,cAAAoF,sBAAA,eAAtCA,sBAAA,CAAwChF,UAAU,EAAE;MACpD+E,kBAAkB,GAAGnM,oBAAoB,CAACgH,gBAAgB,CAAC,CAACI,UAAU,CAACiF,MAAM,CAACnE,CAAC,IAAIA,CAAC,CAACX,IAAI,KAAK,KAAK,CAAC;MACpGnB,OAAO,CAACU,GAAG,CAAC,yDAAyDhH,oBAAoB,SAASkH,gBAAgB,GAAG,EAAE;QACnHsF,eAAe,EAAEtM,oBAAoB,CAACgH,gBAAgB,CAAC,CAACI,UAAU,CAACpB,MAAM;QACzEuG,aAAa,EAAEJ,kBAAkB,CAACnG,MAAM;QACxCwG,OAAO,EAAEL,kBAAkB,CAAClE,GAAG,CAACC,CAAC,KAAK;UAAEnE,EAAE,EAAEmE,CAAC,CAACnE,EAAE;UAAE0I,WAAW,EAAEvE,CAAC,CAACuE;QAAY,CAAC,CAAC;MACnF,CAAC,CAAC;IACN,CAAC,MAAM;MACHrG,OAAO,CAAC2B,IAAI,CAAC,iDAAiDjI,oBAAoB,SAASkH,gBAAgB,EAAE,CAAC;MAC9GmF,kBAAkB,GAAG,EAAE;IAC3B;EACJ,CAAC,MAAM;IACH;IACAA,kBAAkB,GAAG3M,aAAa;EACtC;;EAEA;EACAjC,KAAK,CAACE,SAAS,CAAC,MAAM;IAClB0O,kBAAkB,CAACO,OAAO,CAAEC,IAAS,IAAK;MACtC,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAIxL,KAAK,GAAG,EAAE;MACd,IAAI2C,EAAE,GAAG,EAAE;MAEX,IAAK0C,gBAAgB,IAAI,CAACE,kBAAkB,IAAMD,QAAQ,KAAKE,YAAY,IAAIC,aAAa,IAAIF,kBAAkB,CAAE,EAAE;QAClHiG,OAAO,GAAGD,IAAI,CAACF,WAAW,IAAI,EAAE;QAChCrL,KAAK,GAAGuL,IAAI,CAAC5I,EAAE;QACfA,EAAE,GAAG4I,IAAI,CAAC5I,EAAE;MAChB,CAAC,MAAM;QAAA,IAAA8I,UAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,YAAA;QACHJ,OAAO,GAAG,EAAAC,UAAA,GAAAF,IAAI,CAACM,IAAI,cAAAJ,UAAA,wBAAAC,WAAA,GAATD,UAAA,CAAY,CAAC,CAAC,cAAAC,WAAA,uBAAdA,WAAA,CAAgBI,IAAI,KAAI,EAAE;QACpC9L,KAAK,IAAA2L,WAAA,GAAGJ,IAAI,CAACM,IAAI,cAAAF,WAAA,wBAAAC,YAAA,GAATD,WAAA,CAAY,CAAC,CAAC,cAAAC,YAAA,uBAAdA,YAAA,CAAgBjJ,EAAE;QAC1BA,EAAE,GAAG4I,IAAI,CAAC5I,EAAE;MAChB;MAEA,IAAIA,EAAE,IAAI3C,KAAK,EAAE;QACbsB,kBAAkB,CAACkK,OAAO,EAAExL,KAAK,CAAC;MACtC;IACJ,CAAC,CAAC;EACN,CAAC,EAAE,CAAC+K,kBAAkB,EAAE1F,gBAAgB,EAAEE,kBAAkB,EAAED,QAAQ,EAAEE,YAAY,EAAEC,aAAa,EAAEnE,kBAAkB,CAAC,CAAC;EAEzH,oBACIlE,OAAA,CAAAE,SAAA;IAAAyO,QAAA,EACKhB,kBAAkB,CAAClE,GAAG,CAAE0E,IAAS,IAAK;MACnC,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAIxL,KAAK,GAAG,EAAE;MACd,IAAI2C,EAAE,GAAG,EAAE;MAEX,IAAK0C,gBAAgB,IAAI,CAACE,kBAAkB,IAAMD,QAAQ,KAAKE,YAAY,IAAIC,aAAa,IAAIF,kBAAkB,CAAE,EAAE;QAClH;QACA;QACAiG,OAAO,GAAGD,IAAI,CAACF,WAAW,IAAI,EAAE;QAChCrL,KAAK,GAAGuL,IAAI,CAAC5I,EAAE;QACfA,EAAE,GAAG4I,IAAI,CAAC5I,EAAE;MAChB,CAAC,MAAM;QAAA,IAAAqJ,WAAA,EAAAC,YAAA,EAAAC,WAAA,EAAAC,YAAA;QACH;QACAX,OAAO,GAAG,EAAAQ,WAAA,GAAAT,IAAI,CAACM,IAAI,cAAAG,WAAA,wBAAAC,YAAA,GAATD,WAAA,CAAY,CAAC,CAAC,cAAAC,YAAA,uBAAdA,YAAA,CAAgBH,IAAI,KAAI,EAAE;QACpC9L,KAAK,IAAAkM,WAAA,GAAGX,IAAI,CAACM,IAAI,cAAAK,WAAA,wBAAAC,YAAA,GAATD,WAAA,CAAY,CAAC,CAAC,cAAAC,YAAA,uBAAdA,YAAA,CAAgBxJ,EAAE;QAC1BA,EAAE,GAAG4I,IAAI,CAAC5I,EAAE;MAChB;MAEA,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;MAGpB,MAAMK,mBAAmB,GAAG1C,eAAe,CAACqC,EAAE,CAAC;MAC/C,MAAMyJ,gBAAgB,GAAGrM,YAAY,CAAC4C,EAAE,CAAC;;MAEzC;MACA,MAAM2G,MAAM,GAAGJ,eAAe,CAACvG,EAAE,CAAC;;MAElC;MACA,MAAM0J,UAAU,GAAG;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mDAAmD1J,EAAE;AACrD,+BAA+BA,EAAE;AACjC;AACA;AACA;AACA;AACA;AACA,+BAA+BA,EAAE;AACjC,0CAA0CzD,mBAAmB,KAAKyD,EAAE,GAAG,MAAM,GAAG,MAAM;AACtF;AACA;AACA;AACA,gDAAgDzD,mBAAmB,KAAKyD,EAAE,GAAG,oBAAoB,GAAG,SAAS;AAC7G;AACA,+BAA+BA,EAAE;AACjC;AACA,gDAAgDzD,mBAAmB,KAAKyD,EAAE,GAAG,oBAAoB,GAAG,SAAS;AAC7G;AACA;AACA,+BAA+BA,EAAE;AACjC;AACA;AACA,+BAA+BA,EAAE;AACjC;AACA;AACA;AACA,+BAA+BA,EAAE;AACjC;AACA;AACA;AACA,+BAA+BA,EAAE;AACjC;AACA;AACA,oCAAoCA,EAAE;AACtC;AACA;AACA;AACA;AACA;AACA,qBAAqB;MAED,oBACIvF,OAAA,CAACV,GAAG;QAEAuB,GAAG,EAAE+E,mBAAoB;QACzBsJ,YAAY,EAAEA,CAAA,KAAM/M,eAAe,CAACoD,EAAE,CAAE;QACxC4J,YAAY,EAAEA,CAAA,KAAMhN,eAAe,CAAC,IAAI,CAAE;QAC1CiN,EAAE,EAAE;UACAC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,QAAQ,EAAE,UAAU;UACpB,2BAA2B,EAAE;YACzBC,OAAO,EAAE,cAAc;YACvBC,UAAU,EAAE;UAChB,CAAC;UACD,qCAAqC,EAAE;YACnCD,OAAO,EAAE,cAAc;YACvBC,UAAU,EAAE;UAChB,CAAC;UACD,0BAA0B,EAAE;YACxBJ,OAAO,EAAE;UACb,CAAC;UACD,iBAAiB,EAAE;YACfK,UAAU,EAAE;UAChB,CAAC;UACD,oBAAoB,EAAE;YAClBA,UAAU,EAAE;UAChB,CAAC;UACD,8BAA8B,EAAE;YAC5BC,QAAQ,EAAE;UACd,CAAC;UACD,wBAAwB,EAAE;YACtBC,KAAK,EAAE,MAAM;YACbC,UAAU,EAAE,UAAU;YACtBC,SAAS,EAAE;UACf,CAAC;UACD;UACA,qBAAqB,EAAE;YACnBP,QAAQ,EAAE,kBAAkB;YAC5B9D,MAAM,EAAE,mBAAmB;YAC3BsE,GAAG,EAAE,gBAAgB;YACrBC,IAAI,EAAE,gBAAgB;YACtBC,SAAS,EAAE;UACf,CAAC;UACD,oCAAoC,EAAE;YAClCV,QAAQ,EAAE,qBAAqB;YAC/BQ,GAAG,EAAE,iBAAiB;YACtBC,IAAI,EAAE,iBAAiB;YACvBC,SAAS,EAAE,iBAAiB;YAC5BC,QAAQ,EAAE,kBAAkB;YAC5BC,UAAU,EAAE,kBAAkB;YAC9BC,MAAM,EAAE,2BAA2B;YACnCC,YAAY,EAAE,gBAAgB;YAC9BC,SAAS,EAAE;UACf,CAAC;UACD;UACA,qBAAqB,EAAE;YACnBf,QAAQ,EAAE,kBAAkB;YAC5B9D,MAAM,EAAE,mBAAmB;YAC3BsE,GAAG,EAAE,gBAAgB;YACrBC,IAAI,EAAE,gBAAgB;YACtBC,SAAS,EAAE;UACf;QACJ,CAAE;QACFM,SAAS,EAAC,WAAW;QAAA5B,QAAA,gBAGjB3O,OAAA;UACIwQ,KAAK,EAAE;YACHjB,QAAQ,EAAE,UAAU;YACpB;YACA;YACA;YACA;YACAQ,GAAG,EACKjO,mBAAmB,KAAKyD,EAAE,GACxB,MAAM,GACNlE,gBAAgB,KAAK,QAAQ,GACzB,KAAK,GACL,KAAK;YACnBoP,KAAK,EAAG,MAAM;YACdT,IAAI,EAAG,MAAM;YACb;;YAEAX,OAAO,EAAE,MAAM;YACfqB,GAAG,EAAE,KAAK;YACVjF,MAAM,EAAE,IAAI;YACZ6D,UAAU,EAAE;UAEhB,CAAE;UACFiB,SAAS,EAAC,kBAAkB;UAAA5B,QAAA,gBAG5B3O,OAAA;YACIwQ,KAAK,EAAE;cACHnB,OAAO,EAAEnN,YAAY,KAAKqD,EAAE,GAAG,MAAM,GAAG,MAAM;cAC9C+J,UAAU,EAAE,QAAQ;cACxBqB,cAAc,EAAE,QAAQ;cACxBR,UAAU,EAAE;YAEZ,CAAE;YAAAxB,QAAA,eAEF3O,OAAA,CAACT,UAAU;cACPqR,IAAI,EAAC,OAAO;cACZC,KAAK,EAAE9P,SAAS,CAAC,cAAc,CAAE;cACjCqO,EAAE,EAAE;gBACAQ,KAAK,EAAE,MAAM;gBACb1C,MAAM,EAAE,MAAM;gBAGd4D,UAAU,EAAE;cAChB,CAAE;cAAAnC,QAAA,eAEF3O,OAAA;gBACI+Q,uBAAuB,EAAE;kBAAEC,MAAM,EAAEpR;gBAAY,CAAE;gBACjD4Q,KAAK,EAAE;kBAAEtD,MAAM,EAAE,MAAM;kBAAE0C,KAAK,EAAE,MAAM;kBAACP,OAAO,EAAG,MAAM;kBAAEC,UAAU,EAAG;gBAAQ;cAAE;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eAGNpR,OAAA;YACIwQ,KAAK,EAAE;cACHnB,OAAO,EAAEnN,YAAY,KAAKqD,EAAE,GAAG,MAAM,GAAG,MAAM;cAC9C+J,UAAU,EAAE,QAAQ;cACpBqB,cAAc,EAAE,QAAQ;cACxBR,UAAU,EAAE;YAChB,CAAE;YAAAxB,QAAA,gBAGV3O,OAAA,CAACT,UAAU;cACPqR,IAAI,EAAC,OAAO;cACZS,OAAO,EAAG1J,CAAC,IAAK;gBACZA,CAAC,CAAC2J,eAAe,CAAC,CAAC;gBACnBvP,sBAAsB,CAACD,mBAAmB,KAAKyD,EAAE,GAAG,IAAI,GAAGA,EAAE,CAAC;cAClE,CAAE;cACFsL,KAAK,EAAE9P,SAAS,CAAC,gBAAgB,CAAE;cACnCqO,EAAE,EAAE;gBACAQ,KAAK,EAAE,MAAM;gBACb1C,MAAM,EAAE,MAAM;gBAEd,QAAQ,EAAE;kBACNW,MAAM,EAAE,iHAAiH,CAAE;gBAC/H,CAAC;gBACD,SAAS,EAAE;kBAGPoC,SAAS,EAAE,YAAY;kBACvB,QAAQ,EAAE;oBACNpC,MAAM,EAAE,uHAAuH,CAAE;kBACrI;gBACJ,CAAC;gBACDiD,UAAU,EAAE,sBAAsB;gBAClC,OAAO,EAAE;kBACL5D,MAAM,EAAE,MAAM;kBACd0C,KAAK,EAAE;gBACX;cACJ,CAAE;cAAAjB,QAAA,eAEF3O,OAAA;gBACI+Q,uBAAuB,EAAE;kBAAEC,MAAM,EAAErR;gBAAS,CAAE;gBAC9C6Q,KAAK,EAAE;kBAAEtD,MAAM,EAAE,MAAM;kBAAE0C,KAAK,EAAE,MAAM;kBAAEP,OAAO,EAAG;gBAAM;cAAE;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,EAGR/P,gBAAgB,KAAK,QAAQ,IAAIC,oBAAoB,KAAK,QAAQ,iBAC/DtB,OAAA,CAAAE,SAAA;cAAAyO,QAAA,gBACR3O,OAAA,CAACT,UAAU;gBACPqR,IAAI,EAAC,OAAO;gBACZS,OAAO,EAAEA,CAAA,KAAM1H,oBAAoB,CAACwE,IAAI,CAAC5I,EAAE,CAAE;gBAC7CgM,QAAQ,EAAE3Q,eAAgB;gBAC1BiQ,KAAK,EAAEjQ,eAAe,GAAGG,SAAS,CAAC,+CAA+C,CAAC,GAAGA,SAAS,CAAC,eAAe,CAAE;gBACjHqO,EAAE,EAAE;kBACAQ,KAAK,EAAE,MAAM;kBACb1C,MAAM,EAAE,MAAM;kBACdsE,eAAe,EAAE,2BAA2B;kBAC5C,QAAQ,EAAE;oBACN3D,MAAM,EAAE,iHAAiH,CAAE;kBAC/H,CAAC;kBACD,SAAS,EAAE;oBAGP,QAAQ,EAAE;sBACNA,MAAM,EAAE,uHAAuH,CAAE;oBACrI,CAAC;oBACAoC,SAAS,EAAE;kBAChB,CAAC;kBACD,YAAY,EAAE;oBACVT,OAAO,EAAE,GAAG;oBACZiC,MAAM,EAAE;kBACZ,CAAC;kBACDX,UAAU,EAAE,sBAAsB;kBAClCY,GAAG,EAAE;oBACDxE,MAAM,EAAE,MAAM;oBACd0C,KAAK,EAAE;kBACX;gBACJ,CAAE;gBAAAjB,QAAA,eAEF3O,OAAA;kBACI+Q,uBAAuB,EAAE;oBAAEC,MAAM,EAAEtR;kBAAS,CAAE;kBAC9C8Q,KAAK,EAAE;oBACHtD,MAAM,EAAE,MAAM;oBACd0C,KAAK,EAAE,MAAM;oBAACP,OAAO,EAAG;kBAC5B;gBAAE;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eAGbpR,OAAA,CAACT,UAAU;gBACPqR,IAAI,EAAC,OAAO;gBACZS,OAAO,EAAEA,CAAA,KAAMzH,mBAAmB,CAACuE,IAAI,CAAC5I,EAAE,EAAE3C,KAAK,CAAE;gBACnDiO,KAAK,EAAE9P,SAAS,CAAC,gBAAgB,CAAE;gBACnCqO,EAAE,EAAE;kBACAQ,KAAK,EAAE,MAAM;kBACb1C,MAAM,EAAE,MAAM;kBAEd,QAAQ,EAAE;oBACNW,MAAM,EAAE,iHAAiH,CAAE;kBAC/H,CAAC;kBACD,SAAS,EAAE;oBAEPoC,SAAS,EAAE,YAAY;oBACvB,QAAQ,EAAE;sBACNpC,MAAM,EAAE,uHAAuH,CAAE;oBACrI;kBACJ,CAAC;kBACDiD,UAAU,EAAE,sBAAsB;kBAClCY,GAAG,EAAE;oBACDxE,MAAM,EAAE,MAAM;oBACd0C,KAAK,EAAE;kBACX;gBACJ,CAAE;gBAAAjB,QAAA,eAED3O,OAAA;kBACG+Q,uBAAuB,EAAE;oBAAEC,MAAM,EAAEnR;kBAAW,CAAE;kBAChD2Q,KAAK,EAAE;oBACHtD,MAAM,EAAE,MAAM;oBACd0C,KAAK,EAAE,MAAM;oBAACP,OAAO,EAAG;kBAC5B;gBAAE;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC;YAAA,eACX,CACD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGFpR,OAAA;UACIuF,EAAE,EAAE,OAAOA,EAAE,EAAG;UAChBiL,KAAK,EAAE;YAAEZ,KAAK,EAAE,MAAM;YAAEL,QAAQ,EAAE;UAAW,CAAE;UAAAZ,QAAA,gBAE/C3O,OAAA;YAAA2O,QAAA,EACKM;UAAU;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACRpR,OAAA,CAACR,WAAW;YAERqB,GAAG,EAAEmO,gBAAiB;YACtB2C,KAAK,EAAEvD,OAAQ;YACflC,MAAM,EAAEA,MAAO;YACf0F,QAAQ,EAAG7J,UAAU,IAAKD,YAAY,CAACC,UAAU,EAAEnF,KAAK,EAAE2C,EAAE;UAAE,GAJzD,cAAcA,EAAE,EAAE;YAAA0L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAK1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAED,CAAC;MAAA,GApQD7L,EAAE;QAAA0L,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqQN,CAAC;IAEd,CAAC;EAAC,gBACJ,CAAC;AAEX,CAAC;EAAA,QAz4B4BtR,cAAc,EAkBnCL,cAAc;AAAA,EAw3B1B,CAAC;EAAA,QA14BgCK,cAAc,EAkBnCL,cAAc;AAAA,EAw3BzB;AAACoS,GAAA,GA54BI1R,UAAqC;AA84B3C,eAAeA,UAAU;AAAC,IAAAE,EAAA,EAAAwR,GAAA;AAAAC,YAAA,CAAAzR,EAAA;AAAAyR,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}