import { adminApiService, userApiService } from './APIService';
import { ScrapedElement } from './ScrapingService';
export interface Agent {
  Name: string | undefined;
  Description: string | undefined;
  AccountId:string;
  url: string;
  TrainingFields: ScrapedElement[];
  AdditionalContext?: string;
}


export const NewAgentTraining = async (agent: Agent) => {
    try {
        const response = await userApiService.post(`Assistant/NewAgentTraining`, agent);
        return response.data;
    } catch (error) {
        console.error("Error uploading agent", error);
        return { message: "Upload failed" };
    }
};


export const CreateInteraction = async (userCommand: string, accountId: string,targetUrl:string) => {
    
    try {
        const requestBody = {
			userCommand,
			accountId,
			targetUrl,
		};
        const response = await adminApiService.post(`Ai/CreateInteraction`, requestBody)
        return response.data;
    } catch (error) {
        console.error("Error in creating integration", error);
        return [];
    }
}

export const GenerateImageWithUserPrompt = async (userPrompt:string,accountId:string,onImageGenerated:any,openSnackbar?:any,signal?: AbortSignal) => {
	try {
        const requestBody = {
			userPrompt,
			accountId
		};
        const response = await adminApiService.post(`Ai/GenerateImageOnUserPrompt`, requestBody, {
            signal, // 🆕 attach signal to cancel the request
        });

        if (response.data && response.data.Success) {
            onImageGenerated(response.data.SuccessMessage);
            return response.data;
        } else {
            
            openSnackbar(response.data.ErrorMessage,"error");
            
            return null;
        }

        
    } catch (error: any) {
    if (error.name === "CanceledError" || error.code === "ERR_CANCELED") {
      console.warn("Image generation canceled by user.");
    } else {
      console.error("Error generating image:", error);
      openSnackbar?.("An error occurred while generating the image. Please try again.", "error");
    }
    return null;
  }
}


export const EnhanceUserPrompt = async (userPrompt:string, accountId:string, openSnackbar?:any) => {
    try {
        const requestBody = {
            userPrompt,
            accountId
        };
        const response = await adminApiService.post(`Ai/EnhanceUserPrompt`, requestBody)
        

        if (response.data && response.data.Success) {
            return response.data.SuccessMessage;
        } else {
            if (openSnackbar) {
                openSnackbar(response.data.ErrorMessage || "Failed to enhance prompt. Please try again.", "error");
            }
            return null;
        }
    } catch (error) {
        console.error("Error in enhancing prompt", error);
        if (openSnackbar) {
            openSnackbar("An error occurred while enhancing the prompt. Please try again.", "error");
        }
        return null;
    }
}
