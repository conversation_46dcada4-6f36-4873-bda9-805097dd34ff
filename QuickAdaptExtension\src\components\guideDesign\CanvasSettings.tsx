import React, { useEffect, useState } from "react";
import { Box, Typography, TextField, Grid, IconButton, Button, Tooltip } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import RadioButtonUncheckedIcon from "@mui/icons-material/RadioButtonUnchecked";
import RadioButtonCheckedIcon from "@mui/icons-material/RadioButtonChecked";
// import Draggable from "react-draggable";
import ArrowBackIosNewOutlinedIcon from "@mui/icons-material/ArrowBackIosNewOutlined";
import "./Canvas.module.css";
import useDrawerStore from "../../store/drawerStore";
import { defaultDots,topLeft,topCenter,topRight,middleLeft,middleCenter,middleRight,bottomLeft,bottomMiddle,bottomRight, topcenter, warning, centercenter } from "../../assets/icons/icons";
import { useTranslation } from "react-i18next";
const CanvasSettings = ({ zindeex, setZindeex, setShowCanvasSettings, selectedTemplate }: any) => {
	const {
		setCanvasSetting,
		borderColor,
		announcementJson,
		width,
		setWidth,
		backgroundColor,
		setBorderColor,
		setBackgroundColor,
		borderRadius,
		setBorderRadius,
		Annpadding,
		setAnnPadding,
		AnnborderSize,
		setAnnBorderSize,
		Bposition,
		setBposition,
		setIsUnSavedChanges,
		currentStep,
		syncAIAnnouncementCanvasSettings,
		createWithAI,
		//selectedTemplate,
		selectedTemplateTour
	} = useDrawerStore((state: any) => state);
	const { t: translate } = useTranslation();
	const [isOpen, setIsOpen] = useState(true);
	const [selectedPosition, setSelectedPosition] = useState("middle-center");
	const [widthError, setWidthError] = useState(false);
	const [paddingError, setPaddingError] = useState(false);
	const [borderRadiusError, setBorderRadiusError] = useState(false);
	const [borderSizeError, setBorderSizeError] = useState(false);
	const positions = [
		{ label: translate("Top Left"), icon: <span dangerouslySetInnerHTML={{ __html: topLeft }} style={{ fontSize: "small" }} />, value: "top-left" },
		{ label: translate("Top Center"), icon: <span dangerouslySetInnerHTML={{ __html: topcenter }} style={{ fontSize: "small" }} />, value: "top-center" },
		{ label: translate("Top Right"), icon: <span dangerouslySetInnerHTML={{ __html: topRight }} style={{ fontSize: "small" }} />, value: "top-right" },
		{ label: translate("Middle Left"), icon: <span dangerouslySetInnerHTML={{ __html: middleLeft }} style={{ fontSize: "small" }} />, value: "left-center" },
		{ label: translate("Middle Center"), icon: <span dangerouslySetInnerHTML={{ __html: middleCenter }} style={{ fontSize: "small" }} />, value: "center-center" },
		{ label: translate("Middle Right"), icon: <span dangerouslySetInnerHTML={{ __html: middleRight }} style={{ fontSize: "small" }} />, value: "right-center" },
		{ label: translate("Bottom Left"), icon: <span dangerouslySetInnerHTML={{ __html: bottomLeft }} style={{ fontSize: "small" }} />, value: "bottom-left" },
		{ label: translate("Bottom Center"), icon: <span dangerouslySetInnerHTML={{ __html: bottomMiddle }} style={{ fontSize: "small" }} />, value: "bottom-center" },
		{ label: translate("Bottom Right"), icon: <span dangerouslySetInnerHTML={{ __html: bottomRight }} style={{ fontSize: "small" }} />, value: "bottom-right" },
	];
	const renderPositionIcon = (positionValue:any) => {
		const isSelected = Bposition === positionValue;

		if (!isSelected) {
		  return <span dangerouslySetInnerHTML={{ __html: defaultDots }} style={{ fontSize: "small" }} />;
		}

		switch (positionValue) {
		  case "top-left":
			return <span dangerouslySetInnerHTML={{ __html: topLeft }} style={{ fontSize: "small" }} />;
		  case "top-center":
			return <span dangerouslySetInnerHTML={{ __html: topcenter }} style={{ fontSize: "small" }} />;
		  case "top-right":
			return <span dangerouslySetInnerHTML={{ __html: topRight }} style={{ fontSize: "small" }} />;
		  case "left-center":
			return <span dangerouslySetInnerHTML={{ __html: middleLeft }} style={{ fontSize: "small" }} />;
		  case "center-center":
			return <span dangerouslySetInnerHTML={{ __html: middleCenter }} style={{ fontSize: "small" }} />;
		  case "right-center":
			return <span dangerouslySetInnerHTML={{ __html: middleRight }} style={{ fontSize: "small" }} />;
		  case "bottom-left":
			return <span dangerouslySetInnerHTML={{ __html: bottomLeft }} style={{ fontSize: "small" }} />;
		  case "bottom-center":
			return <span dangerouslySetInnerHTML={{ __html: bottomMiddle }} style={{ fontSize: "small" }} />;
		  case "bottom-right":
			return <span dangerouslySetInnerHTML={{ __html: bottomRight }} style={{ fontSize: "small" }} />;
		  default:
			return <span dangerouslySetInnerHTML={{ __html: defaultDots }} style={{ fontSize: "small" }} />;
		}
	  };

	useEffect(() => {
		const currentStepIndex = announcementJson?.GuideStep?.findIndex(
			(step: any) => {
				let stepNum = step.stepName;// 
				if (typeof stepNum === "string" && stepNum.toLowerCase().startsWith("step")) {
					stepNum = parseInt(stepNum.replace(/[^0-9]/g, ""), 10);
				}
				return String(stepNum) === String(currentStep); 
			}
		);

		// Get canvas data for the current step (if found), otherwise use the first step
		const canvasData = (currentStepIndex !== -1)
			? announcementJson?.GuideStep?.[currentStepIndex]?.Canvas
			:  {
									Position: "center-center",
									Width:500,
									Padding: "12",
									Radius: 8,
									BorderSize: 0,
									BorderColor: "#000000",
									BackgroundColor: "#ffffff"
								};;

		let initialWidth;
		let initialPadding;
		let initialBorderRadius;
		let initialBorderSize;

		if (canvasData) {
			setSelectedPosition(canvasData.Position || "center-center");
			setBposition(canvasData.Position || "center-center");
			initialWidth = canvasData.Width || 500;
			setBackgroundColor(canvasData.BackgroundColor || "#ffffff");
			initialBorderRadius = canvasData.Radius ?? 8;
			initialPadding = canvasData.Padding || "12";
			setBorderColor(canvasData.BorderColor || "#000000");
			initialBorderSize = canvasData.BorderSize || 0;
		} else {
			setSelectedPosition("center-center");
			setBposition("center-center");
			initialWidth = 500;
			setBackgroundColor("#ffffff");
			initialBorderRadius = 8;
			initialPadding = "12";
			setBorderColor("#000000");
			initialBorderSize = 0;
		}

		// Validate initial width
		if (initialWidth < 300 || initialWidth > 1200) {
			setWidthError(true);
			// Set width to closest valid value
			setWidth(initialWidth < 300 ? 300 : 1200);
		} else {
			setWidthError(false);
			setWidth(initialWidth);
		}

		// Validate initial padding
		const paddingValue = parseInt(initialPadding) || 12;
		if (paddingValue < 0 || paddingValue > 20) {
			setPaddingError(true);
			// Set padding to closest valid value
			setAnnPadding(paddingValue < 0 ? "0" : "20");
		} else {
			setPaddingError(false);
			setAnnPadding(initialPadding);
		}

		// Validate initial border radius
		if (initialBorderRadius < 0 || initialBorderRadius > 20) {
			setBorderRadiusError(true);
			// Set border radius to closest valid value
			setBorderRadius(initialBorderRadius < 0 ? 0 : 20);
		} else {
			setBorderRadiusError(false);
			setBorderRadius(initialBorderRadius);
		}

		// Validate initial border size
		if (initialBorderSize < 0 || initialBorderSize > 10) {
			setBorderSizeError(true);
			// Set border size to closest valid value
			setAnnBorderSize(initialBorderSize < 0 ? 0 : 10);
		} else {
			setBorderSizeError(false);
			setAnnBorderSize(initialBorderSize);
		}
	}, [announcementJson, currentStep, setBposition, setWidth, setBackgroundColor, setBorderRadius, setAnnPadding, setBorderColor, setAnnBorderSize]);

	const handlePositionClick = (positionValue: string) => {
		setSelectedPosition(positionValue);
		setBposition(positionValue);
	};
	const handleBorderColorChange = (e: any) => setBorderColor(e.target.value);
	const handleBackgroundColorChange = (e: any) => setBackgroundColor(e.target.value);

	const handleClose = () => {
		setIsOpen(false);
		setShowCanvasSettings(false);
	};
	const handleApplyChanges = () => {
		// Don't apply changes if there's any validation error
		if (widthError || paddingError || borderRadiusError || borderSizeError) {
			return;
		}

		const canvasData = {
			Position: selectedPosition,
			BackgroundColor: backgroundColor,
			Width: width || 500,
			Radius: borderRadius !== undefined ? borderRadius : 0,
			Padding: Annpadding || "12",
			BorderColor: borderColor,
			BorderSize: AnnborderSize || 0,
			Zindex: 9999,
		};

		setCanvasSetting(canvasData);

		// Sync AI announcement canvas settings after applying changes
		if (createWithAI && (selectedTemplate === "Announcement" || selectedTemplateTour === "Announcement")) {
			// Use setTimeout to ensure setCanvasSetting completes first
			setTimeout(() => {
				syncAIAnnouncementCanvasSettings(canvasData);
			}, 0);
		}

		setBposition(selectedPosition);
		handleClose();
		setIsUnSavedChanges(true);
	};

	if (!isOpen) return null;

	return (
		//<Draggable>
		<div
			id="qadpt-designpopup"
			className="qadpt-designpopup"
		>
			<div className="qadpt-content">
				<div className="qadpt-design-header">
					<IconButton
						aria-label="close"
						onClick={handleClose}
					>
						<ArrowBackIosNewOutlinedIcon />
						{/* Header */}
					</IconButton>
					<div className="qadpt-title">{translate("Canvas")}</div>
					{/* Close Button */}
					<IconButton
						size="small"
						aria-label="close"
						onClick={handleClose}
					>
						<CloseIcon />
					</IconButton>
				</div>

				{/* Position Grid */}
				<div className="qadpt-canblock">
				<div className="qadpt-controls qadpt-errmsg">
				<Box className="qadpt-position-grid">
							<Typography className="qadpt-ctrl-title">{translate("Position")}</Typography>
					<Grid
						id="pos-container"
						container
						spacing={1}
						//onClick={handlePositionClick}
					>
						{/* Position Icons */}
						{positions.map((position) => (
							<Grid item xs={4} key={position.value}>
								<IconButton
									size="small"
									disableRipple
									// sx={{
									//     backgroundColor: Bposition === position.value ? "var(--border-color)" : "transparent",
									// }}
									onClick={() => handlePositionClick(position.value)} // Pass value directly
								>
									{renderPositionIcon(position.value)}
								</IconButton>
							</Grid>
						))}
					</Grid>
				</Box>


					{/* Width Control */}
					<Box className="qadpt-control-box">
							<div className="qadpt-control-label">{translate("Width")}</div>
							<div>
						<TextField
							variant="outlined"
							value={`${width}`}
							size="small"
							autoFocus
							className="qadpt-control-input"
							onChange={(e) => {
								const inputValue = parseInt(e.target.value) || 0;

								// Validate width between 300px and 1200px
								if (inputValue < 300 || inputValue > 1200) {
									setWidthError(true);
								} else {
									setWidthError(false);
								}

								setWidth(inputValue);
							}}
							InputProps={{
								endAdornment: "px",
								sx: {

									"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
									"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
									"& fieldset":{border:"none"},

								},
							}}
							error={widthError}
							/>
							</div>

						</Box>
						{widthError && (
						<Typography
							style={{
								fontSize: "12px",
								color: "#e9a971",
								textAlign: "left",
								top: "100%",
								left: 0,
								marginBottom: "5px",
								display: "flex",
								alignItems:centercenter
							}}
								><span style={{ display: "flex", fontSize: "12px", alignItems: "center", marginRight:"4px" }}

							dangerouslySetInnerHTML={{ __html: warning }}
						/>
								{translate("Value must be between 300px and 1200px.")}
						</Typography>
					)}


					{/* Height Control */}
					{/* <Box className="qadpt-control-box">
						<Typography className="qadpt-control-label">Height</Typography>
						<TextField
							variant="outlined"
							value={`${height}`}
							fullWidth
							margin="dense"
							size="small"
							className="qadpt-control-input"
							onChange={(e) => {
								setHeight(parseInt(e.target.value) || 0);
							}}
							InputProps={{
								endAdornment: "px",
							}}
						/>
					</Box> */}

					{/* Padding Control */}
					<Box className="qadpt-control-box">
							<Typography className="qadpt-control-label">{translate("Padding")}</Typography>
							<div>
						<TextField
							variant="outlined"
							value={`${Annpadding}`}
							fullWidth

							size="small"
							className="qadpt-control-input"
							onChange={(e) => {
								const inputValue = parseInt(e.target.value) || 0;

								// Validate padding between 0px and 20px
								if (inputValue < 0 || inputValue > 20) {
									setPaddingError(true);
								} else {
									setPaddingError(false);
								}

								setAnnPadding(inputValue.toString());
							}}
							InputProps={{
								endAdornment: "px",
								sx: {

									"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
									"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
									"& fieldset":{border:"none"},

								},
							}}
							error={paddingError}
							/>
							</div>
					</Box>
					{paddingError && (
						<Typography
						style={{
							fontSize: "12px",
							color: "#e9a971",
							textAlign: "left",
							top: "100%",
							left: 0,
							marginBottom: "5px",
							display: "flex",
							alignItems:centercenter
						}}
							><span style={{ display: "flex", fontSize: "12px", alignItems: "center", marginRight:"4px" }}

						dangerouslySetInnerHTML={{ __html: warning }}
					/>
								{translate("Value must be between 0px and 20px.")}
						</Typography>
					)}

					{/* Border Radius Control */}
					<Box className="qadpt-control-box">
							<Typography className="qadpt-control-label">{translate("Border Radius")}</Typography>
							<div>
						<TextField
							variant="outlined"
							value={`${borderRadius}`}
							fullWidth

							size="small"
							className="qadpt-control-input"
							onChange={(e) => {
								const inputValue = parseInt(e.target.value) || 0;

								// Validate border radius between 0px and 20px
								if (inputValue < 0 || inputValue > 20) {
									setBorderRadiusError(true);
									// Set border radius to closest valid value
									// setBorderRadius(inputValue < 0 ? 0 : 20);
								} else {
									setBorderRadiusError(false);
								}
									setBorderRadius(inputValue);

							}}
							InputProps={{
								endAdornment: "px",
								sx: {

									"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
									"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
									"& fieldset":{border:"none"},

								},
									}}
									error={borderRadiusError}
							/>

									</div>
					</Box>
					{borderRadiusError && (
						<Typography
						style={{
							fontSize: "12px",
							color: "#e9a971",
							textAlign: "left",
							top: "100%",
							left: 0,
							marginBottom: "5px",
							display: "flex",
							alignItems:centercenter
						}}
							><span style={{ display: "flex", fontSize: "12px", alignItems: "center", marginRight:"4px" }}

						dangerouslySetInnerHTML={{ __html: warning }}
					/>
								{translate("Value must be between 0px and 20px.")}
						</Typography>
					)}
					<Box className="qadpt-control-box">
							<Typography className="qadpt-control-label">{translate("Border Size")}</Typography>
							<div>
						<TextField
							variant="outlined"
							value={`${AnnborderSize}`}
							fullWidth

							size="small"
							className="qadpt-control-input"
							onChange={(e) => {
								const inputValue = parseInt(e.target.value) || 0;

								// Validate border size between 0px and 10px
								if (inputValue < 0 || inputValue > 10) {
									setBorderSizeError(true);
								} else {
									setBorderSizeError(false);
								}

								setAnnBorderSize(inputValue);
							}}
							InputProps={{
								endAdornment: "px",
								sx: {

									"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
									"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
									"& fieldset":{border:"none"},

								},
							}}
							error={borderSizeError}
							/>
								</div>
					</Box>
					{borderSizeError && (
						<Typography
						style={{
							fontSize: "12px",
							color: "#e9a971",
							textAlign: "left",
							top: "100%",
							left: 0,
							marginBottom: "5px",
							display: "flex",
							alignItems:centercenter
						}}
							><span style={{ display: "flex", fontSize: "12px", alignItems: "center", marginRight:"4px" }}

						dangerouslySetInnerHTML={{ __html: warning }}
					/>
								{translate("Value must be between 0px and 10px.")}
						</Typography>
					)}
					{/* Zindex value Control */}
					{/* <Box className="qadpt-control-box">
						<Typography className="qadpt-control-label">Z-Index</Typography>
						<TextField
							variant="outlined"
							value={`${zindeex}`}
							fullWidth
							margin="dense"
							size="small"
							className="qadpt-control-input"
							onChange={(e) => {
								setZindeex(parseInt(e.target.value) || 0);
							}}
						/>
					</Box> */}

					{/* Border Color Control */}
					<Box className="qadpt-control-box">
							<Typography className="qadpt-control-label">{translate("Border")}</Typography>
							<div>
						<input
							type="color"
							value={borderColor}
							onChange={handleBorderColorChange}
							className="qadpt-color-input"
								/>
								</div>
					</Box>

					{/* Background Color Control */}
					<Box className="qadpt-control-box">
							<Typography className="qadpt-control-label">{translate("Background")}</Typography>
							<div>
						<input
							type="color"
							value={backgroundColor}
							onChange={handleBackgroundColorChange}
							className="qadpt-color-input"
								/>
								</div>
					</Box>


					</div>
					</div>
				<div className="qadpt-drawerFooter">
						<Button
							variant="contained"
							onClick={handleApplyChanges}
							className={`qadpt-btn ${widthError || paddingError || borderRadiusError || borderSizeError ? "disabled" : ""}`}
							disabled={widthError || paddingError || borderRadiusError || borderSizeError}
						>
						{translate("Apply")}
						</Button>
					</div>
			</div>

		</div>
		//</Draggable>
	);
};

export default CanvasSettings;
