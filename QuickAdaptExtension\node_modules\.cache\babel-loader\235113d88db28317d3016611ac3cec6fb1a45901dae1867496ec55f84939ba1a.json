{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideBanners\\\\selectedpopupfields\\\\ButtonSettings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useMemo, useState } from \"react\";\nimport { Box, IconButton, Typography, Button, FormControl, Select, MenuItem, TextField, ToggleButton, ToggleButtonGroup } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport { AlignHorizontalLeft as TopLeftIcon, AlignHorizontalCenter as TopCenterIcon, AlignHorizontalRight as TopRightIcon, AlignVerticalTop as MiddleLeftIcon, AlignVerticalCenter as MiddleCenterIcon, AlignVerticalBottom as MiddleRightIcon, AlignHorizontalLeft as BottomLeftIcon, AlignHorizontalCenter as BottomCenterIcon, AlignHorizontalRight as BottomRightIcon } from \"@mui/icons-material\";\nimport \"../../guideDesign/Canvas.module.scss\";\nimport useDrawerStore from \"../../../store/drawerStore\";\n// import Draggable from \"react-draggable\";\n\nimport { useTranslation } from \"react-i18next\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ButtonSettings = () => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    buttonsContainer,\n    cloneButtonContainer,\n    updateButton,\n    addNewButton,\n    deleteButton,\n    deleteButtonContainer,\n    updateContainer,\n    setSettingAnchorEl,\n    selectedTemplate,\n    selectedTemplateTour,\n    setSelectedTemplate,\n    buttonProperty,\n    setButtonProperty,\n    setSelectActions,\n    currentButtonName,\n    setCurrentButtonName,\n    targetURL,\n    setTargetURL,\n    selectedInteraction,\n    setSelectedInteraction,\n    openInteractionList,\n    setOpenInteractionList,\n    selectedTab,\n    setSelectedTab,\n    guideListByOrg,\n    getGuildeListByOrg,\n    loading,\n    setLoading,\n    updateButtonInteraction,\n    updateButtonAction,\n    settingAnchorEl,\n    btnBgColor,\n    btnTextColor,\n    btnBorderColor,\n    setBtnBgColor,\n    setBtnTextColor,\n    setBtnBorderColor,\n    getCurrentButtonInfo,\n    cuntainerId,\n    setCuntainerId,\n    buttonId,\n    setButtonId,\n    btnname,\n    setBtnName,\n    setIsUnSavedChanges,\n    createWithAI\n  } = useDrawerStore(state => state);\n\n  // State for dynamic positioning\n  const [panelPosition, setPanelPosition] = useState({\n    top: 0,\n    left: 0,\n    position: 'fixed',\n    zIndex: 999999\n  });\n  const [selectedActions, setSelectedActions] = useState({\n    value: \"close\",\n    // Default action\n    targetURL: \"\",\n    // Default empty target URL\n    tab: \"same-tab\" // Default tab (same-tab)\n  });\n  const [borderColor, setBorderColor] = useState(\"#000000\");\n  const [backgroundColor, setBackgroundColor] = useState(\"#FFFFFF\");\n  const [isOpen, setIsOpen] = useState(true);\n  const [selectedPosition, setSelectedPosition] = useState(\"\");\n  const [url, setUrl] = useState(\"\");\n  const [action, setAction] = useState(\"close\");\n  const [openInNewTab, setOpenInNewTab] = useState(true);\n  const userInfo = localStorage.getItem(\"userInfo\");\n  const userInfoObj = JSON.parse(userInfo || \"{}\");\n  const orgDetails = JSON.parse(userInfoObj.orgDetails || \"{}\");\n  const organizationId = orgDetails.OrganizationId;\n  const defaultButtonColors = {\n    backgroundColor: \"#5F9EA0\",\n    borderColor: \"#70afaf\",\n    color: \"#ffffff\"\n  };\n  const [tempColors, setTempColors] = useState(defaultButtonColors);\n  const [colors, setColors] = useState({\n    fill: \"#4CAF50\",\n    border: \"#4CAF50\",\n    text: \"#ffffff\"\n  });\n\n  // State variables for validation errors\n  const [buttonNameError, setButtonNameError] = useState(\"\");\n  const [targetURLError, setTargetURLError] = useState(\"\");\n\n  // Function to get GuidePopUp Dialog container position\n  const getGuidePopupPosition = () => {\n    const element = document.querySelector('.qadpt-guide-popup .MuiDialog-paper') || document.getElementById('guide-popup');\n    if (element) {\n      const rect = element.getBoundingClientRect();\n      return {\n        top: rect.top,\n        left: rect.left,\n        width: rect.width,\n        height: rect.height\n      };\n    }\n    return null;\n  };\n\n  // Function to calculate button settings panel position\n  const getButtonSettingsPosition = () => {\n    const guidePopupPos = getGuidePopupPosition();\n    if (!guidePopupPos) return panelPosition;\n    const viewportHeight = window.innerHeight;\n    const viewportWidth = window.innerWidth;\n    const panelHeight = 400; // Approximate height of button settings panel\n    const panelWidth = 300; // Approximate width of button settings panel\n    const minGap = 10; // Minimum gap between GuidePopup and panel\n\n    // Try to position on the right side of GuidePopup (preferred)\n    let leftPosition = guidePopupPos.left + guidePopupPos.width + minGap;\n    let topPosition = guidePopupPos.top + guidePopupPos.height / 2 - panelHeight / 2;\n\n    // Check if there's enough space on the right side\n    const hasSpaceOnRight = leftPosition + panelWidth <= viewportWidth - minGap;\n    if (!hasSpaceOnRight) {\n      // Fallback: Position on top of GuidePopup\n      topPosition = guidePopupPos.top + minGap;\n      leftPosition = guidePopupPos.left + guidePopupPos.width / 2 - panelWidth / 2;\n    }\n\n    // Ensure panel stays within viewport boundaries\n    // Vertical constraints\n    topPosition = Math.max(minGap, Math.min(topPosition, viewportHeight - panelHeight - minGap));\n\n    // Horizontal constraints\n    leftPosition = Math.max(minGap, Math.min(leftPosition, viewportWidth - panelWidth - minGap));\n    return {\n      top: topPosition,\n      left: leftPosition,\n      position: 'fixed',\n      zIndex: 999999\n    };\n  };\n\n  // Update panel position\n  const updatePanelPosition = () => {\n    setPanelPosition(getButtonSettingsPosition());\n  };\n  useEffect(() => {\n    const handleSelectButton = (containerId, buttonId) => {\n      const selectedButton = getCurrentButtonInfo(containerId, buttonId);\n      if (selectedButton) {\n        setCurrentButtonName(selectedButton.title); // Save the initial button name\n        setTargetURL(selectedButton.targetURL || \"\");\n        setTempColors({\n          backgroundColor: selectedButton.bgColor || defaultButtonColors.backgroundColor,\n          borderColor: selectedButton.borderColor || defaultButtonColors.borderColor,\n          color: selectedButton.textColor || defaultButtonColors.color\n        });\n        setSelectedActions({\n          value: selectedButton.selectedActions,\n          // Use the actual saved action value\n          targetURL: selectedButton.targetURL || \"\",\n          // Use the saved target URL\n          tab: selectedButton.tab || \"same-tab\" // Use the saved tab value\n        });\n        setSelectedTab(selectedButton.tab || \"same-tab\"); // Also set the selectedTab state\n      }\n    };\n    handleSelectButton(settingAnchorEl.containerId, settingAnchorEl.buttonId);\n  }, [settingAnchorEl.containerId, settingAnchorEl.buttonId]);\n  const positions = [{\n    label: translate(\"Top Left\", {\n      defaultValue: \"Top Left\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(TopLeftIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 71\n    }, this),\n    value: \"top-left\"\n  }, {\n    label: translate(\"Top Center\", {\n      defaultValue: \"Top Center\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(TopCenterIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 75\n    }, this),\n    value: \"top-center\"\n  }, {\n    label: translate(\"Top Right\", {\n      defaultValue: \"Top Right\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(TopRightIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 73\n    }, this),\n    value: \"top-right\"\n  }, {\n    label: translate(\"Middle Left\", {\n      defaultValue: \"Middle Left\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(MiddleLeftIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 77\n    }, this),\n    value: \"middle-left\"\n  }, {\n    label: translate(\"Middle Center\", {\n      defaultValue: \"Middle Center\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(MiddleCenterIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 81\n    }, this),\n    value: \"middle-center\"\n  }, {\n    label: translate(\"Middle Right\", {\n      defaultValue: \"Middle Right\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(MiddleRightIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 79\n    }, this),\n    value: \"middle-right\"\n  }, {\n    label: translate(\"Bottom Left\", {\n      defaultValue: \"Bottom Left\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(BottomLeftIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 77\n    }, this),\n    value: \"bottom-left\"\n  }, {\n    label: translate(\"Bottom Center\", {\n      defaultValue: \"Bottom Center\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(BottomCenterIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 81\n    }, this),\n    value: \"bottom-center\"\n  }, {\n    label: translate(\"Bottom Right\", {\n      defaultValue: \"Bottom Right\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(BottomRightIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 79\n    }, this),\n    value: \"bottom-right\"\n  }];\n  const curronButtonInfo = useMemo(() => {\n    const result = getCurrentButtonInfo(settingAnchorEl.containerId, settingAnchorEl.buttonId);\n    setCurrentButtonName(result.title);\n    return result;\n  }, [settingAnchorEl.containerId, settingAnchorEl.buttonId]);\n  const handlePositionClick = position => {\n    setSelectedPosition(position);\n  };\n  const handleClose = () => {\n    setButtonProperty(false);\n  };\n  if (!isOpen) return null;\n\n  // const handleColorChange = (type: any, color: any) => {\n  // \tsetColors((prevColors) => ({ ...prevColors, [type]: color }));\n  // };\n\n  const handleColorChange = (e, targetName) => {\n    const value = e.target.value;\n    setTempColors(prev => ({\n      ...prev,\n      [targetName]: value\n    }));\n  };\n  const handleChangeActions = e => {\n    const value = e.target.value; // Casting to TInteractionValue\n    setSelectedActions({\n      value: value,\n      // Ensure that selectedActions.value is of type TInteractionValue\n      targetURL: targetURL,\n      tab: selectedTab // Ensure tab is a valid value\n    });\n  };\n  const handleChangeTabs = event => {\n    setSelectedTab(event.target.value);\n  };\n  const handleCloseInteraction = () => {\n    setOpenInteractionList(false);\n  };\n  const handleOpenInteraction = () => {\n    setOpenInteractionList(true);\n    if (organizationId && !guideListByOrg.length) {\n      (async () => {\n        setLoading(true);\n        await getGuildeListByOrg(organizationId);\n        setLoading(false);\n      })();\n    }\n  };\n  const validateTargetURL = url => {\n    if (selectedActions.value === \"open-url\") {\n      if (!url) {\n        return \"URL is required\";\n      }\n      try {\n        new URL(url);\n        return \"\";\n      } catch (error) {\n        return \"Invalid URL\";\n      }\n    }\n    return \"\";\n  };\n  const handleApplyChanges = (containerId, buttonId) => {\n    setCuntainerId(containerId);\n    setButtonId(buttonId);\n    const targetURLError = validateTargetURL(targetURL);\n    setTargetURLError(targetURLError);\n    if (targetURLError) {\n      return;\n    }\n\n    // Retain the previously saved button name if the field is empty\n    const buttonNameToUpdate = !currentButtonName || !currentButtonName.trim() ? getCurrentButtonInfo(containerId, buttonId).title : currentButtonName;\n    setCurrentButtonName(buttonNameToUpdate);\n\n    // Update button properties\n    updateButton(containerId, buttonId, \"style\", tempColors);\n    updateButton(containerId, buttonId, \"name\", buttonNameToUpdate);\n\n    // Update button actions with the complete action object\n    const actionToSave = {\n      value: selectedActions.value,\n      targetURL: targetURL,\n      tab: selectedTab,\n      interaction: selectedInteraction\n    };\n    updateButton(containerId, buttonId, \"actions\", actionToSave);\n    updateButtonAction(containerId, buttonId, actionToSave);\n    setSettingAnchorEl({\n      containerId: \"\",\n      buttonId: \"\",\n      value: null\n    });\n    handleClose();\n    setIsUnSavedChanges(true);\n    // Clear selection\n    //setSettingAnchorEl({ containerId: \"\", buttonId: \"\", value: null });\n  };\n  const handleURLChange = e => {\n    const newURL = e.target.value;\n    setTargetURL(newURL);\n    setSelectedActions({\n      value: selectedActions.value,\n      targetURL: newURL,\n      tab: selectedTab\n    });\n  };\n  return (\n    /*#__PURE__*/\n    //<Draggable>\n    _jsxDEV(\"div\", {\n      className: \"qadpt-designpopup qadpt-banbtnprop\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-design-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-title\",\n            children: translate(\"Properties\", {\n              defaultValue: \"Properties\"\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            \"aria-label\": \"close\",\n            onClick: () => handleClose(),\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 6\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-canblock qadpt-btnpro\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-controls\",\n            children: [/*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              sx: {\n                marginBottom: \"5px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                sx: {\n                  fontSize: \"14px\",\n                  fontWeight: \"bold\",\n                  my: \"5px\",\n                  textAlign: \"left\"\n                },\n                children: translate(\"Button Name\", {\n                  defaultValue: \"Button Name\"\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 6\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                value: currentButtonName,\n                size: \"small\",\n                sx: {\n                  mb: \"5px\",\n                  border: \"1px solid #ccc\",\n                  borderRadius: \"4px\",\n                  \"& .MuiOutlinedInput-root\": {\n                    height: \"35px\",\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none !important\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none !important\"\n                    }\n                  },\n                  \"& .MuiOutlinedInput-notchedOutline\": {\n                    border: \"none !important\"\n                  }\n                },\n                placeholder: translate(\"Button Name\", {\n                  defaultValue: \"Button Name\"\n                }),\n                onChange: e => {\n                  setCurrentButtonName(e.target.value);\n                  // setBtnName(e.target.value);\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 6\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                sx: {\n                  fontSize: \"14px\",\n                  fontWeight: \"bold\",\n                  mb: \"5px\",\n                  textAlign: \"left\"\n                },\n                children: translate(\"Button Action\", {\n                  defaultValue: \"Button Action\"\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 6\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: selectedActions.value,\n                onChange: handleChangeActions,\n                sx: {\n                  mb: \"5px\",\n                  border: \"1px solid #ccc\",\n                  borderRadius: \"4px\",\n                  textAlign: \"left\",\n                  \"& .MuiSelect-select\": {\n                    padding: \"8px\"\n                  },\n                  \"& .MuiOutlinedInput-root\": {\n                    height: \"35px\",\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none !important\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none !important\"\n                    }\n                  },\n                  \"& .MuiOutlinedInput-notchedOutline\": {\n                    border: \"none !important\"\n                  }\n                },\n                MenuProps: {\n                  PaperProps: {}\n                },\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"close\",\n                  children: translate(\"Close\", {\n                    defaultValue: \"Close\"\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 7\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"open-url\",\n                  children: translate(\"Open URL\", {\n                    defaultValue: \"Open URL\"\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 7\n                }, this), selectedTemplate === \"Tour\" || selectedTemplate !== \"Banner\" && selectedTemplate !== \"Hotspot\" ? [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"Next\",\n                  children: translate(\"Next\", {\n                    defaultValue: \"Next\"\n                  })\n                }, \"next\", false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 9\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"Previous\",\n                  children: translate(\"Previous\", {\n                    defaultValue: \"Previous\"\n                  })\n                }, \"previous\", false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 9\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"Restart\",\n                  children: translate(\"Restart\", {\n                    defaultValue: \"Restart\"\n                  })\n                }, \"restart\", false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 9\n                }, this)] : []]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 6\n              }, this), selectedActions.value === \"open-url\" ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    fontSize: \"14px\",\n                    fontWeight: \"bold\",\n                    my: \"5px\",\n                    textAlign: \"left\"\n                  },\n                  children: translate(\"Enter URL\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 8\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  value: targetURL,\n                  size: \"small\",\n                  placeholder: \"https://quixy.com\",\n                  onChange: e => {\n                    const newURL = e.target.value;\n                    setTargetURL(newURL); // Update the `targetURL` state with the new value\n                    handleURLChange(e); // Update the selectedButton.targetURL with the new value\n                    setTargetURLError(validateTargetURL(newURL));\n                  },\n                  error: !!targetURLError,\n                  helperText: targetURLError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 8\n                }, this), /*#__PURE__*/_jsxDEV(ToggleButtonGroup, {\n                  value: selectedTab,\n                  onChange: handleChangeTabs,\n                  exclusive: true,\n                  \"aria-label\": translate(\"open in tab\", {\n                    defaultValue: \"open in tab\"\n                  }),\n                  sx: {\n                    gap: \"5px\",\n                    marginY: \"5px\",\n                    height: \"35px\"\n                  },\n                  children: [\"new-tab\", \"same-tab\"].map(tab => {\n                    return /*#__PURE__*/_jsxDEV(ToggleButton, {\n                      value: tab,\n                      \"aria-label\": \"new tab\",\n                      sx: {\n                        border: \"1px solid #7EA8A5\",\n                        textTransform: \"capitalize\",\n                        color: \"#000\",\n                        borderRadius: \"4px\",\n                        flex: 1,\n                        padding: \"0 !important\",\n                        \"&.Mui-selected\": {\n                          backgroundColor: \"var(--border-color)\",\n                          color: \"#000\",\n                          border: \"2px solid #7EA8A5\"\n                        },\n                        \"&:hover\": {\n                          backgroundColor: \"#f5f5f5\"\n                        },\n                        \"&:last-child\": {\n                          borderLeft: \"1px solid var(--primarycolor) !important\"\n                        }\n                      },\n                      children: tab\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 480,\n                      columnNumber: 11\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 8\n                }, this)]\n              }, void 0, true) : null]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 5\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              sx: {\n                borderRadius: \"5px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate(\"Background\", {\n                  defaultValue: \"Background\"\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"color\",\n                  value: tempColors.backgroundColor,\n                  onChange: e => handleColorChange(e, \"backgroundColor\"),\n                  className: \"qadpt-color-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 561,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 6\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              sx: {\n                borderRadius: \"5px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate(\"Border\", {\n                  defaultValue: \"Border\"\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"color\",\n                  value: tempColors.borderColor,\n                  onChange: e => handleColorChange(e, \"borderColor\"),\n                  className: \"qadpt-color-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 576,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 6\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              sx: {\n                borderRadius: \"5px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate(\"Text\", {\n                  defaultValue: \"Text\"\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"color\",\n                  value: tempColors.color,\n                  onChange: e => handleColorChange(e, \"color\"),\n                  className: \"qadpt-color-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 6\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 5\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-drawerFooter\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: () => handleApplyChanges(settingAnchorEl.containerId, settingAnchorEl.buttonId),\n            className: \"qadpt-btn\",\n            children: translate(\"Apply\", {\n              defaultValue: \"Apply\"\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 6\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 602,\n          columnNumber: 5\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 4\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 3\n    }, this)\n    //</Draggable>\n  );\n};\n_s(ButtonSettings, \"AhG6+PKJiTepqTsEz8XENJuo3bA=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = ButtonSettings;\nexport default ButtonSettings;\nvar _c;\n$RefreshReg$(_c, \"ButtonSettings\");", "map": {"version": 3, "names": ["React", "useEffect", "useMemo", "useState", "Box", "IconButton", "Typography", "<PERSON><PERSON>", "FormControl", "Select", "MenuItem", "TextField", "ToggleButton", "ToggleButtonGroup", "CloseIcon", "AlignHorizontalLeft", "TopLeftIcon", "AlignHorizontalCenter", "TopCenterIcon", "AlignHorizontalRight", "TopRightIcon", "AlignVerticalTop", "MiddleLeftIcon", "AlignVerticalCenter", "MiddleCenterIcon", "AlignVerticalBottom", "MiddleRightIcon", "BottomLeftIcon", "BottomCenterIcon", "BottomRightIcon", "useDrawerStore", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ButtonSettings", "_s", "t", "translate", "buttonsContainer", "cloneButtonContainer", "updateButton", "addNewButton", "deleteButton", "deleteButtonContainer", "updateContainer", "setSettingAnchorEl", "selectedTemplate", "selectedTemplateTour", "setSelectedTemplate", "buttonProperty", "setButtonProperty", "setSelectActions", "currentButtonName", "setCurrentButtonName", "targetURL", "setTargetURL", "selectedInteraction", "setSelectedInteraction", "openInteractionList", "setOpenInteractionList", "selectedTab", "setSelectedTab", "guideListByOrg", "getGuildeListByOrg", "loading", "setLoading", "updateButtonInteraction", "updateButtonAction", "settingAnchorEl", "btnBgColor", "btnTextColor", "btnBorderColor", "setBtnBgColor", "setBtnTextColor", "setBtnBorderColor", "getCurrentButtonInfo", "cuntainerId", "setCuntainerId", "buttonId", "setButtonId", "btnname", "setBtnName", "setIsUnSavedChanges", "createWithAI", "state", "panelPosition", "setPanelPosition", "top", "left", "position", "zIndex", "selectedActions", "setSelectedActions", "value", "tab", "borderColor", "setBorderColor", "backgroundColor", "setBackgroundColor", "isOpen", "setIsOpen", "selectedPosition", "setSelectedPosition", "url", "setUrl", "action", "setAction", "openInNewTab", "setOpenInNewTab", "userInfo", "localStorage", "getItem", "userInfoObj", "JSON", "parse", "orgDetails", "organizationId", "OrganizationId", "defaultButtonColors", "color", "tempColors", "setTempColors", "colors", "setColors", "fill", "border", "text", "buttonNameError", "setButtonNameError", "targetURLError", "setTargetURLError", "getGuidePopupPosition", "element", "document", "querySelector", "getElementById", "rect", "getBoundingClientRect", "width", "height", "getButtonSettingsPosition", "guidePopupPos", "viewportHeight", "window", "innerHeight", "viewportWidth", "innerWidth", "panelHeight", "panelWidth", "minGap", "leftPosition", "topPosition", "hasSpaceOnRight", "Math", "max", "min", "updatePanelPosition", "handleSelectButton", "containerId", "<PERSON><PERSON><PERSON><PERSON>", "title", "bgColor", "textColor", "positions", "label", "defaultValue", "icon", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "curronButtonInfo", "result", "handlePositionClick", "handleClose", "handleColorChange", "e", "targetName", "target", "prev", "handleChangeActions", "handleChangeTabs", "event", "handleCloseInteraction", "handleOpenInteraction", "length", "validateTargetURL", "URL", "error", "handleApplyChanges", "buttonNameToUpdate", "trim", "actionToSave", "interaction", "handleURLChange", "newURL", "className", "children", "size", "onClick", "fullWidth", "sx", "marginBottom", "fontWeight", "my", "textAlign", "mb", "borderRadius", "placeholder", "onChange", "padding", "MenuProps", "PaperProps", "helperText", "exclusive", "gap", "marginY", "map", "textTransform", "flex", "borderLeft", "type", "variant", "_c", "$RefreshReg$"], "sources": ["E:/Code/quickadapt/QuickAdaptExtension/src/components/guideBanners/selectedpopupfields/ButtonSettings.tsx"], "sourcesContent": ["import React, { useEffect, useMemo, useState } from \"react\";\r\nimport {\r\n\tDialog,\r\n\tDialogContent,\r\n\tuseMediaQ<PERSON>y,\r\n\tuseTheme,\r\n\tBox,\r\n\tIconButton,\r\n\tPopover,\r\n\tTypography,\r\n\tButton,\r\n\tFormControl,\r\n\tSelect,\r\n\tMenuItem,\r\n\tTextField,\r\n\tSelectChangeEvent,\r\n\tRadioGroup,\r\n\tRadio,\r\n\tFormControlLabel,\r\n\tInput,\r\n\tToggleButton,\r\n\tToggleButtonGroup,\r\n\tAutocomplete,\r\n\tCircularProgress,\r\n\tDialogTitle,\r\n\tTooltip,\r\n} from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport {\r\n\tAlignHorizontalLeft as TopLeftIcon,\r\n\tAlignHorizontalCenter as TopCenterIcon,\r\n\tAlignHorizontalRight as TopRightIcon,\r\n\tAlignVerticalTop as MiddleLeftIcon,\r\n\tAlignVerticalCenter as MiddleCenterIcon,\r\n\tAlignVerticalBottom as MiddleRightIcon,\r\n\tAlignHorizontalLeft as BottomLeftIcon,\r\n\tAlignHorizontalCenter as BottomCenterIcon,\r\n\tAlignHorizontalRight as BottomRightIcon,\r\n} from \"@mui/icons-material\";\r\nimport \"../../guideDesign/Canvas.module.scss\";\r\nimport useDrawerStore, { TButtonAction, TInteractionValue } from \"../../../store/drawerStore\";\r\n// import Draggable from \"react-draggable\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport zIndex from \"@mui/material/styles/zIndex\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\nconst ButtonSettings = () => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tbuttonsContainer,\r\n\t\tcloneButtonContainer,\r\n\t\tupdateButton,\r\n\t\taddNewButton,\r\n\t\tdeleteButton,\r\n\t\tdeleteButtonContainer,\r\n\t\tupdateContainer,\r\n\t\tsetSettingAnchorEl,\r\n\t\tselectedTemplate,\r\n\t\tselectedTemplateTour,\r\n\t\tsetSelectedTemplate,\r\n\t\tbuttonProperty,\r\n\t\tsetButtonProperty,\r\n\t\tsetSelectActions,\r\n\t\tcurrentButtonName,\r\n\t\tsetCurrentButtonName,\r\n\t\ttargetURL,\r\n\t\tsetTargetURL,\r\n\t\tselectedInteraction,\r\n\t\tsetSelectedInteraction,\r\n\t\topenInteractionList,\r\n\t\tsetOpenInteractionList,\r\n\t\tselectedTab,\r\n\t\tsetSelectedTab,\r\n\t\tguideListByOrg,\r\n\t\tgetGuildeListByOrg,\r\n\t\tloading,\r\n\t\tsetLoading,\r\n\t\tupdateButtonInteraction,\r\n\t\tupdateButtonAction,\r\n\t\tsettingAnchorEl,\r\n\t\tbtnBgColor,\r\n\t\tbtnTextColor,\r\n\t\tbtnBorderColor,\r\n\t\tsetBtnBgColor,\r\n\t\tsetBtnTextColor,\r\n\t\tsetBtnBorderColor,\r\n\t\tgetCurrentButtonInfo,\r\n\t\tcuntainerId,\r\n\t\tsetCuntainerId,\r\n\t\tbuttonId,\r\n\t\tsetButtonId,\r\n\t\tbtnname,\r\n\t\tsetBtnName,\r\n\t\tsetIsUnSavedChanges,\r\n\t\tcreateWithAI\r\n\t} = useDrawerStore((state) => state);\r\n\r\n\t// State for dynamic positioning\r\n\tconst [panelPosition, setPanelPosition] = useState({\r\n\t\ttop: 0,\r\n\t\tleft: 0,\r\n\t\tposition: 'fixed' as const,\r\n\t\tzIndex: 999999\r\n\t});\r\n\tconst [selectedActions, setSelectedActions] = useState<TButtonAction>({\r\n\t\tvalue: \"close\", // Default action\r\n\t\ttargetURL: \"\", // Default empty target URL\r\n\t\ttab: \"same-tab\", // Default tab (same-tab)\r\n\t});\r\n\tconst [borderColor, setBorderColor] = useState(\"#000000\");\r\n\tconst [backgroundColor, setBackgroundColor] = useState(\"#FFFFFF\");\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst [selectedPosition, setSelectedPosition] = useState(\"\");\r\n\tconst [url, setUrl] = useState(\"\");\r\n\tconst [action, setAction] = useState(\"close\");\r\n\tconst [openInNewTab, setOpenInNewTab] = useState(true);\r\n\tconst userInfo = localStorage.getItem(\"userInfo\");\r\n\tconst userInfoObj = JSON.parse(userInfo || \"{}\");\r\n\tconst orgDetails = JSON.parse(userInfoObj.orgDetails || \"{}\");\r\n\tconst organizationId = orgDetails.OrganizationId;\r\n\tconst defaultButtonColors = {\r\n\t\tbackgroundColor: \"#5F9EA0\",\r\n\t\tborderColor: \"#70afaf\",\r\n\t\tcolor: \"#ffffff\",\r\n\t};\r\n\tconst [tempColors, setTempColors] = useState(defaultButtonColors);\r\n\tconst [colors, setColors] = useState({\r\n\t\tfill: \"#4CAF50\",\r\n\t\tborder: \"#4CAF50\",\r\n\t\ttext: \"#ffffff\",\r\n\t});\r\n\r\n\t// State variables for validation errors\r\n\tconst [buttonNameError, setButtonNameError] = useState(\"\");\r\n\tconst [targetURLError, setTargetURLError] = useState(\"\");\r\n\r\n\t// Function to get GuidePopUp Dialog container position\r\n\tconst getGuidePopupPosition = () => {\r\n\t\tconst element = document.querySelector('.qadpt-guide-popup .MuiDialog-paper') ||\r\n\t\t\t\t\t\tdocument.getElementById('guide-popup');\r\n\r\n\t\tif (element) {\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\treturn {\r\n\t\t\t\ttop: rect.top,\r\n\t\t\t\tleft: rect.left,\r\n\t\t\t\twidth: rect.width,\r\n\t\t\t\theight: rect.height\r\n\t\t\t};\r\n\t\t}\r\n\t\treturn null;\r\n\t};\r\n\r\n\t// Function to calculate button settings panel position\r\n\tconst getButtonSettingsPosition = () => {\r\n\t\tconst guidePopupPos = getGuidePopupPosition();\r\n\t\tif (!guidePopupPos) return panelPosition;\r\n\r\n\t\tconst viewportHeight = window.innerHeight;\r\n\t\tconst viewportWidth = window.innerWidth;\r\n\t\tconst panelHeight = 400; // Approximate height of button settings panel\r\n\t\tconst panelWidth = 300; // Approximate width of button settings panel\r\n\t\tconst minGap = 10; // Minimum gap between GuidePopup and panel\r\n\r\n\t\t// Try to position on the right side of GuidePopup (preferred)\r\n\t\tlet leftPosition = guidePopupPos.left + guidePopupPos.width + minGap;\r\n\t\tlet topPosition = guidePopupPos.top + (guidePopupPos.height / 2) - (panelHeight / 2);\r\n\r\n\t\t// Check if there's enough space on the right side\r\n\t\tconst hasSpaceOnRight = leftPosition + panelWidth <= viewportWidth - minGap;\r\n\r\n\t\tif (!hasSpaceOnRight) {\r\n\t\t\t// Fallback: Position on top of GuidePopup\r\n\t\t\ttopPosition = guidePopupPos.top + minGap;\r\n\t\t\tleftPosition = guidePopupPos.left + (guidePopupPos.width / 2) - (panelWidth / 2);\r\n\t\t}\r\n\r\n\t\t// Ensure panel stays within viewport boundaries\r\n\t\t// Vertical constraints\r\n\t\ttopPosition = Math.max(minGap, Math.min(topPosition, viewportHeight - panelHeight - minGap));\r\n\r\n\t\t// Horizontal constraints\r\n\t\tleftPosition = Math.max(minGap, Math.min(leftPosition, viewportWidth - panelWidth - minGap));\r\n\r\n\t\treturn {\r\n\t\t\ttop: topPosition,\r\n\t\t\tleft: leftPosition,\r\n\t\t\tposition: 'fixed' as const,\r\n\t\t\tzIndex: 999999\r\n\t\t};\r\n\t};\r\n\r\n\t// Update panel position\r\n\tconst updatePanelPosition = () => {\r\n\t\tsetPanelPosition(getButtonSettingsPosition());\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tconst handleSelectButton = (containerId: any, buttonId: any) => {\r\n\t\t\tconst selectedButton = getCurrentButtonInfo(containerId, buttonId);\r\n\t\t\tif (selectedButton) {\r\n\t\t\t\tsetCurrentButtonName(selectedButton.title); // Save the initial button name\r\n\t\t\t\tsetTargetURL(selectedButton.targetURL || \"\");\r\n\t\t\t\tsetTempColors({\r\n\t\t\t\t\tbackgroundColor: selectedButton.bgColor || defaultButtonColors.backgroundColor,\r\n\t\t\t\t\tborderColor: selectedButton.borderColor || defaultButtonColors.borderColor,\r\n\t\t\t\t\tcolor: selectedButton.textColor || defaultButtonColors.color,\r\n\t\t\t\t});\r\n\t\t\t\tsetSelectedActions({\r\n\t\t\t\t\tvalue: selectedButton.selectedActions as TInteractionValue, // Use the actual saved action value\r\n\t\t\t\t\ttargetURL: selectedButton.targetURL || \"\", // Use the saved target URL\r\n\t\t\t\t\ttab: selectedButton.tab || \"same-tab\", // Use the saved tab value\r\n\t\t\t\t});\r\n\t\t\t\tsetSelectedTab(selectedButton.tab || \"same-tab\"); // Also set the selectedTab state\r\n\t\t\t}\r\n\t\t};\r\n\t\thandleSelectButton(settingAnchorEl.containerId, settingAnchorEl.buttonId);\r\n\t}, [settingAnchorEl.containerId, settingAnchorEl.buttonId]);\r\n\r\n\r\n\tconst positions = [\r\n\t\t{ label: translate(\"Top Left\", { defaultValue: \"Top Left\" }), icon: <TopLeftIcon fontSize=\"small\" />, value: \"top-left\" },\r\n\t\t{ label: translate(\"Top Center\", { defaultValue: \"Top Center\" }), icon: <TopCenterIcon fontSize=\"small\" />, value: \"top-center\" },\r\n\t\t{ label: translate(\"Top Right\", { defaultValue: \"Top Right\" }), icon: <TopRightIcon fontSize=\"small\" />, value: \"top-right\" },\r\n\t\t{ label: translate(\"Middle Left\", { defaultValue: \"Middle Left\" }), icon: <MiddleLeftIcon fontSize=\"small\" />, value: \"middle-left\" },\r\n\t\t{ label: translate(\"Middle Center\", { defaultValue: \"Middle Center\" }), icon: <MiddleCenterIcon fontSize=\"small\" />, value: \"middle-center\" },\r\n\t\t{ label: translate(\"Middle Right\", { defaultValue: \"Middle Right\" }), icon: <MiddleRightIcon fontSize=\"small\" />, value: \"middle-right\" },\r\n\t\t{ label: translate(\"Bottom Left\", { defaultValue: \"Bottom Left\" }), icon: <BottomLeftIcon fontSize=\"small\" />, value: \"bottom-left\" },\r\n\t\t{ label: translate(\"Bottom Center\", { defaultValue: \"Bottom Center\" }), icon: <BottomCenterIcon fontSize=\"small\" />, value: \"bottom-center\" },\r\n\t\t{ label: translate(\"Bottom Right\", { defaultValue: \"Bottom Right\" }), icon: <BottomRightIcon fontSize=\"small\" />, value: \"bottom-right\" },\r\n\t];\r\n\r\n\tconst curronButtonInfo = useMemo(() => {\r\n\t\tconst result = getCurrentButtonInfo(settingAnchorEl.containerId, settingAnchorEl.buttonId);\r\n\t\tsetCurrentButtonName(result.title);\r\n\t\treturn result;\r\n\t}, [settingAnchorEl.containerId, settingAnchorEl.buttonId]);\r\n\r\n\tconst handlePositionClick = (position: any) => {\r\n\t\tsetSelectedPosition(position);\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetButtonProperty(false);\r\n\t};\r\n\r\n\tif (!isOpen) return null;\r\n\r\n\t// const handleColorChange = (type: any, color: any) => {\r\n\t// \tsetColors((prevColors) => ({ ...prevColors, [type]: color }));\r\n\t// };\r\n\r\n\r\n\tconst handleColorChange = (e: any, targetName: any) => {\r\n\t\tconst value = e.target.value;\r\n\t\tsetTempColors((prev) => ({\r\n\t\t\t...prev,\r\n\t\t\t[targetName]: value,\r\n\t\t}));\r\n\t};\r\n\r\n\tconst handleChangeActions = (e: SelectChangeEvent) => {\r\n\t\tconst value: TInteractionValue = e.target.value as TInteractionValue; // Casting to TInteractionValue\r\n\t\tsetSelectedActions({\r\n\t\t\tvalue: value, // Ensure that selectedActions.value is of type TInteractionValue\r\n\t\t\ttargetURL: targetURL,\r\n\t\t\ttab: selectedTab as \"same-tab\" | \"new-tab\", // Ensure tab is a valid value\r\n\t\t});\r\n\t};\r\n\tconst handleChangeTabs = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetSelectedTab((event.target as HTMLInputElement).value);\r\n\t};\r\n\tconst handleCloseInteraction = () => {\r\n\t\tsetOpenInteractionList(false);\r\n\t};\r\n\r\n\tconst handleOpenInteraction = () => {\r\n\t\tsetOpenInteractionList(true);\r\n\t\tif (organizationId && !guideListByOrg.length) {\r\n\t\t\t(async () => {\r\n\t\t\t\tsetLoading(true);\r\n\t\t\t\tawait getGuildeListByOrg(organizationId);\r\n\t\t\t\tsetLoading(false);\r\n\t\t\t})();\r\n\t\t}\r\n\t};\r\n\r\n\tconst validateTargetURL = (url: string) => {\r\n\t\tif (selectedActions.value === \"open-url\") {\r\n\t\t\tif (!url) {\r\n\t\t\t\treturn \"URL is required\";\r\n\t\t\t}\r\n\t\t\ttry {\r\n\t\t\t\tnew URL(url);\r\n\t\t\t\treturn \"\";\r\n\t\t\t} catch (error) {\r\n\t\t\t\treturn \"Invalid URL\";\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn \"\";\r\n\t};\r\n\r\n\tconst handleApplyChanges = (containerId: any, buttonId: any) => {\r\n\t\tsetCuntainerId(containerId);\r\n\t\tsetButtonId(buttonId);\r\n\r\n\t\tconst targetURLError = validateTargetURL(targetURL);\r\n\t\tsetTargetURLError(targetURLError);\r\n\r\n\t\tif (targetURLError) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t// Retain the previously saved button name if the field is empty\r\n\t\tconst buttonNameToUpdate = !currentButtonName || !currentButtonName.trim() ? getCurrentButtonInfo(containerId, buttonId).title : currentButtonName;\r\n\t\tsetCurrentButtonName(buttonNameToUpdate);\r\n\r\n\t\t// Update button properties\r\n\t\tupdateButton(containerId, buttonId, \"style\", tempColors);\r\n\t\tupdateButton(containerId, buttonId, \"name\", buttonNameToUpdate);\r\n\r\n\t\t// Update button actions with the complete action object\r\n\t\tconst actionToSave = {\r\n\t\t\tvalue: selectedActions.value,\r\n\t\t\ttargetURL: targetURL,\r\n\t\t\ttab: selectedTab as \"same-tab\" | \"new-tab\",\r\n\t\t\tinteraction: selectedInteraction,\r\n\t\t};\r\n\t\tupdateButton(containerId, buttonId, \"actions\", actionToSave);\r\n\t\tupdateButtonAction(containerId, buttonId, actionToSave);\r\n\t\tsetSettingAnchorEl({ containerId: \"\", buttonId: \"\", value: null });\r\n\t\thandleClose();\r\n\t\tsetIsUnSavedChanges(true);\r\n\t\t// Clear selection\r\n\t\t//setSettingAnchorEl({ containerId: \"\", buttonId: \"\", value: null });\r\n\t};\r\n\tconst handleURLChange = (e: any) => {\r\n\t\tconst newURL = e.target.value;\r\n\t\tsetTargetURL(newURL);\r\n\t\tsetSelectedActions({\r\n\t\t\tvalue: selectedActions.value,\r\n\t\t\ttargetURL: newURL,\r\n\t\t\ttab: selectedTab as \"same-tab\" | \"new-tab\",\r\n\t\t});\r\n\t}\r\n\r\n\treturn (\r\n\t\t//<Draggable>\r\n\t\t<div\r\n\t\t\tclassName=\"qadpt-designpopup qadpt-banbtnprop\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Properties\", { defaultValue: \"Properties\" })}</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={() => handleClose()}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-canblock qadpt-btnpro\">\r\n\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t<FormControl\r\n\t\t\t\t\tfullWidth\r\n\t\t\t\t\tsx={{ marginBottom: \"5px\" }}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", my: \"5px\", textAlign: \"left\" }}>\r\n\t\t\t\t\t\t\t\t{translate(\"Button Name\", { defaultValue: \"Button Name\" })}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t\t<TextField\r\n\t\t\t\t\t\tvalue={currentButtonName}\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tmb: \"5px\",\r\n\t\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\r\n  \"& .MuiOutlinedInput-root\": {\r\n    height: \"35px\",\r\n    \"&:hover .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n  },\r\n  \"& .MuiOutlinedInput-notchedOutline\": {\r\n    border: \"none !important\",\r\n  }\r\n\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tplaceholder={translate(\"Button Name\", { defaultValue: \"Button Name\" })}\r\n\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\tsetCurrentButtonName(e.target.value);\r\n\t\t\t\t\t\t\t// setBtnName(e.target.value);\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", mb: \"5px\", textAlign: \"left\" }}>\r\n\t\t\t\t\t\t\t\t{translate(\"Button Action\", { defaultValue: \"Button Action\" })}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\tvalue={selectedActions.value}\r\n\t\t\t\t\t\t\tonChange={handleChangeActions}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tmb: \"5px\",\r\n\t\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\t\t\"& .MuiSelect-select\": {\r\n\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\r\n  \"& .MuiOutlinedInput-root\": {\r\n    height: \"35px\",\r\n    \"&:hover .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\r\n      border: \"none !important\",\r\n    },\r\n  },\r\n  \"& .MuiOutlinedInput-notchedOutline\": {\r\n    border: \"none !important\",\r\n  }\r\n\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tMenuProps={{\r\n\t\t\t\t\t\t\tPaperProps: {},\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<MenuItem value=\"close\">{translate(\"Close\", { defaultValue: \"Close\" })}</MenuItem>\r\n\t\t\t\t\t\t<MenuItem value=\"open-url\">{translate(\"Open URL\", { defaultValue: \"Open URL\" })}</MenuItem>\r\n\t\t\r\n\t\t\t\t\t\t{(selectedTemplate === \"Tour\" ||\r\n\t\t\t\t\t\t  (selectedTemplate !== \"Banner\" && selectedTemplate !== \"Hotspot\"))\r\n\t\t\t\t\t\t\t? [\r\n\t\t\t\t\t\t\t\t<MenuItem key=\"next\" value=\"Next\">{translate(\"Next\", { defaultValue: \"Next\" })}</MenuItem>,\r\n\t\t\t\t\t\t\t\t<MenuItem key=\"previous\" value=\"Previous\">{translate(\"Previous\", { defaultValue: \"Previous\" })}</MenuItem>,\r\n\t\t\t\t\t\t\t\t<MenuItem key=\"restart\" value=\"Restart\">{translate(\"Restart\", { defaultValue: \"Restart\" })}</MenuItem>,\r\n\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t\t: []}\r\n\r\n\t\t\t\t\t</Select>\r\n\t\t\t\t\t{selectedActions.value === \"open-url\" ? (\r\n\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", my: \"5px\", textAlign: \"left\" }}>\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"Enter URL\")}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvalue={targetURL}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tplaceholder=\"https://quixy.com\"\r\n\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\tconst newURL = e.target.value;\r\n\t\t\t\t\t\t\t\t\tsetTargetURL(newURL);   // Update the `targetURL` state with the new value\r\n\t\t\t\t\t\t\t\t\thandleURLChange(e);  // Update the selectedButton.targetURL with the new value\r\n\t\t\t\t\t\t\t\t\tsetTargetURLError(validateTargetURL(newURL));\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\terror={!!targetURLError}\r\n\t\t\t\t\t\t\t\thelperText={targetURLError}\r\n\t\t\t\t\t\t\t/>\r\n\r\n\t\t\t\t\t\t\t<ToggleButtonGroup\r\n\t\t\t\t\t\t\t\tvalue={selectedTab}\r\n\t\t\t\t\t\t\t\tonChange={handleChangeTabs}\r\n\t\t\t\t\t\t\t\texclusive\r\n\t\t\t\t\t\t\t\t\t\taria-label={translate(\"open in tab\", { defaultValue: \"open in tab\" })}\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tgap: \"5px\",\r\n\t\t\t\t\t\t\t\t\tmarginY: \"5px\",\r\n\t\t\t\t\t\t\t\t\theight: \"35px\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{[\"new-tab\", \"same-tab\"].map((tab) => {\r\n\t\t\t\t\t\t\t\t\treturn (\r\n\t\t\t\t\t\t\t\t\t\t<ToggleButton\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={tab}\r\n\t\t\t\t\t\t\t\t\t\t\taria-label=\"new tab\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tborder: \"1px solid #7EA8A5\",\r\n\t\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#000\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tflex: 1,\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"0 !important\",\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-selected\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"var(--border-color)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#000\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborder: \"2px solid #7EA8A5\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#f5f5f5\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&:last-child\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborderLeft: \"1px solid var(--primarycolor) !important\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{tab}\r\n\t\t\t\t\t\t\t\t\t\t</ToggleButton>\r\n\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t})}\r\n\t\t\t\t\t\t\t</ToggleButtonGroup>\r\n\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t) : null}\r\n\r\n\t\t\t\t\t{/* {selectedActions.value === \"start-interaction\" ? (\r\n\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t<Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", my: \"5px\" }}>Choose Interaction</Typography>\r\n\r\n\t\t\t\t\t\t\t<Autocomplete\r\n\t\t\t\t\t\t\t\t// sx={{ width: 300 }}\r\n\t\t\t\t\t\t\t\topen={openInteractionList}\r\n\t\t\t\t\t\t\t\tvalue={selectedInteraction}\r\n\t\t\t\t\t\t\t\tonChange={(event, newValue) => {\r\n\t\t\t\t\t\t\t\t\tsetSelectedInteraction(newValue);\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonOpen={handleOpenInteraction}\r\n\t\t\t\t\t\t\t\tonClose={handleCloseInteraction}\r\n\t\t\t\t\t\t\t\tisOptionEqualToValue={(option, value) => {\r\n\t\t\t\t\t\t\t\t\treturn option.guideId === value.guideId;\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tgetOptionLabel={(option) => {\r\n\t\t\t\t\t\t\t\t\treturn option.title;\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tfreeSolo\r\n\t\t\t\t\t\t\t\toptions={guideListByOrg}\r\n\t\t\t\t\t\t\t\tloading={loading}\r\n\t\t\t\t\t\t\t\trenderInput={(params) => (\r\n\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t{...params}\r\n\t\t\t\t\t\t\t\t\t\tplaceholder=\"Select Interaction\"\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\t\t\t\t\t\tinputLabel: {\r\n\t\t\t\t\t\t\t\t\t\t\t\tshrink: false,\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</>\r\n\t\t\t\t\t) : null} */}\r\n\t\t\t\t</FormControl>\r\n\t\t\t\t\t{/* <Typography sx={{ fontSize: \"14px\", fontWeight: \"bold\", mb: \"5px\", textAlign: \"left\" }}>\r\n\t\t\t\t\t\tButton Color\r\n\t\t\t\t\t</Typography> */}\r\n\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\tsx={{ borderRadius: \"5px\" }}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Background\", { defaultValue: \"Background\" })}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={tempColors.backgroundColor}\r\n\t\t\t\t\t\t\tonChange={(e) => handleColorChange(e, \"backgroundColor\")}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\tsx={{ borderRadius: \"5px\" }}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Border\", { defaultValue: \"Border\" })}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={tempColors.borderColor}\r\n\t\t\t\t\t\t\tonChange={(e) => handleColorChange(e, \"borderColor\")}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\tsx={{ borderRadius: \"5px\" }}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Text\", { defaultValue: \"Text\" })}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={tempColors.color}\r\n\t\t\t\t\t\t\tonChange={(e) => handleColorChange(e, \"color\")}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\tonClick={() => handleApplyChanges(settingAnchorEl.containerId, settingAnchorEl.buttonId)}\r\n\t\t\t\t\t\tclassName=\"qadpt-btn\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Apply\", { defaultValue: \"Apply\" })}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t//</Draggable>\r\n\t);\r\n};\r\n\r\nexport default ButtonSettings;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,OAAO;AAC3D,SAKCC,GAAG,EACHC,UAAU,EAEVC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,MAAM,EACNC,QAAQ,EACRC,SAAS,EAMTC,YAAY,EACZC,iBAAiB,QAKX,eAAe;AACtB,OAAOC,SAAS,MAAM,2BAA2B;AACjD,SACCC,mBAAmB,IAAIC,WAAW,EAClCC,qBAAqB,IAAIC,aAAa,EACtCC,oBAAoB,IAAIC,YAAY,EACpCC,gBAAgB,IAAIC,cAAc,EAClCC,mBAAmB,IAAIC,gBAAgB,EACvCC,mBAAmB,IAAIC,eAAe,EACtCX,mBAAmB,IAAIY,cAAc,EACrCV,qBAAqB,IAAIW,gBAAgB,EACzCT,oBAAoB,IAAIU,eAAe,QACjC,qBAAqB;AAC5B,OAAO,sCAAsC;AAC7C,OAAOC,cAAc,MAA4C,4BAA4B;AAC7F;;AAGA,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGR,cAAc,CAAC,CAAC;EACzC,MAAM;IACLS,gBAAgB;IAChBC,oBAAoB;IACpBC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,qBAAqB;IACrBC,eAAe;IACfC,kBAAkB;IAClBC,gBAAgB;IAChBC,oBAAoB;IACpBC,mBAAmB;IACnBC,cAAc;IACdC,iBAAiB;IACjBC,gBAAgB;IAChBC,iBAAiB;IACjBC,oBAAoB;IACpBC,SAAS;IACTC,YAAY;IACZC,mBAAmB;IACnBC,sBAAsB;IACtBC,mBAAmB;IACnBC,sBAAsB;IACtBC,WAAW;IACXC,cAAc;IACdC,cAAc;IACdC,kBAAkB;IAClBC,OAAO;IACPC,UAAU;IACVC,uBAAuB;IACvBC,kBAAkB;IAClBC,eAAe;IACfC,UAAU;IACVC,YAAY;IACZC,cAAc;IACdC,aAAa;IACbC,eAAe;IACfC,iBAAiB;IACjBC,oBAAoB;IACpBC,WAAW;IACXC,cAAc;IACdC,QAAQ;IACRC,WAAW;IACXC,OAAO;IACPC,UAAU;IACVC,mBAAmB;IACnBC;EACD,CAAC,GAAGvD,cAAc,CAAEwD,KAAK,IAAKA,KAAK,CAAC;;EAEpC;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrF,QAAQ,CAAC;IAClDsF,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE,OAAgB;IAC1BC,MAAM,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG3F,QAAQ,CAAgB;IACrE4F,KAAK,EAAE,OAAO;IAAE;IAChBvC,SAAS,EAAE,EAAE;IAAE;IACfwC,GAAG,EAAE,UAAU,CAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG/F,QAAQ,CAAC,SAAS,CAAC;EACzD,MAAM,CAACgG,eAAe,EAAEC,kBAAkB,CAAC,GAAGjG,QAAQ,CAAC,SAAS,CAAC;EACjE,MAAM,CAACkG,MAAM,EAAEC,SAAS,CAAC,GAAGnG,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACoG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrG,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACsG,GAAG,EAAEC,MAAM,CAAC,GAAGvG,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACwG,MAAM,EAAEC,SAAS,CAAC,GAAGzG,QAAQ,CAAC,OAAO,CAAC;EAC7C,MAAM,CAAC0G,YAAY,EAAEC,eAAe,CAAC,GAAG3G,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM4G,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;EACjD,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,IAAI,IAAI,CAAC;EAChD,MAAMM,UAAU,GAAGF,IAAI,CAACC,KAAK,CAACF,WAAW,CAACG,UAAU,IAAI,IAAI,CAAC;EAC7D,MAAMC,cAAc,GAAGD,UAAU,CAACE,cAAc;EAChD,MAAMC,mBAAmB,GAAG;IAC3BrB,eAAe,EAAE,SAAS;IAC1BF,WAAW,EAAE,SAAS;IACtBwB,KAAK,EAAE;EACR,CAAC;EACD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGxH,QAAQ,CAACqH,mBAAmB,CAAC;EACjE,MAAM,CAACI,MAAM,EAAEC,SAAS,CAAC,GAAG1H,QAAQ,CAAC;IACpC2H,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAS;IACjBC,IAAI,EAAE;EACP,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG/H,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACgI,cAAc,EAAEC,iBAAiB,CAAC,GAAGjI,QAAQ,CAAC,EAAE,CAAC;;EAExD;EACA,MAAMkI,qBAAqB,GAAGA,CAAA,KAAM;IACnC,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,qCAAqC,CAAC,IACzED,QAAQ,CAACE,cAAc,CAAC,aAAa,CAAC;IAE1C,IAAIH,OAAO,EAAE;MACZ,MAAMI,IAAI,GAAGJ,OAAO,CAACK,qBAAqB,CAAC,CAAC;MAC5C,OAAO;QACNlD,GAAG,EAAEiD,IAAI,CAACjD,GAAG;QACbC,IAAI,EAAEgD,IAAI,CAAChD,IAAI;QACfkD,KAAK,EAAEF,IAAI,CAACE,KAAK;QACjBC,MAAM,EAAEH,IAAI,CAACG;MACd,CAAC;IACF;IACA,OAAO,IAAI;EACZ,CAAC;;EAED;EACA,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;IACvC,MAAMC,aAAa,GAAGV,qBAAqB,CAAC,CAAC;IAC7C,IAAI,CAACU,aAAa,EAAE,OAAOxD,aAAa;IAExC,MAAMyD,cAAc,GAAGC,MAAM,CAACC,WAAW;IACzC,MAAMC,aAAa,GAAGF,MAAM,CAACG,UAAU;IACvC,MAAMC,WAAW,GAAG,GAAG,CAAC,CAAC;IACzB,MAAMC,UAAU,GAAG,GAAG,CAAC,CAAC;IACxB,MAAMC,MAAM,GAAG,EAAE,CAAC,CAAC;;IAEnB;IACA,IAAIC,YAAY,GAAGT,aAAa,CAACrD,IAAI,GAAGqD,aAAa,CAACH,KAAK,GAAGW,MAAM;IACpE,IAAIE,WAAW,GAAGV,aAAa,CAACtD,GAAG,GAAIsD,aAAa,CAACF,MAAM,GAAG,CAAE,GAAIQ,WAAW,GAAG,CAAE;;IAEpF;IACA,MAAMK,eAAe,GAAGF,YAAY,GAAGF,UAAU,IAAIH,aAAa,GAAGI,MAAM;IAE3E,IAAI,CAACG,eAAe,EAAE;MACrB;MACAD,WAAW,GAAGV,aAAa,CAACtD,GAAG,GAAG8D,MAAM;MACxCC,YAAY,GAAGT,aAAa,CAACrD,IAAI,GAAIqD,aAAa,CAACH,KAAK,GAAG,CAAE,GAAIU,UAAU,GAAG,CAAE;IACjF;;IAEA;IACA;IACAG,WAAW,GAAGE,IAAI,CAACC,GAAG,CAACL,MAAM,EAAEI,IAAI,CAACE,GAAG,CAACJ,WAAW,EAAET,cAAc,GAAGK,WAAW,GAAGE,MAAM,CAAC,CAAC;;IAE5F;IACAC,YAAY,GAAGG,IAAI,CAACC,GAAG,CAACL,MAAM,EAAEI,IAAI,CAACE,GAAG,CAACL,YAAY,EAAEL,aAAa,GAAGG,UAAU,GAAGC,MAAM,CAAC,CAAC;IAE5F,OAAO;MACN9D,GAAG,EAAEgE,WAAW;MAChB/D,IAAI,EAAE8D,YAAY;MAClB7D,QAAQ,EAAE,OAAgB;MAC1BC,MAAM,EAAE;IACT,CAAC;EACF,CAAC;;EAED;EACA,MAAMkE,mBAAmB,GAAGA,CAAA,KAAM;IACjCtE,gBAAgB,CAACsD,yBAAyB,CAAC,CAAC,CAAC;EAC9C,CAAC;EAED7I,SAAS,CAAC,MAAM;IACf,MAAM8J,kBAAkB,GAAGA,CAACC,WAAgB,EAAEhF,QAAa,KAAK;MAC/D,MAAMiF,cAAc,GAAGpF,oBAAoB,CAACmF,WAAW,EAAEhF,QAAQ,CAAC;MAClE,IAAIiF,cAAc,EAAE;QACnB1G,oBAAoB,CAAC0G,cAAc,CAACC,KAAK,CAAC,CAAC,CAAC;QAC5CzG,YAAY,CAACwG,cAAc,CAACzG,SAAS,IAAI,EAAE,CAAC;QAC5CmE,aAAa,CAAC;UACbxB,eAAe,EAAE8D,cAAc,CAACE,OAAO,IAAI3C,mBAAmB,CAACrB,eAAe;UAC9EF,WAAW,EAAEgE,cAAc,CAAChE,WAAW,IAAIuB,mBAAmB,CAACvB,WAAW;UAC1EwB,KAAK,EAAEwC,cAAc,CAACG,SAAS,IAAI5C,mBAAmB,CAACC;QACxD,CAAC,CAAC;QACF3B,kBAAkB,CAAC;UAClBC,KAAK,EAAEkE,cAAc,CAACpE,eAAoC;UAAE;UAC5DrC,SAAS,EAAEyG,cAAc,CAACzG,SAAS,IAAI,EAAE;UAAE;UAC3CwC,GAAG,EAAEiE,cAAc,CAACjE,GAAG,IAAI,UAAU,CAAE;QACxC,CAAC,CAAC;QACFjC,cAAc,CAACkG,cAAc,CAACjE,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC;MACnD;IACD,CAAC;IACD+D,kBAAkB,CAACzF,eAAe,CAAC0F,WAAW,EAAE1F,eAAe,CAACU,QAAQ,CAAC;EAC1E,CAAC,EAAE,CAACV,eAAe,CAAC0F,WAAW,EAAE1F,eAAe,CAACU,QAAQ,CAAC,CAAC;EAG3D,MAAMqF,SAAS,GAAG,CACjB;IAAEC,KAAK,EAAE/H,SAAS,CAAC,UAAU,EAAE;MAAEgI,YAAY,EAAE;IAAW,CAAC,CAAC;IAAEC,IAAI,eAAEvI,OAAA,CAACjB,WAAW;MAACyJ,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAE9E,KAAK,EAAE;EAAW,CAAC,EACzH;IAAEuE,KAAK,EAAE/H,SAAS,CAAC,YAAY,EAAE;MAAEgI,YAAY,EAAE;IAAa,CAAC,CAAC;IAAEC,IAAI,eAAEvI,OAAA,CAACf,aAAa;MAACuJ,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAE9E,KAAK,EAAE;EAAa,CAAC,EACjI;IAAEuE,KAAK,EAAE/H,SAAS,CAAC,WAAW,EAAE;MAAEgI,YAAY,EAAE;IAAY,CAAC,CAAC;IAAEC,IAAI,eAAEvI,OAAA,CAACb,YAAY;MAACqJ,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAE9E,KAAK,EAAE;EAAY,CAAC,EAC7H;IAAEuE,KAAK,EAAE/H,SAAS,CAAC,aAAa,EAAE;MAAEgI,YAAY,EAAE;IAAc,CAAC,CAAC;IAAEC,IAAI,eAAEvI,OAAA,CAACX,cAAc;MAACmJ,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAE9E,KAAK,EAAE;EAAc,CAAC,EACrI;IAAEuE,KAAK,EAAE/H,SAAS,CAAC,eAAe,EAAE;MAAEgI,YAAY,EAAE;IAAgB,CAAC,CAAC;IAAEC,IAAI,eAAEvI,OAAA,CAACT,gBAAgB;MAACiJ,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAE9E,KAAK,EAAE;EAAgB,CAAC,EAC7I;IAAEuE,KAAK,EAAE/H,SAAS,CAAC,cAAc,EAAE;MAAEgI,YAAY,EAAE;IAAe,CAAC,CAAC;IAAEC,IAAI,eAAEvI,OAAA,CAACP,eAAe;MAAC+I,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAE9E,KAAK,EAAE;EAAe,CAAC,EACzI;IAAEuE,KAAK,EAAE/H,SAAS,CAAC,aAAa,EAAE;MAAEgI,YAAY,EAAE;IAAc,CAAC,CAAC;IAAEC,IAAI,eAAEvI,OAAA,CAACN,cAAc;MAAC8I,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAE9E,KAAK,EAAE;EAAc,CAAC,EACrI;IAAEuE,KAAK,EAAE/H,SAAS,CAAC,eAAe,EAAE;MAAEgI,YAAY,EAAE;IAAgB,CAAC,CAAC;IAAEC,IAAI,eAAEvI,OAAA,CAACL,gBAAgB;MAAC6I,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAE9E,KAAK,EAAE;EAAgB,CAAC,EAC7I;IAAEuE,KAAK,EAAE/H,SAAS,CAAC,cAAc,EAAE;MAAEgI,YAAY,EAAE;IAAe,CAAC,CAAC;IAAEC,IAAI,eAAEvI,OAAA,CAACJ,eAAe;MAAC4I,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAE9E,KAAK,EAAE;EAAe,CAAC,CACzI;EAED,MAAM+E,gBAAgB,GAAG5K,OAAO,CAAC,MAAM;IACtC,MAAM6K,MAAM,GAAGlG,oBAAoB,CAACP,eAAe,CAAC0F,WAAW,EAAE1F,eAAe,CAACU,QAAQ,CAAC;IAC1FzB,oBAAoB,CAACwH,MAAM,CAACb,KAAK,CAAC;IAClC,OAAOa,MAAM;EACd,CAAC,EAAE,CAACzG,eAAe,CAAC0F,WAAW,EAAE1F,eAAe,CAACU,QAAQ,CAAC,CAAC;EAE3D,MAAMgG,mBAAmB,GAAIrF,QAAa,IAAK;IAC9Ca,mBAAmB,CAACb,QAAQ,CAAC;EAC9B,CAAC;EAED,MAAMsF,WAAW,GAAGA,CAAA,KAAM;IACzB7H,iBAAiB,CAAC,KAAK,CAAC;EACzB,CAAC;EAED,IAAI,CAACiD,MAAM,EAAE,OAAO,IAAI;;EAExB;EACA;EACA;;EAGA,MAAM6E,iBAAiB,GAAGA,CAACC,CAAM,EAAEC,UAAe,KAAK;IACtD,MAAMrF,KAAK,GAAGoF,CAAC,CAACE,MAAM,CAACtF,KAAK;IAC5B4B,aAAa,CAAE2D,IAAI,KAAM;MACxB,GAAGA,IAAI;MACP,CAACF,UAAU,GAAGrF;IACf,CAAC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMwF,mBAAmB,GAAIJ,CAAoB,IAAK;IACrD,MAAMpF,KAAwB,GAAGoF,CAAC,CAACE,MAAM,CAACtF,KAA0B,CAAC,CAAC;IACtED,kBAAkB,CAAC;MAClBC,KAAK,EAAEA,KAAK;MAAE;MACdvC,SAAS,EAAEA,SAAS;MACpBwC,GAAG,EAAElC,WAAqC,CAAE;IAC7C,CAAC,CAAC;EACH,CAAC;EACD,MAAM0H,gBAAgB,GAAIC,KAAoC,IAAK;IAClE1H,cAAc,CAAE0H,KAAK,CAACJ,MAAM,CAAsBtF,KAAK,CAAC;EACzD,CAAC;EACD,MAAM2F,sBAAsB,GAAGA,CAAA,KAAM;IACpC7H,sBAAsB,CAAC,KAAK,CAAC;EAC9B,CAAC;EAED,MAAM8H,qBAAqB,GAAGA,CAAA,KAAM;IACnC9H,sBAAsB,CAAC,IAAI,CAAC;IAC5B,IAAIyD,cAAc,IAAI,CAACtD,cAAc,CAAC4H,MAAM,EAAE;MAC7C,CAAC,YAAY;QACZzH,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMF,kBAAkB,CAACqD,cAAc,CAAC;QACxCnD,UAAU,CAAC,KAAK,CAAC;MAClB,CAAC,EAAE,CAAC;IACL;EACD,CAAC;EAED,MAAM0H,iBAAiB,GAAIpF,GAAW,IAAK;IAC1C,IAAIZ,eAAe,CAACE,KAAK,KAAK,UAAU,EAAE;MACzC,IAAI,CAACU,GAAG,EAAE;QACT,OAAO,iBAAiB;MACzB;MACA,IAAI;QACH,IAAIqF,GAAG,CAACrF,GAAG,CAAC;QACZ,OAAO,EAAE;MACV,CAAC,CAAC,OAAOsF,KAAK,EAAE;QACf,OAAO,aAAa;MACrB;IACD;IACA,OAAO,EAAE;EACV,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAChC,WAAgB,EAAEhF,QAAa,KAAK;IAC/DD,cAAc,CAACiF,WAAW,CAAC;IAC3B/E,WAAW,CAACD,QAAQ,CAAC;IAErB,MAAMmD,cAAc,GAAG0D,iBAAiB,CAACrI,SAAS,CAAC;IACnD4E,iBAAiB,CAACD,cAAc,CAAC;IAEjC,IAAIA,cAAc,EAAE;MACnB;IACD;;IAEA;IACA,MAAM8D,kBAAkB,GAAG,CAAC3I,iBAAiB,IAAI,CAACA,iBAAiB,CAAC4I,IAAI,CAAC,CAAC,GAAGrH,oBAAoB,CAACmF,WAAW,EAAEhF,QAAQ,CAAC,CAACkF,KAAK,GAAG5G,iBAAiB;IAClJC,oBAAoB,CAAC0I,kBAAkB,CAAC;;IAExC;IACAvJ,YAAY,CAACsH,WAAW,EAAEhF,QAAQ,EAAE,OAAO,EAAE0C,UAAU,CAAC;IACxDhF,YAAY,CAACsH,WAAW,EAAEhF,QAAQ,EAAE,MAAM,EAAEiH,kBAAkB,CAAC;;IAE/D;IACA,MAAME,YAAY,GAAG;MACpBpG,KAAK,EAAEF,eAAe,CAACE,KAAK;MAC5BvC,SAAS,EAAEA,SAAS;MACpBwC,GAAG,EAAElC,WAAqC;MAC1CsI,WAAW,EAAE1I;IACd,CAAC;IACDhB,YAAY,CAACsH,WAAW,EAAEhF,QAAQ,EAAE,SAAS,EAAEmH,YAAY,CAAC;IAC5D9H,kBAAkB,CAAC2F,WAAW,EAAEhF,QAAQ,EAAEmH,YAAY,CAAC;IACvDpJ,kBAAkB,CAAC;MAAEiH,WAAW,EAAE,EAAE;MAAEhF,QAAQ,EAAE,EAAE;MAAEe,KAAK,EAAE;IAAK,CAAC,CAAC;IAClEkF,WAAW,CAAC,CAAC;IACb7F,mBAAmB,CAAC,IAAI,CAAC;IACzB;IACA;EACD,CAAC;EACD,MAAMiH,eAAe,GAAIlB,CAAM,IAAK;IACnC,MAAMmB,MAAM,GAAGnB,CAAC,CAACE,MAAM,CAACtF,KAAK;IAC7BtC,YAAY,CAAC6I,MAAM,CAAC;IACpBxG,kBAAkB,CAAC;MAClBC,KAAK,EAAEF,eAAe,CAACE,KAAK;MAC5BvC,SAAS,EAAE8I,MAAM;MACjBtG,GAAG,EAAElC;IACN,CAAC,CAAC;EACH,CAAC;EAED;IAAA;IACC;IACA7B,OAAA;MACCsK,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eAE9CvK,OAAA;QAAKsK,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC7BvK,OAAA;UAAKsK,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBACnCvK,OAAA;YAAKsK,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEjK,SAAS,CAAC,YAAY,EAAE;cAAEgI,YAAY,EAAE;YAAa,CAAC;UAAC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5F5I,OAAA,CAAC5B,UAAU;YACVoM,IAAI,EAAC,OAAO;YACZ,cAAW,OAAO;YAClBC,OAAO,EAAEA,CAAA,KAAMzB,WAAW,CAAC,CAAE;YAAAuB,QAAA,eAE7BvK,OAAA,CAACnB,SAAS;cAAA4J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACN5I,OAAA;UAAKsK,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC5CvK,OAAA;YAAKsK,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC/BvK,OAAA,CAACzB,WAAW;cACXmM,SAAS;cACTC,EAAE,EAAE;gBAAEC,YAAY,EAAE;cAAM,CAAE;cAAAL,QAAA,gBAE5BvK,OAAA,CAAC3B,UAAU;gBAACsM,EAAE,EAAE;kBAAEnC,QAAQ,EAAE,MAAM;kBAAEqC,UAAU,EAAE,MAAM;kBAAEC,EAAE,EAAE,KAAK;kBAAEC,SAAS,EAAE;gBAAO,CAAE;gBAAAR,QAAA,EACnFjK,SAAS,CAAC,aAAa,EAAE;kBAAEgI,YAAY,EAAE;gBAAc,CAAC;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACb5I,OAAA,CAACtB,SAAS;gBACToF,KAAK,EAAEzC,iBAAkB;gBACzBmJ,IAAI,EAAC,OAAO;gBACVG,EAAE,EAAE;kBACHK,EAAE,EAAE,KAAK;kBACTlF,MAAM,EAAE,gBAAgB;kBACxBmF,YAAY,EAAE,KAAK;kBAE1B,0BAA0B,EAAE;oBAC1BrE,MAAM,EAAE,MAAM;oBACd,0CAA0C,EAAE;sBAC1Cd,MAAM,EAAE;oBACV,CAAC;oBACD,gDAAgD,EAAE;sBAChDA,MAAM,EAAE;oBACV;kBACF,CAAC;kBACD,oCAAoC,EAAE;oBACpCA,MAAM,EAAE;kBACV;gBAEI,CAAE;gBACAoF,WAAW,EAAE5K,SAAS,CAAC,aAAa,EAAE;kBAAEgI,YAAY,EAAE;gBAAc,CAAC,CAAE;gBACzE6C,QAAQ,EAAGjC,CAAC,IAAK;kBAChB5H,oBAAoB,CAAC4H,CAAC,CAACE,MAAM,CAACtF,KAAK,CAAC;kBACpC;gBACD;cAAE;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACF5I,OAAA,CAAC3B,UAAU;gBAACsM,EAAE,EAAE;kBAAEnC,QAAQ,EAAE,MAAM;kBAAEqC,UAAU,EAAE,MAAM;kBAAEG,EAAE,EAAE,KAAK;kBAAED,SAAS,EAAE;gBAAO,CAAE;gBAAAR,QAAA,EACnFjK,SAAS,CAAC,eAAe,EAAE;kBAAEgI,YAAY,EAAE;gBAAgB,CAAC;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACb5I,OAAA,CAACxB,MAAM;gBACLsF,KAAK,EAAEF,eAAe,CAACE,KAAM;gBAC7BqH,QAAQ,EAAE7B,mBAAoB;gBAE7BqB,EAAE,EAAE;kBACHK,EAAE,EAAE,KAAK;kBACTlF,MAAM,EAAE,gBAAgB;kBACxBmF,YAAY,EAAE,KAAK;kBACnBF,SAAS,EAAE,MAAM;kBACjB,qBAAqB,EAAE;oBACxBK,OAAO,EAAE;kBACV,CAAC;kBAEN,0BAA0B,EAAE;oBAC1BxE,MAAM,EAAE,MAAM;oBACd,0CAA0C,EAAE;sBAC1Cd,MAAM,EAAE;oBACV,CAAC;oBACD,gDAAgD,EAAE;sBAChDA,MAAM,EAAE;oBACV;kBACF,CAAC;kBACD,oCAAoC,EAAE;oBACpCA,MAAM,EAAE;kBACV;gBAEI,CAAE;gBACFuF,SAAS,EAAE;kBACVC,UAAU,EAAE,CAAC;gBACd,CAAE;gBAAAf,QAAA,gBAEFvK,OAAA,CAACvB,QAAQ;kBAACqF,KAAK,EAAC,OAAO;kBAAAyG,QAAA,EAAEjK,SAAS,CAAC,OAAO,EAAE;oBAAEgI,YAAY,EAAE;kBAAQ,CAAC;gBAAC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAClF5I,OAAA,CAACvB,QAAQ;kBAACqF,KAAK,EAAC,UAAU;kBAAAyG,QAAA,EAAEjK,SAAS,CAAC,UAAU,EAAE;oBAAEgI,YAAY,EAAE;kBAAW,CAAC;gBAAC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,EAEzF7H,gBAAgB,KAAK,MAAM,IAC1BA,gBAAgB,KAAK,QAAQ,IAAIA,gBAAgB,KAAK,SAAU,GAChE,cACDf,OAAA,CAACvB,QAAQ;kBAAYqF,KAAK,EAAC,MAAM;kBAAAyG,QAAA,EAAEjK,SAAS,CAAC,MAAM,EAAE;oBAAEgI,YAAY,EAAE;kBAAO,CAAC;gBAAC,GAAhE,MAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqE,CAAC,eAC1F5I,OAAA,CAACvB,QAAQ;kBAAgBqF,KAAK,EAAC,UAAU;kBAAAyG,QAAA,EAAEjK,SAAS,CAAC,UAAU,EAAE;oBAAEgI,YAAY,EAAE;kBAAW,CAAC;gBAAC,GAAhF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAiF,CAAC,eAC1G5I,OAAA,CAACvB,QAAQ;kBAAeqF,KAAK,EAAC,SAAS;kBAAAyG,QAAA,EAAEjK,SAAS,CAAC,SAAS,EAAE;oBAAEgI,YAAY,EAAE;kBAAU,CAAC;gBAAC,GAA5E,SAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA8E,CAAC,CACtG,GACC,EAAE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEE,CAAC,EACRhF,eAAe,CAACE,KAAK,KAAK,UAAU,gBACpC9D,OAAA,CAAAE,SAAA;gBAAAqK,QAAA,gBACCvK,OAAA,CAAC3B,UAAU;kBAACsM,EAAE,EAAE;oBAAEnC,QAAQ,EAAE,MAAM;oBAAEqC,UAAU,EAAE,MAAM;oBAAEC,EAAE,EAAE,KAAK;oBAAEC,SAAS,EAAE;kBAAO,CAAE;kBAAAR,QAAA,EACnFjK,SAAS,CAAC,WAAW;gBAAC;kBAAAmI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eACb5I,OAAA,CAACtB,SAAS;kBACToF,KAAK,EAAEvC,SAAU;kBACjBiJ,IAAI,EAAC,OAAO;kBACZU,WAAW,EAAC,mBAAmB;kBAC/BC,QAAQ,EAAGjC,CAAC,IAAK;oBAChB,MAAMmB,MAAM,GAAGnB,CAAC,CAACE,MAAM,CAACtF,KAAK;oBAC7BtC,YAAY,CAAC6I,MAAM,CAAC,CAAC,CAAG;oBACxBD,eAAe,CAAClB,CAAC,CAAC,CAAC,CAAE;oBACrB/C,iBAAiB,CAACyD,iBAAiB,CAACS,MAAM,CAAC,CAAC;kBAC7C,CACC;kBACDP,KAAK,EAAE,CAAC,CAAC5D,cAAe;kBACxBqF,UAAU,EAAErF;gBAAe;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eAEF5I,OAAA,CAACpB,iBAAiB;kBACjBkF,KAAK,EAAEjC,WAAY;kBACnBsJ,QAAQ,EAAE5B,gBAAiB;kBAC3BiC,SAAS;kBACP,cAAYlL,SAAS,CAAC,aAAa,EAAE;oBAAEgI,YAAY,EAAE;kBAAc,CAAC,CAAE;kBACxEqC,EAAE,EAAE;oBACHc,GAAG,EAAE,KAAK;oBACVC,OAAO,EAAE,KAAK;oBACd9E,MAAM,EAAE;kBACT,CAAE;kBAAA2D,QAAA,EAED,CAAC,SAAS,EAAE,UAAU,CAAC,CAACoB,GAAG,CAAE5H,GAAG,IAAK;oBACrC,oBACC/D,OAAA,CAACrB,YAAY;sBACZmF,KAAK,EAAEC,GAAI;sBACX,cAAW,SAAS;sBACpB4G,EAAE,EAAE;wBACH7E,MAAM,EAAE,mBAAmB;wBAC3B8F,aAAa,EAAE,YAAY;wBAC3BpG,KAAK,EAAE,MAAM;wBACbyF,YAAY,EAAE,KAAK;wBACnBY,IAAI,EAAE,CAAC;wBACPT,OAAO,EAAE,cAAc;wBAEvB,gBAAgB,EAAE;0BACjBlH,eAAe,EAAE,qBAAqB;0BACtCsB,KAAK,EAAE,MAAM;0BACbM,MAAM,EAAE;wBACT,CAAC;wBACD,SAAS,EAAE;0BACV5B,eAAe,EAAE;wBAClB,CAAC;wBACD,cAAc,EAAE;0BACf4H,UAAU,EAAE;wBACb;sBACD,CAAE;sBAAAvB,QAAA,EAEDxG;oBAAG;sBAAA0E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACS,CAAC;kBAEjB,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACgB,CAAC;cAAA,eACnB,CAAC,GACC,IAAI;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwCG,CAAC,eAKb5I,OAAA,CAAC7B,GAAG;cACHmM,SAAS,EAAC,mBAAmB;cAC7BK,EAAE,EAAE;gBAAEM,YAAY,EAAE;cAAM,CAAE;cAAAV,QAAA,gBAE3BvK,OAAA;gBAAKsK,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEjK,SAAS,CAAC,YAAY,EAAE;kBAAEgI,YAAY,EAAE;gBAAa,CAAC;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpG5I,OAAA;gBAAAuK,QAAA,eACDvK,OAAA;kBACC+L,IAAI,EAAC,OAAO;kBACZjI,KAAK,EAAE2B,UAAU,CAACvB,eAAgB;kBAClCiH,QAAQ,EAAGjC,CAAC,IAAKD,iBAAiB,CAACC,CAAC,EAAE,iBAAiB,CAAE;kBACzDoB,SAAS,EAAC;gBAAmB;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEN5I,OAAA,CAAC7B,GAAG;cACHmM,SAAS,EAAC,mBAAmB;cAC7BK,EAAE,EAAE;gBAAEM,YAAY,EAAE;cAAM,CAAE;cAAAV,QAAA,gBAE3BvK,OAAA;gBAAKsK,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEjK,SAAS,CAAC,QAAQ,EAAE;kBAAEgI,YAAY,EAAE;gBAAS,CAAC;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5F5I,OAAA;gBAAAuK,QAAA,eACDvK,OAAA;kBACC+L,IAAI,EAAC,OAAO;kBACZjI,KAAK,EAAE2B,UAAU,CAACzB,WAAY;kBAC9BmH,QAAQ,EAAGjC,CAAC,IAAKD,iBAAiB,CAACC,CAAC,EAAE,aAAa,CAAE;kBACrDoB,SAAS,EAAC;gBAAmB;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEN5I,OAAA,CAAC7B,GAAG;cACHmM,SAAS,EAAC,mBAAmB;cAC7BK,EAAE,EAAE;gBAAEM,YAAY,EAAE;cAAM,CAAE;cAAAV,QAAA,gBAE3BvK,OAAA;gBAAKsK,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEjK,SAAS,CAAC,MAAM,EAAE;kBAAEgI,YAAY,EAAE;gBAAO,CAAC;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxF5I,OAAA;gBAAAuK,QAAA,eACDvK,OAAA;kBACC+L,IAAI,EAAC,OAAO;kBACZjI,KAAK,EAAE2B,UAAU,CAACD,KAAM;kBACxB2F,QAAQ,EAAGjC,CAAC,IAAKD,iBAAiB,CAACC,CAAC,EAAE,OAAO,CAAE;kBAC/CoB,SAAS,EAAC;gBAAmB;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEP5I,OAAA;UAAKsK,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eAClCvK,OAAA,CAAC1B,MAAM;YACN0N,OAAO,EAAC,WAAW;YACnBvB,OAAO,EAAEA,CAAA,KAAMV,kBAAkB,CAAC1H,eAAe,CAAC0F,WAAW,EAAE1F,eAAe,CAACU,QAAQ,CAAE;YACzFuH,SAAS,EAAC,WAAW;YAAAC,QAAA,EAEpBjK,SAAS,CAAC,OAAO,EAAE;cAAEgI,YAAY,EAAE;YAAQ,CAAC;UAAC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;IACL;EAAA;AAEF,CAAC;AAACxI,EAAA,CAxjBID,cAAc;EAAA,QACML,cAAc,EAgDnCD,cAAc;AAAA;AAAAoM,EAAA,GAjDb9L,cAAc;AA0jBpB,eAAeA,cAAc;AAAC,IAAA8L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}