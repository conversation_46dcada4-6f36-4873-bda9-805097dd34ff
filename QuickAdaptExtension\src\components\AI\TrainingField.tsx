import React, { useContext,useState } from 'react';
import { stopScraping } from '../../services/ScrapingService';
import './EnableAIButton.css';
import { AccountContext } from '../../components/login/AccountContext';
import { useTranslation } from 'react-i18next';
import {
    Dialog,
	DialogContent,
	InputAdornment,
	DialogContentText,
    Grid,
    Box,
    Button,
    Container,
    TextField,
    DialogTitle,
    DialogActions,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    IconButton,
    Tooltip,
    Alert,
    Chip
} from '@mui/material';

const TrainingField = ({ setShowtrainingField, showtrainingField, handleEnableAI }: { setShowtrainingField: any; showtrainingField: any; handleEnableAI:any}) => {
    const { t: translate } = useTranslation()
    const [data, setData] = useState('');
    const handleClick = () =>
    {
        setShowtrainingField(false);
        handleEnableAI();

        }
    return (
      <>
    
            
            <Dialog
				open={showtrainingField}
				onClose={() => setShowtrainingField(false)}
				PaperProps={{
					style: {
						borderRadius: "4px",
						width: "400px",
						textAlign: "center",
						height: "188px",
						boxShadow: "none",
					},
				}}
			>
				
                
				<DialogContent sx={{ padding: "20px !important" }}>
                    <DialogContentText style={{ fontSize: "14px", color: "#000" }}>
                        <label htmlFor="training-name-input" style={{ fontSize: "17px" }}>{translate('Training Name')}</label>
                        <TextField
                            id="training-name-input"
                            style={{marginTop:"16px"}}
                            placeholder={translate("Name")}
                            value={data}
                            onChange={(e) => {
                                const newValue = e.target.value;
                                
                                    setData(newValue);
                                
                            }}
                            
                            fullWidth
                            required
                        />
					</DialogContentText>
				</DialogContent>

				<DialogActions sx={{ justifyContent: "center"}}>
					
					<Button
						onClick={handleClick}
						sx={{
							backgroundColor: "var(--primarycolor)",
							color: "#FFF",
							borderRadius: "8px",
							textTransform: "capitalize",
							padding: "var(--button-padding)",
							lineHeight: "var(--button-lineheight)",
							// "&:hover": {
							// 	backgroundColor: "#D32F2F",
							// },
						}}
					>
                        {translate("Start")}
					</Button>
				</DialogActions>
			</Dialog>
            </>
  );
};

export default TrainingField;