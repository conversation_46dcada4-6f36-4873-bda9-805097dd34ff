{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\Tooltips\\\\components\\\\Buttons.tsx\",\n  _s = $RefreshSig$();\nimport React, { useMemo, useState, useEffect } from \"react\";\nimport { Box, Button, Popover, IconButton, Tooltip } from \"@mui/material\";\nimport { ChromePicker } from \"react-color\";\nimport { deleteicon, copyicon, settingsicon } from \"../../../assets/icons/icons\";\nimport useDrawerStore from \"../../../store/drawerStore\";\nimport AddIcon from \"@mui/icons-material/Add\";\nimport ButtonSetting from \"./ButtonSetting\";\nimport { useTranslation } from 'react-i18next';\n\n// Function to get GuidePopUp position and dimensions\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst getGuidePopupPosition = () => {\n  const element = document.querySelector('.qadpt-guide-popup .MuiDialog-paper') || document.getElementById('guide-popup');\n  if (element) {\n    const rect = element.getBoundingClientRect();\n    return {\n      top: rect.top,\n      left: rect.left,\n      width: rect.width,\n      height: rect.height\n    };\n  }\n  return null;\n};\n\n// Function to get TooltipBody position and dimensions\nconst getTooltipBodyPosition = () => {\n  const element = document.getElementById('Tooltip-unique');\n  if (element) {\n    const rect = element.getBoundingClientRect();\n    return {\n      top: rect.top,\n      left: rect.left,\n      width: rect.width,\n      height: rect.height\n    };\n  }\n  return null;\n};\n\n// Function to calculate button properties popup position\nconst getButtonPropertiesPopupPosition = () => {\n  const guidePopupPos = getGuidePopupPosition();\n  const tooltipBodyPos = getTooltipBodyPosition();\n  if (!guidePopupPos && !tooltipBodyPos) {\n    return {};\n  }\n  const viewportWidth = window.innerWidth;\n  const viewportHeight = window.innerHeight;\n  const popupWidth = 300; // Estimated width of button properties popup\n  const popupHeight = 200; // Estimated height of button properties popup\n  const gap = 15; // Required 15px gap\n  const headerHeight = 56;\n  const bottomPadding = 20;\n  const topPadding = 20;\n\n  // Calculate optimal vertical position (vertically aligned to screen)\n  const idealTop = (viewportHeight - popupHeight) / 2;\n  const minTop = headerHeight + topPadding;\n  const maxTop = viewportHeight - popupHeight - bottomPadding;\n  const viewportCenteredTop = Math.max(minTop, Math.min(idealTop, maxTop));\n\n  // Determine reference element (prioritize tooltip body if available, otherwise guide popup)\n  const referenceElement = tooltipBodyPos || guidePopupPos;\n  if (referenceElement) {\n    // Position to the right of the reference element with 15px gap\n    const rightPosition = referenceElement.left + referenceElement.width + gap;\n\n    // Check if there's enough space on the right\n    if (rightPosition + popupWidth <= viewportWidth - 10) {\n      return {\n        position: 'fixed',\n        top: `${viewportCenteredTop}px`,\n        left: `${rightPosition}px`\n      };\n    } else {\n      // If not enough space on right, position to the left\n      const leftPosition = referenceElement.left - popupWidth - gap;\n      return {\n        position: 'fixed',\n        top: `${viewportCenteredTop}px`,\n        left: `${Math.max(10, leftPosition)}px`\n      };\n    }\n  }\n  return {};\n};\nconst ButtonSection = ({\n  items: buttonsContainer,\n  updatedGuideData,\n  isCloneDisabled\n}) => {\n  _s();\n  var _toolTipGuideMetaData7, _toolTipGuideMetaData8, _toolTipGuideMetaData9, _toolTipGuideMetaData10;\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    // buttonsContainer,\n    tooltipBtnSettingAnchorEl: settingAnchorEl,\n    cloneTooltipButtonContainer: cloneButtonContainer,\n    updateButtonInTooltip: updateButton,\n    addNewButtonInTooltip: addNewButton,\n    deleteButtonInTooltip: deleteButton,\n    updateTooltipButtonInteraction,\n    updateTooltipButtonAction,\n    deleteTooltipButtonContainer: deleteButtonContainer,\n    updateTooltipBtnContainer: updateContainer,\n    setTooltipBtnSettingAnchorEl: setSettingAnchorEl,\n    currentStep,\n    setbtnidss,\n    getCurrentButtonInfo,\n    toolTipGuideMetaData,\n    highlightedButton,\n    setElementClick,\n    SetElementButtonClick\n  } = useDrawerStore(state => state);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [colorPickerAnchorEl, setColorPickerAnchorEl] = useState(null);\n  const [isDeleteIcon, setIsDeleteIcon] = useState(\"\");\n  const [currentContainerId, setCurrentContainerId] = useState(\"\");\n  const [currentButtonId, setCurrentButtonId] = useState(\"\");\n  const [popupPosition, setPopupPosition] = useState({});\n\n  // Update popup position when popup opens or window resizes\n  useEffect(() => {\n    if (anchorEl) {\n      const updatePosition = () => {\n        const newPosition = getButtonPropertiesPopupPosition();\n        setPopupPosition(newPosition);\n      };\n      updatePosition();\n      window.addEventListener('resize', updatePosition);\n      return () => window.removeEventListener('resize', updatePosition);\n    }\n  }, [anchorEl]);\n\n  // Default button color\n  let clickTimeout;\n  const handleClick = (event, containerId, buttonId) => {\n    const target = event.currentTarget;\n    setAnchorEl(target);\n    setSettingAnchorEl({\n      containerId,\n      buttonId,\n      value: null\n    });\n\n    // Set current container and button IDs for reference\n    setCurrentContainerId(containerId);\n    setCurrentButtonId(buttonId);\n  };\n  const handleClose = () => {\n    setAnchorEl(null);\n  };\n  const handleBackgroundColorClick = event => {\n    setColorPickerAnchorEl(event.currentTarget);\n  };\n  const handleColorChange = color => {\n    // Update the backgroundColor in the container's style\n    updateContainer(settingAnchorEl.containerId, \"style\", {\n      backgroundColor: color.hex\n    });\n\n    // Also update the BackgroundColor property at the ButtonSection level\n    updateContainer(settingAnchorEl.containerId, \"BackgroundColor\", color.hex);\n  };\n  const handleCloseColorPicker = () => {\n    setColorPickerAnchorEl(null);\n  };\n  const open = Boolean(anchorEl);\n  // const open = Boolean(anchorEl && !isEditingButton);\n  // const id = open ? \"button-popover\" : undefined;\n  const colorPickerOpen = Boolean(colorPickerAnchorEl);\n  const handleEditButtonName = (containerId, buttonId, isEditing, value) => {\n    // clearTimeout(clickTimeout);\n    updateButton(containerId, buttonId, isEditing, value);\n  };\n  const handleChangeButton = (containerId, buttonId, value) => {\n    updateButton(containerId, buttonId, \"type\", value);\n    setAnchorEl(null);\n  };\n  const handleAddIconClick = containerId => {\n    addNewButton(containerId);\n  };\n  const shouldShowAddBtn = buttonsContainer.buttons.length;\n  const buttonInfo = useMemo(() => {\n    let result = null;\n    if (settingAnchorEl.buttonId) {\n      result = buttonsContainer.buttons.find(item => item.id === settingAnchorEl.buttonId);\n    }\n    return result;\n  }, [settingAnchorEl.buttonId, buttonsContainer.buttons]);\n\n  // console.log({ buttonInfo });\n\n  // setButtonId(currentButtonId);\n  // setCuntainerId(currentButtonId);\n  const handleDelteContainer = () => {\n    deleteButtonContainer(settingAnchorEl.containerId);\n    setElementClick(\"element\");\n    const updatedData = {\n      ...updatedGuideData\n    };\n    if (updatedData.GuideStep && updatedData.GuideStep[currentStep]) {\n      const stepData = {\n        ...updatedData.GuideStep[currentStep - 1]\n      };\n\n      // Ensure GotoNext exists before modifying ButtonId\n      if (stepData.Design && stepData.Design.GotoNext && stepData.Design.GotoNext.ButtonId !== undefined) {\n        stepData.Design = {\n          ...stepData.Design,\n          GotoNext: {\n            ...stepData.Design.GotoNext,\n            ButtonId: \"\",\n            NextStep: \"element\"\n          }\n        };\n      }\n      setbtnidss(\"\");\n      // Update the GuideStep array with modified step data\n      updatedData.GuideStep = [...updatedData.GuideStep];\n      updatedData.GuideStep[currentStep - 1] = stepData;\n      updatedGuideData = updatedData;\n    }\n    setbtnidss(\"\");\n    setAnchorEl(null);\n  };\n  const handleSettingIconClick = event => {\n    //current container and button IDs\n    const containerId = settingAnchorEl.containerId || currentContainerId;\n    const buttonId = settingAnchorEl.buttonId || currentButtonId;\n    setSettingAnchorEl({\n      containerId,\n      buttonId,\n      // @ts-ignore\n      value: event.currentTarget\n    });\n    handleClose();\n  };\n  const handleCloseSettingPopup = (_containerId, _buttonId) => {\n    // updateButtonAction(containerId, buttonId, {\n    // \tvalue: selectedActions,\n    // \ttargetURL: targetURL,\n    // \ttab: selectedTab,\n    // \tinteraction: null,\n    // });\n    // updateButtonInteraction(containerId, buttonId, selectedInteraction);\n    setSettingAnchorEl({\n      containerId: \"\",\n      buttonId: \"\",\n      value: null\n    });\n    setAnchorEl(null);\n  };\n\n  // Clear the design button settings if the deleted button was previously selected\n  const deletebuttonidindesign = (buttonid, deletebuttonid) => {\n    if (buttonid === deletebuttonid) {\n      var _updatedGuideData, _updatedGuideData$Gui;\n      const targetStep = {\n        ...((_updatedGuideData = updatedGuideData) === null || _updatedGuideData === void 0 ? void 0 : (_updatedGuideData$Gui = _updatedGuideData.GuideStep) === null || _updatedGuideData$Gui === void 0 ? void 0 : _updatedGuideData$Gui[currentStep - 1])\n      };\n      if (targetStep && targetStep.Design) {\n        targetStep.Design.GotoNext = {\n          ButtonId: \"\",\n          ButtonName: \"\",\n          ElementPath: \"\",\n          NextStep: \"element\"\n        };\n      }\n      updatedGuideData.GuideStep[currentStep - 1] = targetStep;\n    }\n  };\n  const handleApplyChanges = (tempColors, selectedActions, targetURL, selectedInteraction, currentButtonName, selectedTab) => {\n    // Get the container and button IDs\n    const {\n      containerId,\n      buttonId\n    } = settingAnchorEl;\n    // Update the button style - make sure we're passing the correct structure\n    updateButton(containerId, buttonId, \"style\", {\n      backgroundColor: tempColors.backgroundColor,\n      borderColor: tempColors.borderColor,\n      color: tempColors.color\n    });\n    updateTooltipButtonAction(containerId, buttonId, {\n      value: selectedActions,\n      targetURL: targetURL,\n      tab: selectedTab,\n      interaction: null\n    });\n    updateTooltipButtonInteraction(containerId, buttonId, selectedInteraction);\n    updateButton(containerId, buttonId, \"name\", currentButtonName);\n\n    // Clear selection\n    setSettingAnchorEl({\n      containerId: \"\",\n      buttonId: \"\",\n      value: null\n    });\n    setAnchorEl(null);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      component: \"div\",\n      id: buttonsContainer.id,\n      sx: {\n        height: \"60px\",\n        width: \"100%\",\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: \"5px\",\n        padding: \"0px\",\n        boxSizing: \"border-box\",\n        backgroundColor: buttonsContainer.style.backgroundColor,\n        justifyContent: \"center\",\n        \"&:hover .add-button-icon\": {\n          display: \"flex\"\n        }\n      }\n      // onMouseEnter={(e) => setCurrentContainerId(e.currentTarget.id)}\n      // onMouseLeave={(e) => setCurrentContainerId(\"\")}\n      ,\n      children: [buttonsContainer.buttons.map(item => {\n        return /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: \"relative\",\n            display: \"flex\",\n            // flex: 1,\n            justifyContent: `center`\n            // \"&:hover .edit-icon\": { display: \"inline-flex\" },\n          },\n          onMouseLeave: () => {\n            setIsDeleteIcon(\"\");\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button\n          // contentEditable={item.isEditing}\n          , {\n            onMouseOver: e => {\n              if (item.isEditing === false && e.currentTarget.id === item.id) {\n                setIsDeleteIcon(item.id);\n              }\n            },\n            id: item.id,\n            variant: \"contained\",\n            sx: {\n              borderRadius: \"8px\",\n              transition: \"none\",\n              boxShadow: \"none !important\",\n              border: item.style.borderColor,\n              //border: `${item.type !== \"primary\" ? item.style.borderColor : \"none\"}`,\n              color: `${item.style.color}`,\n              textTransform: \"none\",\n              padding: \"4px 8px !important\",\n              lineHeight: \"var(--button-lineheight)\",\n              fontSize: \"14px !important\",\n              backgroundColor: item.style.backgroundColor,\n              width: \"fit-content\",\n              //boxShadow: \"none !important\", // Remove box shadow in normal state\n              \"&:hover\": {\n                backgroundColor: item.style.backgroundColor,\n                // Keep the same background color on hover\n                opacity: 0.9,\n                // Slightly reduce opacity on hover for visual feedback\n                boxShadow: \"none !important\" // Remove box shadow in hover state\n              }\n            },\n            onClick: e => handleClick(e, buttonsContainer.id, item.id) // Open popover when clicking the button\n            ,\n            children: item.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 8\n          }, this), buttonsContainer.buttons.length > 1 && isDeleteIcon === item.id ? /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            className: \"del-icon\",\n            sx: {\n              position: \"absolute\",\n              top: \"-10px\",\n              right: \"-10px\",\n              backgroundColor: \"#fff\",\n              //boxShadow: \"none !important\", // Remove box shadow in normal state\n              // display: \"none\", // Initially hidden\n              boxShadow: \"rgba(0, 0, 0, 0.4) 0px 2px 6px\",\n              zIndex: \"1\",\n              padding: \"3px !important\",\n              \"&:hover\": {\n                backgroundColor: \"#fff\",\n                boxShadow: \"none !important\"\n              },\n              span: {\n                height: \"14px\"\n              },\n              svg: {\n                width: \"14px\",\n                height: \"14px\",\n                path: {\n                  fill: \"#ff0000\"\n                }\n              }\n            },\n            onClick: e => {\n              var _toolTipGuideMetaData, _toolTipGuideMetaData2, _toolTipGuideMetaData3, _toolTipGuideMetaData4, _toolTipGuideMetaData5, _toolTipGuideMetaData6;\n              e.stopPropagation();\n              deletebuttonidindesign(item.id, (_toolTipGuideMetaData = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData === void 0 ? void 0 : (_toolTipGuideMetaData2 = _toolTipGuideMetaData.design) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : (_toolTipGuideMetaData3 = _toolTipGuideMetaData2.gotoNext) === null || _toolTipGuideMetaData3 === void 0 ? void 0 : _toolTipGuideMetaData3.ButtonId);\n              deleteButton(item.id, buttonsContainer.id, (_toolTipGuideMetaData4 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : (_toolTipGuideMetaData5 = _toolTipGuideMetaData4.design) === null || _toolTipGuideMetaData5 === void 0 ? void 0 : (_toolTipGuideMetaData6 = _toolTipGuideMetaData5.gotoNext) === null || _toolTipGuideMetaData6 === void 0 ? void 0 : _toolTipGuideMetaData6.ButtonId);\n              setElementClick(\"element\");\n              // setButtonClick(false);\n              SetElementButtonClick(false);\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: deleteicon\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 10\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 9\n          }, this) : null]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 7\n        }, this);\n      }), shouldShowAddBtn < 4 ? /*#__PURE__*/_jsxDEV(IconButton, {\n        className: \"add-button-icon\",\n        sx: {\n          backgroundColor: \"#5F9EA0\",\n          cursor: \"pointer\",\n          zIndex: 1000,\n          padding: \"6px !important\",\n          display: \"none\",\n          boxShadow: \"none !important\",\n          // Remove box shadow in normal state\n          \"&:hover\": {\n            backgroundColor: \"#70afaf\",\n            boxShadow: \"none !important\" // Remove box shadow in hover state\n          }\n        }\n        // sx={sideAddButtonStyle}\n        ,\n        onClick: () => handleAddIconClick(buttonsContainer.id),\n        children: /*#__PURE__*/_jsxDEV(AddIcon, {\n          fontSize: \"small\",\n          sx: {\n            color: \"#fff\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 442,\n        columnNumber: 6\n      }, this) : null, /*#__PURE__*/_jsxDEV(Popover, {\n        id: \"button-toolbar\",\n        open: open\n        // anchorEl={anchorEl}\n        ,\n        onClose: handleClose,\n        anchorReference: \"anchorPosition\",\n        anchorPosition: {\n          top: (anchorEl === null || anchorEl === void 0 ? void 0 : anchorEl.getBoundingClientRect().top) || 0,\n          left: (anchorEl === null || anchorEl === void 0 ? void 0 : anchorEl.getBoundingClientRect().left) || 0\n        },\n        anchorOrigin: {\n          vertical: \"top\",\n          horizontal: \"left\"\n        },\n        transformOrigin: {\n          vertical: \"bottom\",\n          horizontal: \"left\"\n        },\n        slotProps: {\n          root: {\n            // instead of writing sx on popover write here it also target to root and more clear\n            sx: {\n              zIndex: theme => theme.zIndex.tooltip + 1000,\n              ...getButtonPropertiesPopupPosition()\n            }\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: \"6px\",\n            padding: \"4px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"Settings\",\n            style: {\n              zIndex: 99999\n            },\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: handleSettingIconClick,\n              sx: {\n                boxShadow: \"none !important\",\n                // Remove box shadow in normal state\n                \"&:hover\": {\n                  boxShadow: \"none !important\" // Remove box shadow in hover state\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: settingsicon\n                },\n                style: {\n                  width: \"20px\",\n                  height: \"20px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            arrow: true,\n            title: \"Background Color\",\n            style: {\n              zIndex: 99999\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                backgroundColor: buttonsContainer.style.backgroundColor === \"#5f9ea0\" ? buttonsContainer.style.backgroundColor : \"#e0dbdb\",\n                width: \"20px\",\n                height: \"20px\",\n                borderRadius: \"50%\",\n                //border: `1px solid red`,\n                marginTop: \"-3px\"\n              },\n              component: \"div\",\n              role: \"button\",\n              onClick: handleBackgroundColorClick\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: isCloneDisabled ? \"Maximum limit of 3 Button sections reached\" : \"Clone Section\",\n            style: {\n              zIndex: 99999\n            },\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => {\n                cloneButtonContainer(settingAnchorEl.containerId);\n                setAnchorEl(null);\n              },\n              disabled: isCloneDisabled,\n              sx: {\n                boxShadow: \"none !important\",\n                // Remove box shadow in normal state\n                \"&:hover\": {\n                  boxShadow: \"none !important\" // Remove box shadow in hover state\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: copyicon\n                },\n                style: {\n                  opacity: isCloneDisabled ? 0.5 : 1,\n                  width: \"20px\",\n                  height: \"20px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"Delete Button Section\",\n            style: {\n              zIndex: 99999\n            },\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\"\n              // disabled={buttonsContainer.buttons.length === 1}\n              ,\n              onClick: handleDelteContainer,\n              disabled: ((_toolTipGuideMetaData7 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData7 === void 0 ? void 0 : (_toolTipGuideMetaData8 = _toolTipGuideMetaData7.containers) === null || _toolTipGuideMetaData8 === void 0 ? void 0 : _toolTipGuideMetaData8.length) === 1,\n              sx: {\n                boxShadow: \"none !important\",\n                // Remove box shadow in normal state\n                \"&:hover\": {\n                  boxShadow: \"none !important\" // Remove box shadow in hover state\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: deleteicon\n                },\n                style: {\n                  opacity: ((_toolTipGuideMetaData9 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData9 === void 0 ? void 0 : (_toolTipGuideMetaData10 = _toolTipGuideMetaData9.containers) === null || _toolTipGuideMetaData10 === void 0 ? void 0 : _toolTipGuideMetaData10.length) === 1 ? 0.5 : 1,\n                  pointerEvents: \"none\",\n                  width: \"20px\",\n                  height: \"24px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(ButtonSetting, {\n        handleCloseSettingPopup: handleCloseSettingPopup,\n        settingAnchorEl: settingAnchorEl,\n        buttonInfo: buttonInfo,\n        handleApplyChanges: handleApplyChanges,\n        updatedGuideData: updatedGuideData\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 616,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 4\n    }, this), /*#__PURE__*/_jsxDEV(Popover, {\n      open: colorPickerOpen,\n      anchorEl: colorPickerAnchorEl,\n      onClose: handleCloseColorPicker,\n      anchorOrigin: {\n        vertical: \"bottom\",\n        horizontal: \"center\"\n      },\n      transformOrigin: {\n        vertical: \"top\",\n        horizontal: \"center\"\n      },\n      id: \"color-picker\",\n      slotProps: {\n        root: {\n          // instead of writing sx on popover write here it also target to root and more clear\n          sx: {\n            zIndex: theme => theme.zIndex.tooltip + 1000\n          }\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(ChromePicker, {\n          color: buttonsContainer.style.backgroundColor,\n          onChange: handleColorChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 649,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n          children: `\n      .chrome-picker input {\n        padding: 0 !important;\n      }\n    `\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 653,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 648,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 626,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true);\n};\n_s(ButtonSection, \"4W0wwe8UAPLF8I5oRfLIgHd20Ao=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = ButtonSection;\nexport default ButtonSection;\nvar _c;\n$RefreshReg$(_c, \"ButtonSection\");", "map": {"version": 3, "names": ["React", "useMemo", "useState", "useEffect", "Box", "<PERSON><PERSON>", "Popover", "IconButton", "<PERSON><PERSON><PERSON>", "ChromePicker", "deleteicon", "copyicon", "settingsicon", "useDrawerStore", "AddIcon", "ButtonSetting", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "getGuidePopupPosition", "element", "document", "querySelector", "getElementById", "rect", "getBoundingClientRect", "top", "left", "width", "height", "getTooltipBodyPosition", "getButtonPropertiesPopupPosition", "guidePopupPos", "tooltipBodyPos", "viewportWidth", "window", "innerWidth", "viewportHeight", "innerHeight", "popup<PERSON><PERSON><PERSON>", "popupHeight", "gap", "headerHeight", "bottomPadding", "topPadding", "idealTop", "minTop", "maxTop", "viewportCenteredTop", "Math", "max", "min", "referenceElement", "rightPosition", "position", "leftPosition", "ButtonSection", "items", "buttonsContainer", "updatedGuideData", "isCloneDisabled", "_s", "_toolTipGuideMetaData7", "_toolTipGuideMetaData8", "_toolTipGuideMetaData9", "_toolTipGuideMetaData10", "t", "translate", "tooltipBtnSettingAnchorEl", "settingAnchorEl", "cloneTooltipButtonContainer", "cloneButtonContainer", "updateButtonInTooltip", "updateButton", "addNewButtonInTooltip", "addNewButton", "deleteButtonInTooltip", "deleteButton", "updateTooltipButtonInteraction", "updateTooltipButtonAction", "deleteTooltipButtonContainer", "deleteButtonContainer", "updateTooltipBtnContainer", "updateContainer", "setTooltipBtnSettingAnchorEl", "setSettingAnchorEl", "currentStep", "setbtnidss", "getCurrentButtonInfo", "toolTipGuideMetaData", "highlighted<PERSON><PERSON><PERSON>", "setElementClick", "SetElementButtonClick", "state", "anchorEl", "setAnchorEl", "colorPickerAnchorEl", "setColorPickerAnchorEl", "isDeleteIcon", "setIsDeleteIcon", "currentContainerId", "setCurrentContainerId", "currentButtonId", "setCurrentButtonId", "popupPosition", "setPopupPosition", "updatePosition", "newPosition", "addEventListener", "removeEventListener", "clickTimeout", "handleClick", "event", "containerId", "buttonId", "target", "currentTarget", "value", "handleClose", "handleBackgroundColorClick", "handleColorChange", "color", "backgroundColor", "hex", "handleCloseColorPicker", "open", "Boolean", "colorPickerOpen", "handleEditButtonName", "isEditing", "handleChangeButton", "handleAddIconClick", "shouldShowAddBtn", "buttons", "length", "buttonInfo", "result", "find", "item", "id", "handleDelteContainer", "updatedData", "GuideStep", "stepData", "Design", "GotoNext", "ButtonId", "undefined", "NextStep", "handleSettingIconClick", "handleCloseSettingPopup", "_containerId", "_buttonId", "deletebuttonidindesign", "buttonid", "deletebuttonid", "_updatedGuideData", "_updatedGuideData$Gui", "targetStep", "ButtonName", "<PERSON>ement<PERSON><PERSON>", "handleApplyChanges", "tempColors", "selectedActions", "targetURL", "selectedInteraction", "currentButtonName", "selectedTab", "borderColor", "tab", "interaction", "children", "component", "sx", "display", "alignItems", "padding", "boxSizing", "style", "justifyContent", "map", "onMouseLeave", "onMouseOver", "e", "variant", "borderRadius", "transition", "boxShadow", "border", "textTransform", "lineHeight", "fontSize", "opacity", "onClick", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "className", "right", "zIndex", "span", "svg", "path", "fill", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "stopPropagation", "design", "gotoNext", "dangerouslySetInnerHTML", "__html", "cursor", "onClose", "anchorReference", "anchorPosition", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "slotProps", "root", "theme", "tooltip", "title", "arrow", "marginTop", "role", "disabled", "containers", "pointerEvents", "onChange", "_c", "$RefreshReg$"], "sources": ["E:/Code/quickadapt/QuickAdaptExtension/src/components/Tooltips/components/Buttons.tsx"], "sourcesContent": ["import React, { useMemo, useState, useEffect } from \"react\";\r\nimport { Box, Button, Popover, Typography, TextField, IconButton, Tooltip } from \"@mui/material\";\r\nimport { ChromePicker, ColorResult } from \"react-color\";\r\nimport { deleteicon, copyicon, settingsicon, backgroundcoloricon, editicon } from \"../../../assets/icons/icons\";\r\nimport useDrawerStore, { ButtonContainer, TButton } from \"../../../store/drawerStore\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport ButtonSetting from \"./ButtonSetting\";\r\nimport { useAsyncError } from \"react-router-dom\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\n// Function to get GuidePopUp position and dimensions\r\nconst getGuidePopupPosition = () => {\r\n\tconst element = document.querySelector('.qadpt-guide-popup .MuiDialog-paper') ||\r\n\t\t\t\t\tdocument.getElementById('guide-popup');\r\n\tif (element) {\r\n\t\tconst rect = element.getBoundingClientRect();\r\n\t\treturn {\r\n\t\t\ttop: rect.top,\r\n\t\t\tleft: rect.left,\r\n\t\t\twidth: rect.width,\r\n\t\t\theight: rect.height\r\n\t\t};\r\n\t}\r\n\treturn null;\r\n};\r\n\r\n// Function to get TooltipBody position and dimensions\r\nconst getTooltipBodyPosition = () => {\r\n\tconst element = document.getElementById('Tooltip-unique');\r\n\tif (element) {\r\n\t\tconst rect = element.getBoundingClientRect();\r\n\t\treturn {\r\n\t\t\ttop: rect.top,\r\n\t\t\tleft: rect.left,\r\n\t\t\twidth: rect.width,\r\n\t\t\theight: rect.height\r\n\t\t};\r\n\t}\r\n\treturn null;\r\n};\r\n\r\n// Function to calculate button properties popup position\r\nconst getButtonPropertiesPopupPosition = () => {\r\n\tconst guidePopupPos = getGuidePopupPosition();\r\n\tconst tooltipBodyPos = getTooltipBodyPosition();\r\n\r\n\tif (!guidePopupPos && !tooltipBodyPos) {\r\n\t\treturn {};\r\n\t}\r\n\r\n\tconst viewportWidth = window.innerWidth;\r\n\tconst viewportHeight = window.innerHeight;\r\n\tconst popupWidth = 300; // Estimated width of button properties popup\r\n\tconst popupHeight = 200; // Estimated height of button properties popup\r\n\tconst gap = 15; // Required 15px gap\r\n\tconst headerHeight = 56;\r\n\tconst bottomPadding = 20;\r\n\tconst topPadding = 20;\r\n\r\n\t// Calculate optimal vertical position (vertically aligned to screen)\r\n\tconst idealTop = (viewportHeight - popupHeight) / 2;\r\n\tconst minTop = headerHeight + topPadding;\r\n\tconst maxTop = viewportHeight - popupHeight - bottomPadding;\r\n\tconst viewportCenteredTop = Math.max(minTop, Math.min(idealTop, maxTop));\r\n\r\n\t// Determine reference element (prioritize tooltip body if available, otherwise guide popup)\r\n\tconst referenceElement = tooltipBodyPos || guidePopupPos;\r\n\r\n\tif (referenceElement) {\r\n\t\t// Position to the right of the reference element with 15px gap\r\n\t\tconst rightPosition = referenceElement.left + referenceElement.width + gap;\r\n\r\n\t\t// Check if there's enough space on the right\r\n\t\tif (rightPosition + popupWidth <= viewportWidth - 10) {\r\n\t\t\treturn {\r\n\t\t\t\tposition: 'fixed',\r\n\t\t\t\ttop: `${viewportCenteredTop}px`,\r\n\t\t\t\tleft: `${rightPosition}px`,\r\n\t\t\t};\r\n\t\t} else {\r\n\t\t\t// If not enough space on right, position to the left\r\n\t\t\tconst leftPosition = referenceElement.left - popupWidth - gap;\r\n\t\t\treturn {\r\n\t\t\t\tposition: 'fixed',\r\n\t\t\t\ttop: `${viewportCenteredTop}px`,\r\n\t\t\t\tleft: `${Math.max(10, leftPosition)}px`,\r\n\t\t\t};\r\n\t\t}\r\n\t}\r\n\r\n\treturn {};\r\n};\r\n\r\nconst ButtonSection: React.FC<{ items: ButtonContainer; updatedGuideData: any; isCloneDisabled?: boolean }> = ({\r\n\titems: buttonsContainer,\r\n\tupdatedGuideData,\r\n\tisCloneDisabled,\r\n}) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\t// buttonsContainer,\r\n\t\ttooltipBtnSettingAnchorEl: settingAnchorEl,\r\n\t\tcloneTooltipButtonContainer: cloneButtonContainer,\r\n\t\tupdateButtonInTooltip: updateButton,\r\n\t\taddNewButtonInTooltip: addNewButton,\r\n\t\tdeleteButtonInTooltip: deleteButton,\r\n\t\tupdateTooltipButtonInteraction,\r\n\t\tupdateTooltipButtonAction,\r\n\t\tdeleteTooltipButtonContainer: deleteButtonContainer,\r\n\t\tupdateTooltipBtnContainer: updateContainer,\r\n\t\tsetTooltipBtnSettingAnchorEl: setSettingAnchorEl,\r\n\t\tcurrentStep,\r\n\t\tsetbtnidss,\r\n\t\tgetCurrentButtonInfo,\r\n\t\ttoolTipGuideMetaData,\r\n\t\thighlightedButton,\r\n\t\tsetElementClick,\r\n\t\tSetElementButtonClick,\r\n\t} = useDrawerStore((state: any) => state);\r\n\tconst [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [colorPickerAnchorEl, setColorPickerAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [isDeleteIcon, setIsDeleteIcon] = useState(\"\");\r\n\tconst [currentContainerId, setCurrentContainerId] = useState(\"\");\r\n\tconst [currentButtonId, setCurrentButtonId] = useState(\"\");\r\n\tconst [popupPosition, setPopupPosition] = useState({});\r\n\r\n\t// Update popup position when popup opens or window resizes\r\n\tuseEffect(() => {\r\n\t\tif (anchorEl) {\r\n\t\t\tconst updatePosition = () => {\r\n\t\t\t\tconst newPosition = getButtonPropertiesPopupPosition();\r\n\t\t\t\tsetPopupPosition(newPosition);\r\n\t\t\t};\r\n\r\n\t\t\tupdatePosition();\r\n\t\t\twindow.addEventListener('resize', updatePosition);\r\n\t\t\treturn () => window.removeEventListener('resize', updatePosition);\r\n\t\t}\r\n\t}, [anchorEl]);\r\n\r\n\t// Default button color\r\n\tlet clickTimeout: NodeJS.Timeout;\r\n\tconst handleClick = (event: React.MouseEvent<HTMLElement>, containerId: string, buttonId: string) => {\r\n\t\tconst target = event.currentTarget;\r\n\t\tsetAnchorEl(target);\r\n\t\tsetSettingAnchorEl({\r\n\t\t\tcontainerId,\r\n\t\t\tbuttonId,\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\r\n\t\t// Set current container and button IDs for reference\r\n\t\tsetCurrentContainerId(containerId);\r\n\t\tsetCurrentButtonId(buttonId);\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\r\n\tconst handleBackgroundColorClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetColorPickerAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\tconst handleColorChange = (color: ColorResult) => {\r\n\t\t// Update the backgroundColor in the container's style\r\n\t\tupdateContainer(settingAnchorEl.containerId, \"style\", {\r\n\t\t\tbackgroundColor: color.hex,\r\n\t\t});\r\n\r\n\t\t// Also update the BackgroundColor property at the ButtonSection level\r\n\t\tupdateContainer(settingAnchorEl.containerId, \"BackgroundColor\", color.hex);\r\n\t};\r\n\r\n\tconst handleCloseColorPicker = () => {\r\n\t\tsetColorPickerAnchorEl(null);\r\n\t};\r\n\r\n\tconst open = Boolean(anchorEl);\r\n\t// const open = Boolean(anchorEl && !isEditingButton);\r\n\t// const id = open ? \"button-popover\" : undefined;\r\n\tconst colorPickerOpen = Boolean(colorPickerAnchorEl);\r\n\r\n\tconst handleEditButtonName = (\r\n\t\tcontainerId: string,\r\n\t\tbuttonId: string,\r\n\t\tisEditing: keyof TButton,\r\n\t\tvalue: TButton[keyof TButton]\r\n\t) => {\r\n\t\t// clearTimeout(clickTimeout);\r\n\t\tupdateButton(containerId, buttonId, isEditing, value);\r\n\t};\r\n\r\n\tconst handleChangeButton = (containerId: string, buttonId: string, value: TButton[keyof TButton]) => {\r\n\t\tupdateButton(containerId, buttonId, \"type\", value);\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\r\n\tconst handleAddIconClick = (containerId: string) => {\r\n\t\taddNewButton(containerId);\r\n\t};\r\n\r\n\tconst shouldShowAddBtn = buttonsContainer.buttons.length;\r\n\r\n\tconst buttonInfo = useMemo(() => {\r\n\t\tlet result = null;\r\n\t\tif (settingAnchorEl.buttonId) {\r\n\t\t\tresult = buttonsContainer.buttons.find((item: any) => item.id === settingAnchorEl.buttonId);\r\n\t\t}\r\n\t\treturn result;\r\n\t}, [settingAnchorEl.buttonId, buttonsContainer.buttons]);\r\n\r\n\t// console.log({ buttonInfo });\r\n\r\n\t// setButtonId(currentButtonId);\r\n\t// setCuntainerId(currentButtonId);\r\n\tconst handleDelteContainer = () => {\r\n\t\tdeleteButtonContainer(settingAnchorEl.containerId);\r\n\t\tsetElementClick(\"element\");\r\n\t\tconst updatedData = { ...updatedGuideData };\r\n\t\tif (updatedData.GuideStep && updatedData.GuideStep[currentStep]) {\r\n\t\t\tconst stepData = { ...updatedData.GuideStep[currentStep - 1] };\r\n\r\n\t\t\t// Ensure GotoNext exists before modifying ButtonId\r\n\t\t\tif (stepData.Design && stepData.Design.GotoNext && stepData.Design.GotoNext.ButtonId !== undefined) {\r\n\t\t\t\tstepData.Design = {\r\n\t\t\t\t\t...stepData.Design,\r\n\t\t\t\t\tGotoNext: {\r\n\t\t\t\t\t\t...stepData.Design.GotoNext,\r\n\t\t\t\t\t\tButtonId: \"\",\r\n\t\t\t\t\t\tNextStep: \"element\",\r\n\t\t\t\t\t},\r\n\t\t\t\t};\r\n\t\t\t}\r\n\t\t\tsetbtnidss(\"\");\r\n\t\t\t// Update the GuideStep array with modified step data\r\n\t\t\tupdatedData.GuideStep = [...updatedData.GuideStep];\r\n\t\t\tupdatedData.GuideStep[currentStep - 1] = stepData;\r\n\t\t\tupdatedGuideData = updatedData;\r\n\t\t}\r\n\r\n\t\tsetbtnidss(\"\");\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\r\n\tconst handleSettingIconClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\t//current container and button IDs\r\n\t\tconst containerId = settingAnchorEl.containerId || currentContainerId;\r\n\t\tconst buttonId = settingAnchorEl.buttonId || currentButtonId;\r\n\r\n\t\tsetSettingAnchorEl({\r\n\t\t\tcontainerId,\r\n\t\t\tbuttonId,\r\n\t\t\t// @ts-ignore\r\n\t\t\tvalue: event.currentTarget,\r\n\t\t});\r\n\t\thandleClose();\r\n\t};\r\n\r\n\tconst handleCloseSettingPopup = (_containerId: string, _buttonId: string) => {\r\n\t\t// updateButtonAction(containerId, buttonId, {\r\n\t\t// \tvalue: selectedActions,\r\n\t\t// \ttargetURL: targetURL,\r\n\t\t// \ttab: selectedTab,\r\n\t\t// \tinteraction: null,\r\n\t\t// });\r\n\t\t// updateButtonInteraction(containerId, buttonId, selectedInteraction);\r\n\t\tsetSettingAnchorEl({\r\n\t\t\tcontainerId: \"\",\r\n\t\t\tbuttonId: \"\",\r\n\t\t\tvalue: null,\r\n\t\t});\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\r\n\t// Clear the design button settings if the deleted button was previously selected\r\n\tconst deletebuttonidindesign = (buttonid: any, deletebuttonid: any) => {\r\n\t\tif (buttonid === deletebuttonid) {\r\n\t\t\tconst targetStep = { ...updatedGuideData?.GuideStep?.[currentStep - 1] };\r\n\t\t\tif (targetStep && targetStep.Design) {\r\n\t\t\t\ttargetStep.Design.GotoNext = {\r\n\t\t\t\t\tButtonId: \"\",\r\n\t\t\t\t\tButtonName: \"\",\r\n\t\t\t\t\tElementPath: \"\",\r\n\t\t\t\t\tNextStep: \"element\",\r\n\t\t\t\t};\r\n\t\t\t}\r\n\t\t\tupdatedGuideData.GuideStep[currentStep - 1] = targetStep;\r\n\t\t}\r\n\t};\r\n\r\n\r\n\tconst handleApplyChanges = (\r\n\t\ttempColors: any, // Changed from string to any to handle the object structure\r\n\t\tselectedActions: string,\r\n\t\ttargetURL: string,\r\n\t\tselectedInteraction: string,\r\n\t\tcurrentButtonName: string,\r\n\t\tselectedTab: string\r\n\t) => {\r\n\t\t// Get the container and button IDs\r\n\t\tconst { containerId, buttonId } = settingAnchorEl;\r\n\t\t// Update the button style - make sure we're passing the correct structure\r\n\t\tupdateButton(containerId, buttonId, \"style\", {\r\n\t\t\tbackgroundColor: tempColors.backgroundColor,\r\n\t\t\tborderColor: tempColors.borderColor,\r\n\t\t\tcolor: tempColors.color\r\n\t\t});\r\n\t\tupdateTooltipButtonAction(containerId, buttonId, {\r\n\t\t\tvalue: selectedActions,\r\n\t\t\ttargetURL: targetURL,\r\n\t\t\ttab: selectedTab,\r\n\t\t\tinteraction: null,\r\n\t\t});\r\n\t\tupdateTooltipButtonInteraction(containerId, buttonId, selectedInteraction);\r\n\t\tupdateButton(containerId, buttonId, \"name\", currentButtonName);\r\n\r\n\t\t// Clear selection\r\n\t\tsetSettingAnchorEl({ containerId: \"\", buttonId: \"\", value: null });\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t<Box\r\n\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\tid={buttonsContainer.id}\r\n\t\t\t\tsx={{\r\n\t\t\t\t\theight: \"60px\",\r\n\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\tgap: \"5px\",\r\n\t\t\t\t\tpadding: \"0px\",\r\n\t\t\t\t\tboxSizing: \"border-box\",\r\n\t\t\t\t\tbackgroundColor: buttonsContainer.style.backgroundColor,\r\n\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\"&:hover .add-button-icon\": {\r\n\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t\t// onMouseEnter={(e) => setCurrentContainerId(e.currentTarget.id)}\r\n\t\t\t\t// onMouseLeave={(e) => setCurrentContainerId(\"\")}\r\n\t\t\t>\r\n\t\t\t\t{buttonsContainer.buttons.map((item: any) => {\r\n\t\t\t\t\treturn (\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t// flex: 1,\r\n\t\t\t\t\t\t\t\tjustifyContent: `center`,\r\n\t\t\t\t\t\t\t\t// \"&:hover .edit-icon\": { display: \"inline-flex\" },\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonMouseLeave={() => {\r\n\t\t\t\t\t\t\t\tsetIsDeleteIcon(\"\");\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t// contentEditable={item.isEditing}\r\n\t\t\t\t\t\t\t\tonMouseOver={(e) => {\r\n\t\t\t\t\t\t\t\t\tif (item.isEditing === false && e.currentTarget.id === item.id) {\r\n\t\t\t\t\t\t\t\t\t\tsetIsDeleteIcon(item.id);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tid={item.id}\r\n\t\t\t\t\t\t\t\tvariant={\"contained\"}\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\t\t\ttransition: \"none\",\r\n\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\",\r\n\t\t\t\t\t\t\t\t\tborder: item.style.borderColor,\r\n\t\t\t\t\t\t\t\t\t//border: `${item.type !== \"primary\" ? item.style.borderColor : \"none\"}`,\r\n\t\t\t\t\t\t\t\t\tcolor: `${item.style.color}`,\r\n\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\tpadding: \"4px 8px !important\",\r\n\t\t\t\t\t\t\t\t\tlineHeight: \"var(--button-lineheight)\",\r\n\t\t\t\t\t\t\t\t\tfontSize: \"14px !important\",\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: item.style.backgroundColor,\r\n\t\t\t\t\t\t\t\t\twidth: \"fit-content\",\r\n\t\t\t\t\t\t\t\t\t//boxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: item.style.backgroundColor, // Keep the same background color on hover\r\n\t\t\t\t\t\t\t\t\t\topacity: 0.9, // Slightly reduce opacity on hover for visual feedback\r\n\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonClick={(e) => handleClick(e, buttonsContainer.id, item.id)} // Open popover when clicking the button\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{item.name}\r\n\t\t\t\t\t\t\t</Button>\r\n\r\n\t\t\t\t\t\t\t{buttonsContainer.buttons.length > 1 && isDeleteIcon === item.id ? (\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"del-icon\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\t\t\t\ttop: \"-10px\",\r\n\t\t\t\t\t\t\t\t\t\tright: \"-10px\",\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t//boxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\t\t// display: \"none\", // Initially hidden\r\n\t\t\t\t\t\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.4) 0px 2px 6px\",\r\n\t\t\t\t\t\t\t\t\t\tzIndex: \"1\",\r\n\t\t\t\t\t\t\t\t\t\tpadding : \"3px !important\",\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", \r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tspan: {\r\n\t\t\t\t\t\t\t\t\t\t\theight: \"14px\"\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tsvg: {\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"14px\", \r\n\t\t\t\t\t\t\t\t\t\t\theight: \"14px\", \r\n\t\t\t\t\t\t\t\t\t\t\tpath: {\r\n\t\t\t\t\t\t\t\t\t\t\t\tfill:\"#ff0000\"\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\tonClick={(e) => {\r\n\t\t\t\t\t\t\t\t\t\te.stopPropagation();\r\n\t\t\t\t\t\t\t\t\t\tdeletebuttonidindesign(item.id, toolTipGuideMetaData[currentStep - 1]?.design?.gotoNext?.ButtonId);\r\n\t\t\t\t\t\t\t\t\t\tdeleteButton(\r\n\t\t\t\t\t\t\t\t\t\t\titem.id,\r\n\t\t\t\t\t\t\t\t\t\t\tbuttonsContainer.id,\r\n\t\t\t\t\t\t\t\t\t\t\ttoolTipGuideMetaData[currentStep - 1]?.design?.gotoNext?.ButtonId\r\n\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\tsetElementClick(\"element\");\r\n\t\t\t\t\t\t\t\t\t\t// setButtonClick(false);\r\n\t\t\t\t\t\t\t\t\t\tSetElementButtonClick(false);\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: deleteicon }} />\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t) : null}\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t);\r\n\t\t\t\t})}\r\n\t\t\t\t{shouldShowAddBtn < 4 ? (\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tclassName=\"add-button-icon\"\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tbackgroundColor: \"#5F9EA0\",\r\n\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\tzIndex: 1000,\r\n\t\t\t\t\t\t\tpadding: \"6px !important\",\r\n\t\t\t\t\t\t\tdisplay: \"none\",\r\n\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"#70afaf\",\r\n\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t// sx={sideAddButtonStyle}\r\n\t\t\t\t\t\tonClick={() => handleAddIconClick(buttonsContainer.id)}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<AddIcon\r\n\t\t\t\t\t\t\tfontSize=\"small\"\r\n\t\t\t\t\t\t\tsx={{ color: \"#fff\" }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t) : null}\r\n\r\n\t\t\t\t<Popover\r\n\t\t\t\t\tid={\"button-toolbar\"}\r\n\t\t\t\t\topen={open}\r\n\t\t\t\t\t// anchorEl={anchorEl}\r\n\t\t\t\t\tonClose={handleClose}\r\n\t\t\t\t\tanchorReference=\"anchorPosition\"\r\n\t\t\t\t\tanchorPosition={{\r\n\t\t\t\t\t\ttop: anchorEl?.getBoundingClientRect().top || 0,\r\n\t\t\t\t\t\tleft: anchorEl?.getBoundingClientRect().left || 0,\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\t\thorizontal: \"left\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\t\thorizontal: \"left\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tslotProps={{\r\n\t\t\t\t\t\troot: {\r\n\t\t\t\t\t\t\t// instead of writing sx on popover write here it also target to root and more clear\r\n\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\tzIndex: (theme) => theme.zIndex.tooltip + 1000,\r\n\t\t\t\t\t\t\t\t...getButtonPropertiesPopupPosition(),\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\tgap: \"6px\",\r\n\t\t\t\t\t\t\tpadding: \"4px\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{/*\t<Typography\r\n\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\tsx={{ cursor: \"pointer\", fontWeight: \"bold\" }}\r\n\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\tid=\"primary\"\r\n\t\t\t\t\t\t\tonClick={(e) =>\r\n\t\t\t\t\t\t\t\thandleChangeButton(settingAnchorEl.containerId, settingAnchorEl.buttonId, e.currentTarget.id)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\tPrimary\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\tsx={{ cursor: \"pointer\", color: \"gray\" }}\r\n\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\tid=\"secondary\"\r\n\t\t\t\t\t\t\tonClick={(e) =>\r\n\t\t\t\t\t\t\t\thandleChangeButton(settingAnchorEl.containerId, settingAnchorEl.buttonId, e.currentTarget.id)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\tSecondary\r\n\t\t\t\t\t\t</Typography>\r\n\r\n\t\t\t\t\t\t<Box sx={{ borderLeft: \"1px solid #ccc\", height: \"24px\", marginLeft: \"8px\" }}></Box>\r\n                       */}\r\n\t\t\t\t\t\t{/* Icons for additional options */}\r\n\t\t\t\t\t\t<Tooltip title=\"Settings\" style={{ zIndex: 99999 }}>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tonClick={handleSettingIconClick}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: settingsicon }}\r\n\t\t\t\t\t\t\t\tstyle={{width:\"20px\", height:\"20px\"}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<Tooltip arrow\r\n\t\t\t\t\t\t\ttitle=\"Background Color\"\r\n\t\t\t\t\t\t\tstyle={{ zIndex: 99999 }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\tbuttonsContainer.style.backgroundColor === \"#5f9ea0\"\r\n\t\t\t\t\t\t\t\t\t\t\t? buttonsContainer.style.backgroundColor\r\n\t\t\t\t\t\t\t\t\t\t\t: \"#e0dbdb\",\r\n\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t\t//border: `1px solid red`,\r\n\t\t\t\t\t\t\t\t\tmarginTop: \"-3px\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\t\trole=\"button\"\r\n\t\t\t\t\t\t\t\tonClick={handleBackgroundColorClick}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\ttitle={isCloneDisabled ? \"Maximum limit of 3 Button sections reached\" : \"Clone Section\"}\r\n\t\t\t\t\t\t\tstyle={{ zIndex: 99999 }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\tcloneButtonContainer(settingAnchorEl.containerId);\r\n\t\t\t\t\t\t\t\tsetAnchorEl(null);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tdisabled={isCloneDisabled}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: copyicon }}\r\n\t\t\t\t\t\t\t\tstyle={{ opacity: isCloneDisabled ? 0.5 : 1, width:\"20px\", height:\"20px\" }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t<Tooltip title=\"Delete Button Section\" style={{ zIndex: 99999 }}>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t// disabled={buttonsContainer.buttons.length === 1}\r\n\t\t\t\t\t\t\tonClick={handleDelteContainer}\r\n\t\t\t\t\t\t\tdisabled={toolTipGuideMetaData[currentStep - 1]?.containers?.length === 1}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: deleteicon }}\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\topacity: toolTipGuideMetaData[currentStep - 1]?.containers?.length === 1 ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\tpointerEvents: \"none\",\r\n\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\theight: \"24px\"\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t</Popover>\r\n\r\n\t\t\t\t<ButtonSetting\r\n\t\t\t\t\thandleCloseSettingPopup={handleCloseSettingPopup}\r\n\t\t\t\t\tsettingAnchorEl={settingAnchorEl}\r\n\t\t\t\t\tbuttonInfo={buttonInfo}\r\n\t\t\t\t\thandleApplyChanges={handleApplyChanges}\r\n\t\t\t\t\tupdatedGuideData={updatedGuideData}\r\n\t\t\t\t/>\r\n\t\t\t</Box>\r\n\r\n\t\t\t{/* Color Picker Popover */}\r\n\t\t\t<Popover\r\n\t\t\t\topen={colorPickerOpen}\r\n\t\t\t\tanchorEl={colorPickerAnchorEl}\r\n\t\t\t\tonClose={handleCloseColorPicker}\r\n\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\tid=\"color-picker\"\r\n\t\t\t\tslotProps={{\r\n\t\t\t\t\troot: {\r\n\t\t\t\t\t\t// instead of writing sx on popover write here it also target to root and more clear\r\n\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\tzIndex: (theme) => theme.zIndex.tooltip + 1000,\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<ChromePicker\r\n\t\t\t\t\t\tcolor={buttonsContainer.style.backgroundColor}\r\n\t\t\t\t\t\tonChange={handleColorChange}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t<style>\r\n\t\t\t\t\t\t{`\r\n      .chrome-picker input {\r\n        padding: 0 !important;\r\n      }\r\n    `}\r\n\t\t\t\t\t</style>\r\n\t\t\t\t</Box>\r\n\t\t\t</Popover>\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default ButtonSection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,OAAO,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3D,SAASC,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAyBC,UAAU,EAAEC,OAAO,QAAQ,eAAe;AAChG,SAASC,YAAY,QAAqB,aAAa;AACvD,SAASC,UAAU,EAAEC,QAAQ,EAAEC,YAAY,QAAuC,6BAA6B;AAC/G,OAAOC,cAAc,MAAoC,4BAA4B;AACrF,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,aAAa,MAAM,iBAAiB;AAE3C,SAASC,cAAc,QAAQ,eAAe;;AAE9C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EACnC,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,qCAAqC,CAAC,IACzED,QAAQ,CAACE,cAAc,CAAC,aAAa,CAAC;EAC1C,IAAIH,OAAO,EAAE;IACZ,MAAMI,IAAI,GAAGJ,OAAO,CAACK,qBAAqB,CAAC,CAAC;IAC5C,OAAO;MACNC,GAAG,EAAEF,IAAI,CAACE,GAAG;MACbC,IAAI,EAAEH,IAAI,CAACG,IAAI;MACfC,KAAK,EAAEJ,IAAI,CAACI,KAAK;MACjBC,MAAM,EAAEL,IAAI,CAACK;IACd,CAAC;EACF;EACA,OAAO,IAAI;AACZ,CAAC;;AAED;AACA,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;EACpC,MAAMV,OAAO,GAAGC,QAAQ,CAACE,cAAc,CAAC,gBAAgB,CAAC;EACzD,IAAIH,OAAO,EAAE;IACZ,MAAMI,IAAI,GAAGJ,OAAO,CAACK,qBAAqB,CAAC,CAAC;IAC5C,OAAO;MACNC,GAAG,EAAEF,IAAI,CAACE,GAAG;MACbC,IAAI,EAAEH,IAAI,CAACG,IAAI;MACfC,KAAK,EAAEJ,IAAI,CAACI,KAAK;MACjBC,MAAM,EAAEL,IAAI,CAACK;IACd,CAAC;EACF;EACA,OAAO,IAAI;AACZ,CAAC;;AAED;AACA,MAAME,gCAAgC,GAAGA,CAAA,KAAM;EAC9C,MAAMC,aAAa,GAAGb,qBAAqB,CAAC,CAAC;EAC7C,MAAMc,cAAc,GAAGH,sBAAsB,CAAC,CAAC;EAE/C,IAAI,CAACE,aAAa,IAAI,CAACC,cAAc,EAAE;IACtC,OAAO,CAAC,CAAC;EACV;EAEA,MAAMC,aAAa,GAAGC,MAAM,CAACC,UAAU;EACvC,MAAMC,cAAc,GAAGF,MAAM,CAACG,WAAW;EACzC,MAAMC,UAAU,GAAG,GAAG,CAAC,CAAC;EACxB,MAAMC,WAAW,GAAG,GAAG,CAAC,CAAC;EACzB,MAAMC,GAAG,GAAG,EAAE,CAAC,CAAC;EAChB,MAAMC,YAAY,GAAG,EAAE;EACvB,MAAMC,aAAa,GAAG,EAAE;EACxB,MAAMC,UAAU,GAAG,EAAE;;EAErB;EACA,MAAMC,QAAQ,GAAG,CAACR,cAAc,GAAGG,WAAW,IAAI,CAAC;EACnD,MAAMM,MAAM,GAAGJ,YAAY,GAAGE,UAAU;EACxC,MAAMG,MAAM,GAAGV,cAAc,GAAGG,WAAW,GAAGG,aAAa;EAC3D,MAAMK,mBAAmB,GAAGC,IAAI,CAACC,GAAG,CAACJ,MAAM,EAAEG,IAAI,CAACE,GAAG,CAACN,QAAQ,EAAEE,MAAM,CAAC,CAAC;;EAExE;EACA,MAAMK,gBAAgB,GAAGnB,cAAc,IAAID,aAAa;EAExD,IAAIoB,gBAAgB,EAAE;IACrB;IACA,MAAMC,aAAa,GAAGD,gBAAgB,CAACzB,IAAI,GAAGyB,gBAAgB,CAACxB,KAAK,GAAGa,GAAG;;IAE1E;IACA,IAAIY,aAAa,GAAGd,UAAU,IAAIL,aAAa,GAAG,EAAE,EAAE;MACrD,OAAO;QACNoB,QAAQ,EAAE,OAAO;QACjB5B,GAAG,EAAE,GAAGsB,mBAAmB,IAAI;QAC/BrB,IAAI,EAAE,GAAG0B,aAAa;MACvB,CAAC;IACF,CAAC,MAAM;MACN;MACA,MAAME,YAAY,GAAGH,gBAAgB,CAACzB,IAAI,GAAGY,UAAU,GAAGE,GAAG;MAC7D,OAAO;QACNa,QAAQ,EAAE,OAAO;QACjB5B,GAAG,EAAE,GAAGsB,mBAAmB,IAAI;QAC/BrB,IAAI,EAAE,GAAGsB,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEK,YAAY,CAAC;MACpC,CAAC;IACF;EACD;EAEA,OAAO,CAAC,CAAC;AACV,CAAC;AAED,MAAMC,aAAqG,GAAGA,CAAC;EAC9GC,KAAK,EAAEC,gBAAgB;EACvBC,gBAAgB;EAChBC;AACD,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA;EACL,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGrD,cAAc,CAAC,CAAC;EACzC,MAAM;IACL;IACAsD,yBAAyB,EAAEC,eAAe;IAC1CC,2BAA2B,EAAEC,oBAAoB;IACjDC,qBAAqB,EAAEC,YAAY;IACnCC,qBAAqB,EAAEC,YAAY;IACnCC,qBAAqB,EAAEC,YAAY;IACnCC,8BAA8B;IAC9BC,yBAAyB;IACzBC,4BAA4B,EAAEC,qBAAqB;IACnDC,yBAAyB,EAAEC,eAAe;IAC1CC,4BAA4B,EAAEC,kBAAkB;IAChDC,WAAW;IACXC,UAAU;IACVC,oBAAoB;IACpBC,oBAAoB;IACpBC,iBAAiB;IACjBC,eAAe;IACfC;EACD,CAAC,GAAGjF,cAAc,CAAEkF,KAAU,IAAKA,KAAK,CAAC;EACzC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG/F,QAAQ,CAAqB,IAAI,CAAC;EAClE,MAAM,CAACgG,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjG,QAAQ,CAAqB,IAAI,CAAC;EACxF,MAAM,CAACkG,YAAY,EAAEC,eAAe,CAAC,GAAGnG,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrG,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACsG,eAAe,EAAEC,kBAAkB,CAAC,GAAGvG,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACwG,aAAa,EAAEC,gBAAgB,CAAC,GAAGzG,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEtD;EACAC,SAAS,CAAC,MAAM;IACf,IAAI6F,QAAQ,EAAE;MACb,MAAMY,cAAc,GAAGA,CAAA,KAAM;QAC5B,MAAMC,WAAW,GAAG5E,gCAAgC,CAAC,CAAC;QACtD0E,gBAAgB,CAACE,WAAW,CAAC;MAC9B,CAAC;MAEDD,cAAc,CAAC,CAAC;MAChBvE,MAAM,CAACyE,gBAAgB,CAAC,QAAQ,EAAEF,cAAc,CAAC;MACjD,OAAO,MAAMvE,MAAM,CAAC0E,mBAAmB,CAAC,QAAQ,EAAEH,cAAc,CAAC;IAClE;EACD,CAAC,EAAE,CAACZ,QAAQ,CAAC,CAAC;;EAEd;EACA,IAAIgB,YAA4B;EAChC,MAAMC,WAAW,GAAGA,CAACC,KAAoC,EAAEC,WAAmB,EAAEC,QAAgB,KAAK;IACpG,MAAMC,MAAM,GAAGH,KAAK,CAACI,aAAa;IAClCrB,WAAW,CAACoB,MAAM,CAAC;IACnB9B,kBAAkB,CAAC;MAClB4B,WAAW;MACXC,QAAQ;MACRG,KAAK,EAAE;IACR,CAAC,CAAC;;IAEF;IACAhB,qBAAqB,CAACY,WAAW,CAAC;IAClCV,kBAAkB,CAACW,QAAQ,CAAC;EAC7B,CAAC;EAED,MAAMI,WAAW,GAAGA,CAAA,KAAM;IACzBvB,WAAW,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,MAAMwB,0BAA0B,GAAIP,KAAoC,IAAK;IAC5Ef,sBAAsB,CAACe,KAAK,CAACI,aAAa,CAAC;EAC5C,CAAC;EAED,MAAMI,iBAAiB,GAAIC,KAAkB,IAAK;IACjD;IACAtC,eAAe,CAACd,eAAe,CAAC4C,WAAW,EAAE,OAAO,EAAE;MACrDS,eAAe,EAAED,KAAK,CAACE;IACxB,CAAC,CAAC;;IAEF;IACAxC,eAAe,CAACd,eAAe,CAAC4C,WAAW,EAAE,iBAAiB,EAAEQ,KAAK,CAACE,GAAG,CAAC;EAC3E,CAAC;EAED,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACpC3B,sBAAsB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAM4B,IAAI,GAAGC,OAAO,CAAChC,QAAQ,CAAC;EAC9B;EACA;EACA,MAAMiC,eAAe,GAAGD,OAAO,CAAC9B,mBAAmB,CAAC;EAEpD,MAAMgC,oBAAoB,GAAGA,CAC5Bf,WAAmB,EACnBC,QAAgB,EAChBe,SAAwB,EACxBZ,KAA6B,KACzB;IACJ;IACA5C,YAAY,CAACwC,WAAW,EAAEC,QAAQ,EAAEe,SAAS,EAAEZ,KAAK,CAAC;EACtD,CAAC;EAED,MAAMa,kBAAkB,GAAGA,CAACjB,WAAmB,EAAEC,QAAgB,EAAEG,KAA6B,KAAK;IACpG5C,YAAY,CAACwC,WAAW,EAAEC,QAAQ,EAAE,MAAM,EAAEG,KAAK,CAAC;IAClDtB,WAAW,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,MAAMoC,kBAAkB,GAAIlB,WAAmB,IAAK;IACnDtC,YAAY,CAACsC,WAAW,CAAC;EAC1B,CAAC;EAED,MAAMmB,gBAAgB,GAAG1E,gBAAgB,CAAC2E,OAAO,CAACC,MAAM;EAExD,MAAMC,UAAU,GAAGxI,OAAO,CAAC,MAAM;IAChC,IAAIyI,MAAM,GAAG,IAAI;IACjB,IAAInE,eAAe,CAAC6C,QAAQ,EAAE;MAC7BsB,MAAM,GAAG9E,gBAAgB,CAAC2E,OAAO,CAACI,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACC,EAAE,KAAKtE,eAAe,CAAC6C,QAAQ,CAAC;IAC5F;IACA,OAAOsB,MAAM;EACd,CAAC,EAAE,CAACnE,eAAe,CAAC6C,QAAQ,EAAExD,gBAAgB,CAAC2E,OAAO,CAAC,CAAC;;EAExD;;EAEA;EACA;EACA,MAAMO,oBAAoB,GAAGA,CAAA,KAAM;IAClC3D,qBAAqB,CAACZ,eAAe,CAAC4C,WAAW,CAAC;IAClDtB,eAAe,CAAC,SAAS,CAAC;IAC1B,MAAMkD,WAAW,GAAG;MAAE,GAAGlF;IAAiB,CAAC;IAC3C,IAAIkF,WAAW,CAACC,SAAS,IAAID,WAAW,CAACC,SAAS,CAACxD,WAAW,CAAC,EAAE;MAChE,MAAMyD,QAAQ,GAAG;QAAE,GAAGF,WAAW,CAACC,SAAS,CAACxD,WAAW,GAAG,CAAC;MAAE,CAAC;;MAE9D;MACA,IAAIyD,QAAQ,CAACC,MAAM,IAAID,QAAQ,CAACC,MAAM,CAACC,QAAQ,IAAIF,QAAQ,CAACC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAKC,SAAS,EAAE;QACnGJ,QAAQ,CAACC,MAAM,GAAG;UACjB,GAAGD,QAAQ,CAACC,MAAM;UAClBC,QAAQ,EAAE;YACT,GAAGF,QAAQ,CAACC,MAAM,CAACC,QAAQ;YAC3BC,QAAQ,EAAE,EAAE;YACZE,QAAQ,EAAE;UACX;QACD,CAAC;MACF;MACA7D,UAAU,CAAC,EAAE,CAAC;MACd;MACAsD,WAAW,CAACC,SAAS,GAAG,CAAC,GAAGD,WAAW,CAACC,SAAS,CAAC;MAClDD,WAAW,CAACC,SAAS,CAACxD,WAAW,GAAG,CAAC,CAAC,GAAGyD,QAAQ;MACjDpF,gBAAgB,GAAGkF,WAAW;IAC/B;IAEAtD,UAAU,CAAC,EAAE,CAAC;IACdQ,WAAW,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,MAAMsD,sBAAsB,GAAIrC,KAAoC,IAAK;IACxE;IACA,MAAMC,WAAW,GAAG5C,eAAe,CAAC4C,WAAW,IAAIb,kBAAkB;IACrE,MAAMc,QAAQ,GAAG7C,eAAe,CAAC6C,QAAQ,IAAIZ,eAAe;IAE5DjB,kBAAkB,CAAC;MAClB4B,WAAW;MACXC,QAAQ;MACR;MACAG,KAAK,EAAEL,KAAK,CAACI;IACd,CAAC,CAAC;IACFE,WAAW,CAAC,CAAC;EACd,CAAC;EAED,MAAMgC,uBAAuB,GAAGA,CAACC,YAAoB,EAAEC,SAAiB,KAAK;IAC5E;IACA;IACA;IACA;IACA;IACA;IACA;IACAnE,kBAAkB,CAAC;MAClB4B,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,EAAE;MACZG,KAAK,EAAE;IACR,CAAC,CAAC;IACFtB,WAAW,CAAC,IAAI,CAAC;EAClB,CAAC;;EAED;EACA,MAAM0D,sBAAsB,GAAGA,CAACC,QAAa,EAAEC,cAAmB,KAAK;IACtE,IAAID,QAAQ,KAAKC,cAAc,EAAE;MAAA,IAAAC,iBAAA,EAAAC,qBAAA;MAChC,MAAMC,UAAU,GAAG;QAAE,KAAAF,iBAAA,GAAGjG,gBAAgB,cAAAiG,iBAAA,wBAAAC,qBAAA,GAAhBD,iBAAA,CAAkBd,SAAS,cAAAe,qBAAA,uBAA3BA,qBAAA,CAA8BvE,WAAW,GAAG,CAAC,CAAC;MAAC,CAAC;MACxE,IAAIwE,UAAU,IAAIA,UAAU,CAACd,MAAM,EAAE;QACpCc,UAAU,CAACd,MAAM,CAACC,QAAQ,GAAG;UAC5BC,QAAQ,EAAE,EAAE;UACZa,UAAU,EAAE,EAAE;UACdC,WAAW,EAAE,EAAE;UACfZ,QAAQ,EAAE;QACX,CAAC;MACF;MACAzF,gBAAgB,CAACmF,SAAS,CAACxD,WAAW,GAAG,CAAC,CAAC,GAAGwE,UAAU;IACzD;EACD,CAAC;EAGD,MAAMG,kBAAkB,GAAGA,CAC1BC,UAAe,EACfC,eAAuB,EACvBC,SAAiB,EACjBC,mBAA2B,EAC3BC,iBAAyB,EACzBC,WAAmB,KACf;IACJ;IACA,MAAM;MAAEtD,WAAW;MAAEC;IAAS,CAAC,GAAG7C,eAAe;IACjD;IACAI,YAAY,CAACwC,WAAW,EAAEC,QAAQ,EAAE,OAAO,EAAE;MAC5CQ,eAAe,EAAEwC,UAAU,CAACxC,eAAe;MAC3C8C,WAAW,EAAEN,UAAU,CAACM,WAAW;MACnC/C,KAAK,EAAEyC,UAAU,CAACzC;IACnB,CAAC,CAAC;IACF1C,yBAAyB,CAACkC,WAAW,EAAEC,QAAQ,EAAE;MAChDG,KAAK,EAAE8C,eAAe;MACtBC,SAAS,EAAEA,SAAS;MACpBK,GAAG,EAAEF,WAAW;MAChBG,WAAW,EAAE;IACd,CAAC,CAAC;IACF5F,8BAA8B,CAACmC,WAAW,EAAEC,QAAQ,EAAEmD,mBAAmB,CAAC;IAC1E5F,YAAY,CAACwC,WAAW,EAAEC,QAAQ,EAAE,MAAM,EAAEoD,iBAAiB,CAAC;;IAE9D;IACAjF,kBAAkB,CAAC;MAAE4B,WAAW,EAAE,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAEG,KAAK,EAAE;IAAK,CAAC,CAAC;IAClEtB,WAAW,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,oBACC/E,OAAA,CAAAE,SAAA;IAAAyJ,QAAA,gBACC3J,OAAA,CAACd,GAAG;MACH0K,SAAS,EAAE,KAAM;MACjBjC,EAAE,EAAEjF,gBAAgB,CAACiF,EAAG;MACxBkC,EAAE,EAAE;QACHhJ,MAAM,EAAE,MAAM;QACdD,KAAK,EAAE,MAAM;QACbkJ,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBtI,GAAG,EAAE,KAAK;QACVuI,OAAO,EAAE,KAAK;QACdC,SAAS,EAAE,YAAY;QACvBvD,eAAe,EAAEhE,gBAAgB,CAACwH,KAAK,CAACxD,eAAe;QACvDyD,cAAc,EAAE,QAAQ;QACxB,0BAA0B,EAAE;UAC3BL,OAAO,EAAE;QACV;MACD;MACA;MACA;MAAA;MAAAH,QAAA,GAECjH,gBAAgB,CAAC2E,OAAO,CAAC+C,GAAG,CAAE1C,IAAS,IAAK;QAC5C,oBACC1H,OAAA,CAACd,GAAG;UACH2K,EAAE,EAAE;YACHvH,QAAQ,EAAE,UAAU;YACpBwH,OAAO,EAAE,MAAM;YACf;YACAK,cAAc,EAAE;YAChB;UACD,CAAE;UACFE,YAAY,EAAEA,CAAA,KAAM;YACnBlF,eAAe,CAAC,EAAE,CAAC;UACpB,CAAE;UAAAwE,QAAA,gBAEF3J,OAAA,CAACb;UACA;UAAA;YACAmL,WAAW,EAAGC,CAAC,IAAK;cACnB,IAAI7C,IAAI,CAACT,SAAS,KAAK,KAAK,IAAIsD,CAAC,CAACnE,aAAa,CAACuB,EAAE,KAAKD,IAAI,CAACC,EAAE,EAAE;gBAC/DxC,eAAe,CAACuC,IAAI,CAACC,EAAE,CAAC;cACzB;YACD,CAAE;YACFA,EAAE,EAAED,IAAI,CAACC,EAAG;YACZ6C,OAAO,EAAE,WAAY;YACrBX,EAAE,EAAE;cACHY,YAAY,EAAE,KAAK;cACnBC,UAAU,EAAE,MAAM;cAClBC,SAAS,EAAE,iBAAiB;cAC5BC,MAAM,EAAElD,IAAI,CAACwC,KAAK,CAACV,WAAW;cAC9B;cACA/C,KAAK,EAAE,GAAGiB,IAAI,CAACwC,KAAK,CAACzD,KAAK,EAAE;cAC5BoE,aAAa,EAAE,MAAM;cACrBb,OAAO,EAAE,oBAAoB;cAC7Bc,UAAU,EAAE,0BAA0B;cACtCC,QAAQ,EAAE,iBAAiB;cAC3BrE,eAAe,EAAEgB,IAAI,CAACwC,KAAK,CAACxD,eAAe;cAC3C9F,KAAK,EAAE,aAAa;cACpB;cACA,SAAS,EAAE;gBACV8F,eAAe,EAAEgB,IAAI,CAACwC,KAAK,CAACxD,eAAe;gBAAE;gBAC7CsE,OAAO,EAAE,GAAG;gBAAE;gBACdL,SAAS,EAAE,iBAAiB,CAAE;cAC/B;YACD,CAAE;YACFM,OAAO,EAAGV,CAAC,IAAKxE,WAAW,CAACwE,CAAC,EAAE7H,gBAAgB,CAACiF,EAAE,EAAED,IAAI,CAACC,EAAE,CAAE,CAAC;YAAA;YAAAgC,QAAA,EAE7DjC,IAAI,CAACwD;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAER5I,gBAAgB,CAAC2E,OAAO,CAACC,MAAM,GAAG,CAAC,IAAIpC,YAAY,KAAKwC,IAAI,CAACC,EAAE,gBAC/D3H,OAAA,CAACX,UAAU;YACVkM,IAAI,EAAC,OAAO;YACZC,SAAS,EAAC,UAAU;YACpB3B,EAAE,EAAE;cACHvH,QAAQ,EAAE,UAAU;cACpB5B,GAAG,EAAE,OAAO;cACZ+K,KAAK,EAAE,OAAO;cACd/E,eAAe,EAAE,MAAM;cACvB;cACA;cACAiE,SAAS,EAAE,gCAAgC;cAC3Ce,MAAM,EAAE,GAAG;cACX1B,OAAO,EAAG,gBAAgB;cAC1B,SAAS,EAAE;gBACVtD,eAAe,EAAE,MAAM;gBACvBiE,SAAS,EAAE;cACZ,CAAC;cACDgB,IAAI,EAAE;gBACL9K,MAAM,EAAE;cACT,CAAC;cACD+K,GAAG,EAAE;gBACJhL,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdgL,IAAI,EAAE;kBACLC,IAAI,EAAC;gBACN;cACD;YACD,CAAE;YACFb,OAAO,EAAGV,CAAC,IAAK;cAAA,IAAAwB,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;cACf7B,CAAC,CAAC8B,eAAe,CAAC,CAAC;cACnB5D,sBAAsB,CAACf,IAAI,CAACC,EAAE,GAAAoE,qBAAA,GAAEtH,oBAAoB,CAACH,WAAW,GAAG,CAAC,CAAC,cAAAyH,qBAAA,wBAAAC,sBAAA,GAArCD,qBAAA,CAAuCO,MAAM,cAAAN,sBAAA,wBAAAC,sBAAA,GAA7CD,sBAAA,CAA+CO,QAAQ,cAAAN,sBAAA,uBAAvDA,sBAAA,CAAyD/D,QAAQ,CAAC;cAClGrE,YAAY,CACX6D,IAAI,CAACC,EAAE,EACPjF,gBAAgB,CAACiF,EAAE,GAAAuE,sBAAA,GACnBzH,oBAAoB,CAACH,WAAW,GAAG,CAAC,CAAC,cAAA4H,sBAAA,wBAAAC,sBAAA,GAArCD,sBAAA,CAAuCI,MAAM,cAAAH,sBAAA,wBAAAC,sBAAA,GAA7CD,sBAAA,CAA+CI,QAAQ,cAAAH,sBAAA,uBAAvDA,sBAAA,CAAyDlE,QAC1D,CAAC;cACDvD,eAAe,CAAC,SAAS,CAAC;cAC1B;cACAC,qBAAqB,CAAC,KAAK,CAAC;YAC7B,CAAE;YAAA+E,QAAA,eAEF3J,OAAA;cAAMwM,uBAAuB,EAAE;gBAAEC,MAAM,EAAEjN;cAAW;YAAE;cAAA2L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,GACV,IAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAER,CAAC,CAAC,EACDlE,gBAAgB,GAAG,CAAC,gBACpBpH,OAAA,CAACX,UAAU;QACVmM,SAAS,EAAC,iBAAiB;QAC3B3B,EAAE,EAAE;UACHnD,eAAe,EAAE,SAAS;UAC1BgG,MAAM,EAAE,SAAS;UACjBhB,MAAM,EAAE,IAAI;UACZ1B,OAAO,EAAE,gBAAgB;UACzBF,OAAO,EAAE,MAAM;UACfa,SAAS,EAAE,iBAAiB;UAAE;UAC9B,SAAS,EAAE;YACVjE,eAAe,EAAE,SAAS;YAC1BiE,SAAS,EAAE,iBAAiB,CAAE;UAC/B;QACD;QACA;QAAA;QACAM,OAAO,EAAEA,CAAA,KAAM9D,kBAAkB,CAACzE,gBAAgB,CAACiF,EAAE,CAAE;QAAAgC,QAAA,eAEvD3J,OAAA,CAACJ,OAAO;UACPmL,QAAQ,EAAC,OAAO;UAChBlB,EAAE,EAAE;YAAEpD,KAAK,EAAE;UAAO;QAAE;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,GACV,IAAI,eAERtL,OAAA,CAACZ,OAAO;QACPuI,EAAE,EAAE,gBAAiB;QACrBd,IAAI,EAAEA;QACN;QAAA;QACA8F,OAAO,EAAErG,WAAY;QACrBsG,eAAe,EAAC,gBAAgB;QAChCC,cAAc,EAAE;UACfnM,GAAG,EAAE,CAAAoE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAErE,qBAAqB,CAAC,CAAC,CAACC,GAAG,KAAI,CAAC;UAC/CC,IAAI,EAAE,CAAAmE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAErE,qBAAqB,CAAC,CAAC,CAACE,IAAI,KAAI;QACjD,CAAE;QACFmM,YAAY,EAAE;UACbC,QAAQ,EAAE,KAAK;UACfC,UAAU,EAAE;QACb,CAAE;QACFC,eAAe,EAAE;UAChBF,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE;QACb,CAAE;QACFE,SAAS,EAAE;UACVC,IAAI,EAAE;YACL;YACAtD,EAAE,EAAE;cACH6B,MAAM,EAAG0B,KAAK,IAAKA,KAAK,CAAC1B,MAAM,CAAC2B,OAAO,GAAG,IAAI;cAC9C,GAAGtM,gCAAgC,CAAC;YACrC;UACD;QACD,CAAE;QAAA4I,QAAA,eAEF3J,OAAA,CAACd,GAAG;UACH2K,EAAE,EAAE;YACHC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBtI,GAAG,EAAE,KAAK;YACVuI,OAAO,EAAE;UACV,CAAE;UAAAL,QAAA,gBA4BF3J,OAAA,CAACV,OAAO;YAACgO,KAAK,EAAC,UAAU;YAACpD,KAAK,EAAE;cAAEwB,MAAM,EAAE;YAAM,CAAE;YAAA/B,QAAA,eACnD3J,OAAA,CAACX,UAAU;cACVkM,IAAI,EAAC,OAAO;cACZN,OAAO,EAAE5C,sBAAuB;cAChCwB,EAAE,EAAE;gBACHc,SAAS,EAAE,iBAAiB;gBAAE;gBAC9B,SAAS,EAAE;kBACVA,SAAS,EAAE,iBAAiB,CAAE;gBAC/B;cACD,CAAE;cAAAhB,QAAA,eAEF3J,OAAA;gBAAMwM,uBAAuB,EAAE;kBAAEC,MAAM,EAAE/M;gBAAa,CAAE;gBACvDwK,KAAK,EAAE;kBAACtJ,KAAK,EAAC,MAAM;kBAAEC,MAAM,EAAC;gBAAM;cAAE;gBAAAsK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACXtL,OAAA,CAACV,OAAO;YAACiO,KAAK;YACbD,KAAK,EAAC,kBAAkB;YACxBpD,KAAK,EAAE;cAAEwB,MAAM,EAAE;YAAM,CAAE;YAAA/B,QAAA,eAEzB3J,OAAA,CAACd,GAAG;cACH2K,EAAE,EAAE;gBACHnD,eAAe,EACdhE,gBAAgB,CAACwH,KAAK,CAACxD,eAAe,KAAK,SAAS,GACjDhE,gBAAgB,CAACwH,KAAK,CAACxD,eAAe,GACtC,SAAS;gBACb9F,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACd4J,YAAY,EAAE,KAAK;gBACnB;gBACA+C,SAAS,EAAE;cACZ,CAAE;cACF5D,SAAS,EAAE,KAAM;cACjB6D,IAAI,EAAC,QAAQ;cACbxC,OAAO,EAAE1E;YAA2B;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACVtL,OAAA,CAACV,OAAO;YACPgO,KAAK,EAAE1K,eAAe,GAAG,4CAA4C,GAAG,eAAgB;YACxFsH,KAAK,EAAE;cAAEwB,MAAM,EAAE;YAAM,CAAE;YAAA/B,QAAA,eAE1B3J,OAAA,CAACX,UAAU;cACVkM,IAAI,EAAC,OAAO;cACZN,OAAO,EAAEA,CAAA,KAAM;gBACd1H,oBAAoB,CAACF,eAAe,CAAC4C,WAAW,CAAC;gBACjDlB,WAAW,CAAC,IAAI,CAAC;cAClB,CAAE;cACD2I,QAAQ,EAAE9K,eAAgB;cAC3BiH,EAAE,EAAE;gBACHc,SAAS,EAAE,iBAAiB;gBAAE;gBAC9B,SAAS,EAAE;kBACVA,SAAS,EAAE,iBAAiB,CAAE;gBAC/B;cACD,CAAE;cAAAhB,QAAA,eAEF3J,OAAA;gBACCwM,uBAAuB,EAAE;kBAAEC,MAAM,EAAEhN;gBAAS,CAAE;gBAC9CyK,KAAK,EAAE;kBAAEc,OAAO,EAAEpI,eAAe,GAAG,GAAG,GAAG,CAAC;kBAAEhC,KAAK,EAAC,MAAM;kBAAEC,MAAM,EAAC;gBAAO;cAAE;gBAAAsK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACVtL,OAAA,CAACV,OAAO;YAACgO,KAAK,EAAC,uBAAuB;YAACpD,KAAK,EAAE;cAAEwB,MAAM,EAAE;YAAM,CAAE;YAAA/B,QAAA,eAChE3J,OAAA,CAACX,UAAU;cACVkM,IAAI,EAAC;cACL;cAAA;cACAN,OAAO,EAAErD,oBAAqB;cAC9B8F,QAAQ,EAAE,EAAA5K,sBAAA,GAAA2B,oBAAoB,CAACH,WAAW,GAAG,CAAC,CAAC,cAAAxB,sBAAA,wBAAAC,sBAAA,GAArCD,sBAAA,CAAuC6K,UAAU,cAAA5K,sBAAA,uBAAjDA,sBAAA,CAAmDuE,MAAM,MAAK,CAAE;cAC1EuC,EAAE,EAAE;gBACHc,SAAS,EAAE,iBAAiB;gBAAE;gBAC9B,SAAS,EAAE;kBACVA,SAAS,EAAE,iBAAiB,CAAE;gBAC/B;cACD,CAAE;cAAAhB,QAAA,eAEF3J,OAAA;gBACCwM,uBAAuB,EAAE;kBAAEC,MAAM,EAAEjN;gBAAW,CAAE;gBAChD0K,KAAK,EAAE;kBACNc,OAAO,EAAE,EAAAhI,sBAAA,GAAAyB,oBAAoB,CAACH,WAAW,GAAG,CAAC,CAAC,cAAAtB,sBAAA,wBAAAC,uBAAA,GAArCD,sBAAA,CAAuC2K,UAAU,cAAA1K,uBAAA,uBAAjDA,uBAAA,CAAmDqE,MAAM,MAAK,CAAC,GAAG,GAAG,GAAG,CAAC;kBAClFsG,aAAa,EAAE,MAAM;kBACrBhN,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE;gBACT;cAAE;gBAAAsK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEVtL,OAAA,CAACH,aAAa;QACbyI,uBAAuB,EAAEA,uBAAwB;QACjDjF,eAAe,EAAEA,eAAgB;QACjCkE,UAAU,EAAEA,UAAW;QACvB0B,kBAAkB,EAAEA,kBAAmB;QACvCtG,gBAAgB,EAAEA;MAAiB;QAAAwI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNtL,OAAA,CAACZ,OAAO;MACPyH,IAAI,EAAEE,eAAgB;MACtBjC,QAAQ,EAAEE,mBAAoB;MAC9B2H,OAAO,EAAE/F,sBAAuB;MAChCkG,YAAY,EAAE;QACbC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACb,CAAE;MACFC,eAAe,EAAE;QAChBF,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACb,CAAE;MACFrF,EAAE,EAAC,cAAc;MACjBuF,SAAS,EAAE;QACVC,IAAI,EAAE;UACL;UACAtD,EAAE,EAAE;YACH6B,MAAM,EAAG0B,KAAK,IAAKA,KAAK,CAAC1B,MAAM,CAAC2B,OAAO,GAAG;UAC3C;QACD;MACD,CAAE;MAAA1D,QAAA,eAEF3J,OAAA,CAACd,GAAG;QAAAyK,QAAA,gBACH3J,OAAA,CAACT,YAAY;UACZkH,KAAK,EAAE/D,gBAAgB,CAACwH,KAAK,CAACxD,eAAgB;UAC9CmH,QAAQ,EAAErH;QAAkB;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACFtL,OAAA;UAAA2J,QAAA,EACE;AACP;AACA;AACA;AACA;QAAK;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,eACT,CAAC;AAEL,CAAC;AAACzI,EAAA,CA1jBIL,aAAqG;EAAA,QAKjF1C,cAAc,EAoBnCH,cAAc;AAAA;AAAAmO,EAAA,GAzBbtL,aAAqG;AA4jB3G,eAAeA,aAAa;AAAC,IAAAsL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}