import React, { useContext, useState, useEffect } from 'react';
import { closeIcon, magicPen } from '../../../assets/icons/icons';
import { Box,Typography,IconButton } from '@mui/material';
import { GenerateImageWithUserPrompt, EnhanceUserPrompt } from '../../../services/AIService';
import { AccountContext } from '../../login/AccountContext';
import { FileUpload } from '../../../models/FileUpload';
import { timeStamp } from 'console';
import { useTranslation } from 'react-i18next';
import { useSnackbar } from '../guideList/SnackbarContext';
import useDrawerStore from '../../../store/drawerStore';
import CloseIcon from "@mui/icons-material/Close";

const ImageGenerationPopup = ({openGenAiImagePopup,setOpenGenAiImagePopup,handleImageUploadFormApp,setReplaceImage}:any) => {
  const { t: translate } = useTranslation();
  const { openSnackbar } = useSnackbar();
  const { imageAnchorEl } = useDrawerStore((state) => state);

  const [isHovered, setIsHovered] = useState(false);
  const [abortController, setAbortController] = useState<AbortController | null>(null);
  
  // Function to get guidepopup position and dimensions
  const getGuidePopupPosition = () => {
    const element = document.querySelector('.qadpt-guide-popup .MuiDialog-paper') ||
                    document.getElementById('guide-popup');
    if (element) {
      const rect = element.getBoundingClientRect();
      return {
        top: rect.top,
        left: rect.left,
        width: rect.width,
        height: rect.height
      };
    }
    return null;
  };

  // Function to calculate adaptive positioning
  const calculatePosition = () => {
    const guidePopupPos = getGuidePopupPosition();
    if (!guidePopupPos) {
      return {
        top: Math.max(20, (window.innerHeight - 500) / 2),
        left: 0
      };
    }

    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const popupWidth = 260;
    const popupHeight = 580;
    const gap = 15;
    const headerHeight = 56;
    const bottomPadding = 20;
    const topPadding = 20;

    // Calculate optimal vertical position
    const idealTop = (viewportHeight - popupHeight) / 2;
    const minTop = headerHeight + topPadding;
    const maxTop = viewportHeight - popupHeight - bottomPadding;
    
    const viewportCenteredTop = Math.max(minTop, Math.min(idealTop, maxTop));

    const requiredSpaceForExternal = guidePopupPos.left + guidePopupPos.width + gap + popupWidth;
    const hasSpaceForExternal = requiredSpaceForExternal <= viewportWidth - 20;

    if (hasSpaceForExternal) {
      // External positioning: to the right of guidepopup with gap
      const externalLeft = guidePopupPos.left + guidePopupPos.width + gap;
      return {
        top: viewportCenteredTop,
        left: Math.min(externalLeft, viewportWidth - popupWidth - 10),
      };
    } else {
      // Internal positioning: centered vertically in viewport
      const maxInternalLeft = guidePopupPos.left + guidePopupPos.width - popupWidth - 15;
      const minInternalLeft = guidePopupPos.left + 15;
      const internalLeft = Math.max(minInternalLeft, maxInternalLeft);
      
      return {
        top: viewportCenteredTop,
        left: Math.min(internalLeft, viewportWidth - popupWidth - 10), 
      };
    }
  };

  // Initialize with calculated position or default
  const [popupPosition, setPopupPosition] = useState(() => {
    if (openGenAiImagePopup) {
      return calculatePosition();
    }
    return {
      top: Math.max(20, (window.innerHeight - 500) / 2),
      left: -320 
    };
  });

  const [selectedStyle, setSelectedStyle] = useState('Professional');
  const [selectedColor, setSelectedColor] = useState('Black & White');
  const [selectedRatio, setSelectedRatio] = useState('16:9');

  const { accountId } = useContext(AccountContext);

  const styles = ['Professional', 'Formal', 'Friendly', 'Casual', 'Storytelling', 'Direct'];
  const colors = ['Black & White', 'Cold Neon', 'Vibrant', 'Softhue', 'Gradient', 'Retro'];
  const ratios = ['1:1', '16:9','9:16'];
  const [description, setDescription] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [isEnhancing, setIsEnhancing] = useState(false);

  // Update positioning when popup opens or window resizes
  useEffect(() => {
    if (openGenAiImagePopup) {
      const newPosition = calculatePosition();
      setPopupPosition(newPosition);

      const handleResize = () => {
        const updatedPosition = calculatePosition();
        setPopupPosition(updatedPosition);
      };

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, [openGenAiImagePopup]);

  const handleClose = () => {
    setOpenGenAiImagePopup(false);
  };

  const onImageGenerated = (imageUrl: any) => {
    
    // const timeStamp = () => new Date.now().toISOString(); // or Date.now()

    let file: any = {
      Url: imageUrl,
      FileName: `Generated Image ${Date.now()}`,
      IsAiGenerated: true,
    };
    
    handleImageUploadFormApp(file);
    setOpenGenAiImagePopup(false);
  }

  const handleEnhanceDescription = async () => {
    if (description.trim() === "") {
      openSnackbar("Please enter a description first.", "error");
      return;
    }

    setIsEnhancing(true);
    try {
      const enhancedPrompt = await EnhanceUserPrompt(description, accountId, openSnackbar);
      if (enhancedPrompt) {
        setDescription(enhancedPrompt);
      }
    } catch (error) {
      console.error("Error enhancing description:", error);
    } finally {
      setIsEnhancing(false);
    }
  };

  const GenerateImage = () => {
    if (description === "") return;

  // Validate that we have the correct image context
  // containerId is required, but buttonId can be empty for new image generation
    if (!imageAnchorEl.containerId) {
      openSnackbar("Error: Unable to identify which image container to use. Please try again.", "error");
      return;
    }

  setIsGenerating(true); // Start loader
    const controller = new AbortController();
    setAbortController(controller);

    const userPromptWithSelectedOptions = `User Asked: ${description} ${
      selectedStyle !== "" ? " and Image Should be in the Style " + selectedStyle : ""
    } ${
      selectedColor !== "" ? " and Image Should be in Color Palette " + selectedColor : ""
    } ${
      selectedRatio !== "" ? " and Image Should be in Aspect Ratio " + selectedRatio : ""
    }`;

    GenerateImageWithUserPrompt(userPromptWithSelectedOptions, accountId, (imageUrl: any) => {
      onImageGenerated(imageUrl);
    setIsGenerating(false); // Stop loader
      setAbortController(null);
    }, openSnackbar, controller.signal);
  };

  const blackMagicPen = magicPen.replace(/stroke="white"/g, 'stroke="black"');
  
  return (
    <>
      <div className='qadpt-genai-popup'
        style={{
          top: `${popupPosition.top}px`,
          left: openGenAiImagePopup ? `${popupPosition.left}px` : '-320px',
          transition: openGenAiImagePopup ? 'left 0.3s ease-in-out' : 'none' // Smooth transition when opening
        }}
      >
        <Box className="qadpt-genai-header">
          <div>{translate("Generate Images")}</div>
          <IconButton size="small" aria-label="close" onClick={handleClose}>
            <CloseIcon />
          </IconButton>
        </Box>
        
        <div>
          <div className="qadpt-desccont">
            <div className="qadpt-txt-cont">
              <textarea
              
                placeholder={translate("Please describe your image...")}
                value={description}
 onChange={(e) => {
    const el = e.target;
    el.style.height = 'auto'; 
    el.style.height = Math.min(el.scrollHeight, 70) + 'px';
    setDescription(e.target.value);
                }}
                className="qadpt-txtarea"
              />
            </div>

            <div className="qadpt-btncont">
              <button
                onClick={handleEnhanceDescription}
                disabled={isEnhancing || description.trim() === ""}
                className="qadpt-enhbtn"
              >
                {isEnhancing ? (
                  <div className="qadpt-enhancing" />
                ) : (
                  <span dangerouslySetInnerHTML={{ __html: blackMagicPen }} style={{ display: "flex" }} />
                )}
                {isEnhancing ? translate("Enhancing...") : translate("Enhance AI")}
              </button>
            </div>
          </div>

          <div className="qadpt-img-style">
            <div className="qadpt-hdr">{translate("Style")}</div>
            <div className="qadpt-options">
              {styles.map((style) => (
                <button
                  key={style}
                  onClick={() => setSelectedStyle(style)}
                  className={`qadpt-btnoption ${selectedStyle === style ? 'selected' : ''}`}
                  disabled={isGenerating}
                >
                  {translate(`${style}`)}
                </button>
              ))}
            </div>
          </div>

          <div className="qadpt-img-style">
            <div className="qadpt-hdr">{translate("Colors")}</div>
            <div className="qadpt-options">
              {colors.map((color) => (
                <button
                  key={color}
                  onClick={() => setSelectedColor(color)}
                  className={`qadpt-btnoption ${selectedColor === color ? 'selected' : ''}`}
                  disabled={isGenerating}
                >
                   {translate(`${color}`)}
                </button>
              ))}
            </div>
          </div>

          <div className="qadpt-img-style">
            <div className="qadpt-hdr">{translate("Ratio")}</div>
            <div className="qadpt-options">
              {ratios.map((ratio) => (
                <button
                  key={ratio}
                  onClick={() => setSelectedRatio(ratio)}
                  className={`qadpt-btnoption ${selectedRatio === ratio ? 'selected' : ''}`}
                  disabled={isGenerating}
                >
                  {ratio}
                </button>
              ))}
            </div>
          </div>
        </div>

        <button
          onClick={() => {
            if (isGenerating) {
              abortController?.abort();
              setIsGenerating(false);
              setAbortController(null);
              openSnackbar(translate("Generation stopped."), "success");
            } else if (description.trim() !== '') {
              GenerateImage();
            }
          }}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          disabled={isGenerating && !abortController}
          className={`qadpr-genbtn-cont ${isGenerating ? 'generating' : ''} ${
            description.trim() === '' ? 'disabled' : ''
          }`}
        >
          {isGenerating ? (
            <>
              <div className="qadpt-generating" />
              {isHovered ? translate('Stop Generation') : translate('Generating...')}
            </>
          ) : (
            <>
              <span dangerouslySetInnerHTML={{ __html: magicPen }} style={{ display: "flex" }} />
              {translate('Generate Image')}
            </>
          )}
        </button>
      </div>
    </>
  );
};

export default ImageGenerationPopup;
