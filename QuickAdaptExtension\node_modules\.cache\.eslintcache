[{"E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\index.tsx": "1", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\reportWebVitals.ts": "2", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\App.tsx": "3", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\store\\UserInfoStore.ts": "4", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\AuthProvider.tsx": "5", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\AccountContext.tsx": "6", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Drawer.tsx": "7", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\SnackbarContext.tsx": "8", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\store\\drawerStore.ts": "9", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\store\\historyStore.ts": "10", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\store\\userSession.ts": "11", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\UseAuth.tsx": "12", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuidePopUp.tsx": "13", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuideSettings.tsx": "14", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\ExtensionLogin.tsx": "15", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\LogoutPopup.tsx": "16", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\GuideMenuOptions.tsx": "17", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\services\\UserService.ts": "18", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\services\\UserRoleService.ts": "19", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\services\\ScrapingService.ts": "20", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\services\\SaveGuideService.tsx": "21", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\services\\GuideListServices.tsx": "22", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\assets\\icons\\icons.ts": "23", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\UndoRedoButtons.tsx": "24", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistLauncherPreview.tsx": "25", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\TrainingField.tsx": "26", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPopup.tsx": "27", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\Banners.tsx": "28", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Design.tsx": "29", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Banner.tsx": "30", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\AnnouncementPreview.tsx": "31", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\HotspotPreview.tsx": "32", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\Tooltip.tsx": "33", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourTemplate.tsx": "34", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\stepPopup.tsx": "35", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourPreview.tsx": "36", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltipuserview.tsx": "37", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\services\\APIService.tsx": "38", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\services\\OrganizationService.ts": "39", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\OidcConfig.ts": "40", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\AlertPopup.tsx": "41", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\services\\LoginService.tsx": "42", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PageTrigger.tsx": "43", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementRules.tsx": "44", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\StopScrapingButton.tsx": "45", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\PopupList.tsx": "46", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\utils\\historyUtils.ts": "47", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\services\\FileService.tsx": "48", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPreview.tsx": "49", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCheckIcon.tsx": "50", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ImageCarousel.tsx": "51", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\LauncherSettings.tsx": "52", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\VideoPlayer.tsx": "53", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\Chekpoints.tsx": "54", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CanvasSettings.tsx": "55", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Button.tsx": "56", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\RTEsection.tsx": "57", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Imagesection.tsx": "58", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\HtmlSection.tsx": "59", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\VideoSection.tsx": "60", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\CreateWithAIButton.tsx": "61", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\ModernChatWindow.tsx": "62", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Overlay.tsx": "63", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CustomCss.tsx": "64", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Animation.tsx": "65", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementsSettings.tsx": "66", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCanvasSettings.tsx": "67", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\TitleSubTitle.tsx": "68", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\TooltipBody.tsx": "69", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageSectionField.tsx": "70", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\PageInteraction.tsx": "71", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ButtonSettings.tsx": "72", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\BannerStepPreview.tsx": "73", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\hotspot\\HotspotSettings.tsx": "74", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltips.tsx": "75", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\designFields\\TooltipCanvasSettings.tsx": "76", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\CloneGuidePopUp.tsx": "77", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\TimeZoneConversion.tsx": "78", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointEditPopup.tsx": "79", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointAddPopup.tsx": "80", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\DraggableCheckpoint.tsx": "81", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\services\\AIService.ts": "82", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\services\\SpeechRecognitionService.ts": "83", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\SelectImageFromApplication.tsx": "84", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageGalleryPopup.tsx": "85", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageProperties.tsx": "86", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\Buttons.tsx": "87", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ImageSection.tsx": "88", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\RTE\\RTESection.tsx": "89", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ButtonSetting.tsx": "90", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\i18n.ts": "91", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\contexts\\TranslationContext.tsx": "92", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\LanguageService.ts": "93", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\LanguageSelector.tsx": "94", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\AgentTraining.tsx": "95", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\AgentAdditionalContextPopup.tsx": "96", "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\ImageGenerationPopup.tsx": "97"}, {"size": 604, "mtime": 1752751669320, "results": "98", "hashOfConfig": "99"}, {"size": 440, "mtime": 1752751669320, "results": "100", "hashOfConfig": "99"}, {"size": 2871, "mtime": 1754569275437, "results": "101", "hashOfConfig": "99"}, {"size": 3344, "mtime": 1753685879005, "results": "102", "hashOfConfig": "99"}, {"size": 6890, "mtime": 1753685878927, "results": "103", "hashOfConfig": "99"}, {"size": 3102, "mtime": 1753685902990, "results": "104", "hashOfConfig": "99"}, {"size": 251295, "mtime": 1754651101464, "results": "105", "hashOfConfig": "99"}, {"size": 3112, "mtime": 1753685878989, "results": "106", "hashOfConfig": "99"}, {"size": 401488, "mtime": 1754569275500, "results": "107", "hashOfConfig": "99"}, {"size": 3927, "mtime": 1753071777590, "results": "108", "hashOfConfig": "99"}, {"size": 6144, "mtime": 1753071777590, "results": "109", "hashOfConfig": "99"}, {"size": 193, "mtime": 1752751669289, "results": "110", "hashOfConfig": "99"}, {"size": 42431, "mtime": 1754569275485, "results": "111", "hashOfConfig": "99"}, {"size": 5077, "mtime": 1754569275485, "results": "112", "hashOfConfig": "99"}, {"size": 14118, "mtime": 1754569275500, "results": "113", "hashOfConfig": "99"}, {"size": 3731, "mtime": 1753685878974, "results": "114", "hashOfConfig": "99"}, {"size": 13630, "mtime": 1754569275485, "results": "115", "hashOfConfig": "99"}, {"size": 1898, "mtime": 1753071777575, "results": "116", "hashOfConfig": "99"}, {"size": 677, "mtime": 1753685879005, "results": "117", "hashOfConfig": "99"}, {"size": 56751, "mtime": 1753685879005, "results": "118", "hashOfConfig": "99"}, {"size": 1956, "mtime": 1754377327517, "results": "119", "hashOfConfig": "99"}, {"size": 9087, "mtime": 1753685879005, "results": "120", "hashOfConfig": "99"}, {"size": 312887, "mtime": 1754569275453, "results": "121", "hashOfConfig": "99"}, {"size": 3012, "mtime": 1754569275469, "results": "122", "hashOfConfig": "99"}, {"size": 9097, "mtime": 1753685878942, "results": "123", "hashOfConfig": "99"}, {"size": 2892, "mtime": 1754377327491, "results": "124", "hashOfConfig": "99"}, {"size": 30684, "mtime": 1754569275464, "results": "125", "hashOfConfig": "99"}, {"size": 23362, "mtime": 1754569275469, "results": "126", "hashOfConfig": "99"}, {"size": 33251, "mtime": 1754569275469, "results": "127", "hashOfConfig": "99"}, {"size": 13588, "mtime": 1754569275453, "results": "128", "hashOfConfig": "99"}, {"size": 27259, "mtime": 1754569275453, "results": "129", "hashOfConfig": "99"}, {"size": 50578, "mtime": 1754569275453, "results": "130", "hashOfConfig": "99"}, {"size": 32522, "mtime": 1753853650213, "results": "131", "hashOfConfig": "99"}, {"size": 7599, "mtime": 1753685879005, "results": "132", "hashOfConfig": "99"}, {"size": 11556, "mtime": 1754569275500, "results": "133", "hashOfConfig": "99"}, {"size": 27699, "mtime": 1754569275500, "results": "134", "hashOfConfig": "99"}, {"size": 4880, "mtime": 1753071777543, "results": "135", "hashOfConfig": "99"}, {"size": 2608, "mtime": 1753071777575, "results": "136", "hashOfConfig": "99"}, {"size": 9238, "mtime": 1753071777575, "results": "137", "hashOfConfig": "99"}, {"size": 743, "mtime": 1752751669289, "results": "138", "hashOfConfig": "99"}, {"size": 2749, "mtime": 1754569275469, "results": "139", "hashOfConfig": "99"}, {"size": 1248, "mtime": 1752751669320, "results": "140", "hashOfConfig": "99"}, {"size": 3285, "mtime": 1754569275485, "results": "141", "hashOfConfig": "99"}, {"size": 2997, "mtime": 1753685878974, "results": "142", "hashOfConfig": "99"}, {"size": 2052, "mtime": 1753685878911, "results": "143", "hashOfConfig": "99"}, {"size": 20180, "mtime": 1753961420679, "results": "144", "hashOfConfig": "99"}, {"size": 14238, "mtime": 1753071777590, "results": "145", "hashOfConfig": "99"}, {"size": 1594, "mtime": 1754569275500, "results": "146", "hashOfConfig": "99"}, {"size": 29119, "mtime": 1753685878942, "results": "147", "hashOfConfig": "99"}, {"size": 1962, "mtime": 1752751669289, "results": "148", "hashOfConfig": "99"}, {"size": 2423, "mtime": 1753685902990, "results": "149", "hashOfConfig": "99"}, {"size": 27254, "mtime": 1754569275469, "results": "150", "hashOfConfig": "99"}, {"size": 702, "mtime": 1753685878958, "results": "151", "hashOfConfig": "99"}, {"size": 13889, "mtime": 1753685878958, "results": "152", "hashOfConfig": "99"}, {"size": 19214, "mtime": 1753869665261, "results": "153", "hashOfConfig": "99"}, {"size": 16794, "mtime": 1754569275485, "results": "154", "hashOfConfig": "99"}, {"size": 48076, "mtime": 1754888925025, "results": "155", "hashOfConfig": "99"}, {"size": 34290, "mtime": 1754887684712, "results": "156", "hashOfConfig": "99"}, {"size": 6245, "mtime": 1752751669304, "results": "157", "hashOfConfig": "99"}, {"size": 7772, "mtime": 1753685878989, "results": "158", "hashOfConfig": "99"}, {"size": 2034, "mtime": 1753685878911, "results": "159", "hashOfConfig": "99"}, {"size": 29744, "mtime": 1753685878911, "results": "160", "hashOfConfig": "99"}, {"size": 6625, "mtime": 1753685878974, "results": "161", "hashOfConfig": "99"}, {"size": 2848, "mtime": 1752751669304, "results": "162", "hashOfConfig": "99"}, {"size": 3236, "mtime": 1752751669304, "results": "163", "hashOfConfig": "99"}, {"size": 20321, "mtime": 1753685878974, "results": "164", "hashOfConfig": "99"}, {"size": 15264, "mtime": 1753869665261, "results": "165", "hashOfConfig": "99"}, {"size": 15261, "mtime": 1753685878958, "results": "166", "hashOfConfig": "99"}, {"size": 33829, "mtime": 1754569275453, "results": "167", "hashOfConfig": "99"}, {"size": 8476, "mtime": 1753685878974, "results": "168", "hashOfConfig": "99"}, {"size": 11585, "mtime": 1754398695146, "results": "169", "hashOfConfig": "99"}, {"size": 17429, "mtime": 1754887659847, "results": "170", "hashOfConfig": "99"}, {"size": 16332, "mtime": 1754398695162, "results": "171", "hashOfConfig": "99"}, {"size": 17039, "mtime": 1754569275485, "results": "172", "hashOfConfig": "99"}, {"size": 61442, "mtime": 1753961420667, "results": "173", "hashOfConfig": "99"}, {"size": 26698, "mtime": 1753685878927, "results": "174", "hashOfConfig": "99"}, {"size": 5258, "mtime": 1753685878989, "results": "175", "hashOfConfig": "99"}, {"size": 883, "mtime": 1752751669320, "results": "176", "hashOfConfig": "99"}, {"size": 33137, "mtime": 1754569275469, "results": "177", "hashOfConfig": "99"}, {"size": 37236, "mtime": 1754569275464, "results": "178", "hashOfConfig": "99"}, {"size": 5504, "mtime": 1753685878958, "results": "179", "hashOfConfig": "99"}, {"size": 3070, "mtime": 1754569275500, "results": "180", "hashOfConfig": "99"}, {"size": 7943, "mtime": 1753071777575, "results": "181", "hashOfConfig": "99"}, {"size": 6969, "mtime": 1754569275469, "results": "182", "hashOfConfig": "99"}, {"size": 2669, "mtime": 1752751669304, "results": "183", "hashOfConfig": "99"}, {"size": 2931, "mtime": 1753866861445, "results": "184", "hashOfConfig": "99"}, {"size": 17528, "mtime": 1754569275453, "results": "185", "hashOfConfig": "99"}, {"size": 28719, "mtime": 1754569275453, "results": "186", "hashOfConfig": "99"}, {"size": 19267, "mtime": 1754569275464, "results": "187", "hashOfConfig": "99"}, {"size": 15667, "mtime": 1753685902974, "results": "188", "hashOfConfig": "99"}, {"size": 6886, "mtime": 1753685879005, "results": "189", "hashOfConfig": "99"}, {"size": 7158, "mtime": 1753685879005, "results": "190", "hashOfConfig": "99"}, {"size": 1211, "mtime": 1753685879005, "results": "191", "hashOfConfig": "99"}, {"size": 10166, "mtime": 1754569275469, "results": "192", "hashOfConfig": "99"}, {"size": 3836, "mtime": 1753685878911, "results": "193", "hashOfConfig": "99"}, {"size": 3021, "mtime": 1753685878911, "results": "194", "hashOfConfig": "99"}, {"size": 11798, "mtime": 1754569275485, "results": "195", "hashOfConfig": "99"}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qwr42p", {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 229, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 40, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 61, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 50, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 54, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 57, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 32, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 31, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 51, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 63, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 49, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 62, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 32, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\index.tsx", [], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\reportWebVitals.ts", [], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\App.tsx", ["487", "488", "489"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\store\\UserInfoStore.ts", ["490"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\AuthProvider.tsx", ["491", "492", "493", "494", "495", "496"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\AccountContext.tsx", [], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Drawer.tsx", ["497", "498", "499", "500", "501", "502", "503", "504", "505", "506", "507", "508", "509", "510", "511", "512", "513", "514", "515", "516", "517", "518", "519", "520", "521", "522", "523", "524", "525", "526", "527", "528", "529", "530", "531", "532", "533", "534", "535", "536", "537", "538", "539", "540", "541", "542", "543", "544", "545", "546", "547", "548", "549", "550", "551", "552", "553", "554", "555", "556", "557", "558", "559", "560", "561", "562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572", "573", "574", "575", "576", "577", "578", "579", "580", "581", "582", "583", "584", "585", "586", "587", "588", "589", "590", "591", "592", "593", "594", "595", "596", "597", "598", "599", "600", "601", "602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661", "662", "663", "664", "665", "666", "667", "668", "669", "670", "671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706", "707", "708", "709", "710", "711", "712", "713", "714", "715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\SnackbarContext.tsx", ["726", "727"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\store\\drawerStore.ts", ["728", "729", "730", "731", "732", "733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743", "744", "745", "746", "747", "748", "749"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\store\\historyStore.ts", ["750"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\store\\userSession.ts", ["751"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\UseAuth.tsx", [], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuidePopUp.tsx", ["752", "753", "754", "755", "756", "757", "758", "759", "760", "761", "762", "763", "764", "765", "766", "767", "768", "769", "770", "771", "772", "773", "774", "775", "776", "777", "778", "779", "780", "781", "782", "783", "784", "785", "786", "787", "788", "789", "790", "791"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuideSettings.tsx", [], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\ExtensionLogin.tsx", ["792", "793", "794", "795", "796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\LogoutPopup.tsx", ["808"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\GuideMenuOptions.tsx", ["809", "810", "811", "812", "813", "814", "815", "816", "817", "818", "819", "820", "821", "822", "823", "824", "825", "826", "827", "828", "829", "830", "831", "832"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\services\\UserService.ts", [], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\services\\UserRoleService.ts", ["833"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\services\\ScrapingService.ts", ["834", "835", "836", "837", "838", "839", "840", "841", "842", "843", "844", "845", "846", "847", "848"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\services\\SaveGuideService.tsx", [], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\services\\GuideListServices.tsx", ["849", "850", "851"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\assets\\icons\\icons.ts", [], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\UndoRedoButtons.tsx", ["852", "853", "854", "855"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistLauncherPreview.tsx", ["856", "857", "858", "859", "860", "861", "862", "863"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\TrainingField.tsx", ["864", "865", "866", "867", "868", "869", "870", "871", "872", "873", "874", "875", "876", "877", "878", "879"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPopup.tsx", ["880", "881", "882", "883", "884", "885", "886", "887", "888", "889", "890", "891", "892", "893", "894", "895", "896", "897", "898", "899", "900", "901", "902", "903", "904", "905", "906"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\Banners.tsx", ["907", "908", "909", "910", "911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921", "922", "923", "924", "925", "926", "927"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Design.tsx", ["928", "929", "930", "931", "932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947", "948", "949", "950", "951", "952", "953", "954", "955", "956", "957", "958", "959", "960", "961", "962", "963", "964", "965", "966", "967", "968", "969", "970", "971", "972", "973", "974", "975", "976", "977", "978", "979", "980", "981", "982", "983", "984", "985", "986", "987", "988"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Banner.tsx", ["989", "990", "991", "992", "993", "994", "995", "996", "997", "998", "999", "1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\AnnouncementPreview.tsx", ["1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027", "1028", "1029", "1030", "1031", "1032", "1033", "1034"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\HotspotPreview.tsx", ["1035", "1036", "1037", "1038", "1039", "1040", "1041", "1042", "1043", "1044", "1045", "1046", "1047", "1048", "1049", "1050", "1051", "1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\Tooltip.tsx", ["1060", "1061", "1062", "1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086", "1087", "1088", "1089", "1090", "1091", "1092", "1093", "1094", "1095", "1096", "1097", "1098", "1099", "1100", "1101", "1102", "1103", "1104", "1105", "1106", "1107", "1108", "1109"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourTemplate.tsx", ["1110", "1111", "1112", "1113", "1114", "1115", "1116", "1117", "1118", "1119"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\stepPopup.tsx", ["1120", "1121", "1122", "1123", "1124", "1125", "1126", "1127", "1128", "1129"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourPreview.tsx", ["1130", "1131", "1132", "1133", "1134", "1135", "1136", "1137", "1138", "1139", "1140", "1141", "1142", "1143", "1144", "1145", "1146", "1147", "1148", "1149", "1150"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltipuserview.tsx", ["1151", "1152", "1153", "1154", "1155", "1156"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\services\\APIService.tsx", [], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\services\\OrganizationService.ts", ["1157", "1158", "1159", "1160"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\OidcConfig.ts", [], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\AlertPopup.tsx", ["1161", "1162", "1163"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\services\\LoginService.tsx", ["1164", "1165", "1166"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PageTrigger.tsx", ["1167"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementRules.tsx", ["1168", "1169"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\StopScrapingButton.tsx", [], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\PopupList.tsx", ["1170", "1171", "1172", "1173", "1174", "1175", "1176", "1177", "1178", "1179", "1180", "1181", "1182", "1183"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\utils\\historyUtils.ts", [], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\services\\FileService.tsx", ["1184", "1185"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPreview.tsx", ["1186", "1187", "1188", "1189", "1190", "1191", "1192", "1193", "1194", "1195", "1196"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCheckIcon.tsx", [], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ImageCarousel.tsx", [], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\LauncherSettings.tsx", ["1197", "1198", "1199", "1200", "1201", "1202", "1203", "1204", "1205", "1206", "1207", "1208", "1209", "1210", "1211", "1212", "1213", "1214", "1215", "1216", "1217", "1218", "1219", "1220", "1221", "1222", "1223", "1224", "1225", "1226", "1227", "1228", "1229", "1230", "1231", "1232", "1233", "1234", "1235", "1236", "1237", "1238", "1239", "1240", "1241", "1242", "1243", "1244", "1245", "1246", "1247", "1248", "1249", "1250"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\VideoPlayer.tsx", [], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\Chekpoints.tsx", ["1251", "1252", "1253", "1254", "1255", "1256", "1257", "1258", "1259", "1260", "1261", "1262", "1263", "1264", "1265", "1266", "1267", "1268", "1269", "1270", "1271", "1272", "1273", "1274", "1275", "1276", "1277", "1278", "1279", "1280", "1281", "1282", "1283", "1284", "1285", "1286", "1287", "1288", "1289", "1290", "1291", "1292", "1293", "1294", "1295", "1296", "1297", "1298", "1299", "1300", "1301", "1302", "1303", "1304", "1305", "1306", "1307"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CanvasSettings.tsx", ["1308", "1309", "1310", "1311"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Button.tsx", ["1312", "1313", "1314", "1315", "1316", "1317", "1318", "1319", "1320", "1321", "1322", "1323", "1324", "1325", "1326", "1327", "1328", "1329", "1330", "1331", "1332", "1333", "1334", "1335", "1336", "1337", "1338"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\RTEsection.tsx", ["1339", "1340", "1341", "1342", "1343", "1344", "1345", "1346", "1347"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Imagesection.tsx", ["1348", "1349", "1350", "1351", "1352", "1353", "1354"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\HtmlSection.tsx", [], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\VideoSection.tsx", [], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\CreateWithAIButton.tsx", ["1355", "1356", "1357", "1358", "1359"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\ModernChatWindow.tsx", ["1360", "1361", "1362", "1363", "1364", "1365", "1366", "1367", "1368", "1369", "1370", "1371", "1372", "1373"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Overlay.tsx", ["1374", "1375", "1376", "1377", "1378", "1379"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CustomCss.tsx", ["1380", "1381"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Animation.tsx", ["1382", "1383", "1384"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementsSettings.tsx", ["1385", "1386", "1387", "1388", "1389", "1390", "1391", "1392", "1393", "1394", "1395", "1396", "1397", "1398", "1399", "1400", "1401", "1402", "1403", "1404"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCanvasSettings.tsx", ["1405", "1406", "1407", "1408", "1409", "1410", "1411", "1412", "1413", "1414", "1415", "1416", "1417", "1418", "1419", "1420", "1421", "1422", "1423", "1424", "1425", "1426", "1427", "1428", "1429", "1430", "1431", "1432", "1433", "1434", "1435", "1436"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\TitleSubTitle.tsx", ["1437", "1438", "1439", "1440", "1441", "1442", "1443", "1444", "1445", "1446", "1447", "1448", "1449", "1450", "1451", "1452", "1453", "1454", "1455", "1456", "1457", "1458", "1459", "1460", "1461", "1462", "1463", "1464", "1465", "1466", "1467"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\TooltipBody.tsx", ["1468", "1469", "1470", "1471", "1472", "1473", "1474", "1475", "1476", "1477", "1478", "1479", "1480", "1481", "1482", "1483", "1484", "1485", "1486", "1487", "1488", "1489", "1490", "1491", "1492", "1493", "1494", "1495", "1496", "1497", "1498", "1499", "1500", "1501", "1502", "1503", "1504", "1505", "1506", "1507", "1508", "1509", "1510", "1511", "1512", "1513", "1514", "1515", "1516", "1517", "1518"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageSectionField.tsx", ["1519", "1520", "1521", "1522", "1523", "1524", "1525", "1526"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\PageInteraction.tsx", ["1527", "1528", "1529", "1530", "1531", "1532", "1533", "1534", "1535", "1536", "1537", "1538", "1539", "1540", "1541", "1542", "1543", "1544", "1545", "1546", "1547", "1548", "1549", "1550"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ButtonSettings.tsx", ["1551", "1552", "1553", "1554", "1555", "1556", "1557", "1558", "1559", "1560", "1561", "1562", "1563", "1564", "1565", "1566", "1567", "1568", "1569", "1570", "1571", "1572", "1573", "1574", "1575", "1576", "1577", "1578", "1579", "1580", "1581", "1582", "1583", "1584", "1585", "1586", "1587", "1588", "1589", "1590", "1591", "1592", "1593", "1594", "1595", "1596", "1597", "1598", "1599", "1600", "1601", "1602", "1603", "1604", "1605", "1606", "1607", "1608", "1609", "1610", "1611", "1612", "1613"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\BannerStepPreview.tsx", ["1614", "1615", "1616", "1617", "1618", "1619", "1620", "1621", "1622", "1623", "1624", "1625", "1626", "1627", "1628", "1629", "1630", "1631", "1632", "1633"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\hotspot\\HotspotSettings.tsx", ["1634", "1635", "1636", "1637", "1638", "1639", "1640", "1641", "1642", "1643", "1644", "1645", "1646", "1647", "1648", "1649", "1650", "1651", "1652"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltips.tsx", ["1653", "1654", "1655", "1656", "1657", "1658", "1659", "1660", "1661", "1662", "1663", "1664", "1665", "1666", "1667", "1668"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\designFields\\TooltipCanvasSettings.tsx", ["1669", "1670", "1671", "1672", "1673", "1674", "1675", "1676", "1677", "1678", "1679", "1680", "1681", "1682", "1683", "1684", "1685"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\CloneGuidePopUp.tsx", ["1686", "1687"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\TimeZoneConversion.tsx", [], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointEditPopup.tsx", ["1688", "1689", "1690", "1691", "1692", "1693", "1694", "1695", "1696", "1697", "1698", "1699", "1700", "1701", "1702", "1703", "1704", "1705", "1706", "1707", "1708", "1709", "1710", "1711", "1712", "1713", "1714", "1715", "1716", "1717", "1718", "1719", "1720", "1721", "1722", "1723", "1724", "1725", "1726", "1727", "1728", "1729", "1730", "1731", "1732", "1733", "1734", "1735", "1736"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointAddPopup.tsx", ["1737", "1738", "1739", "1740", "1741", "1742", "1743", "1744", "1745", "1746", "1747", "1748", "1749", "1750", "1751", "1752", "1753", "1754", "1755", "1756", "1757", "1758", "1759", "1760", "1761", "1762", "1763", "1764", "1765", "1766", "1767", "1768", "1769", "1770", "1771", "1772", "1773", "1774", "1775", "1776", "1777", "1778", "1779", "1780", "1781", "1782", "1783", "1784", "1785", "1786", "1787", "1788", "1789", "1790", "1791", "1792", "1793", "1794", "1795", "1796", "1797", "1798"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\DraggableCheckpoint.tsx", ["1799", "1800", "1801", "1802", "1803", "1804", "1805", "1806", "1807", "1808", "1809", "1810", "1811", "1812", "1813", "1814", "1815", "1816", "1817", "1818", "1819", "1820", "1821", "1822", "1823", "1824", "1825", "1826", "1827"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\services\\AIService.ts", [], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\services\\SpeechRecognitionService.ts", [], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\SelectImageFromApplication.tsx", ["1828", "1829", "1830", "1831", "1832", "1833", "1834", "1835", "1836", "1837", "1838", "1839"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageGalleryPopup.tsx", ["1840", "1841"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageProperties.tsx", ["1842", "1843", "1844", "1845", "1846", "1847", "1848", "1849", "1850", "1851", "1852", "1853", "1854", "1855"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\Buttons.tsx", ["1856", "1857", "1858", "1859", "1860", "1861", "1862", "1863", "1864", "1865", "1866"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ImageSection.tsx", ["1867", "1868", "1869", "1870", "1871", "1872", "1873", "1874", "1875", "1876", "1877", "1878", "1879", "1880"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\RTE\\RTESection.tsx", ["1881", "1882", "1883", "1884", "1885", "1886", "1887", "1888", "1889", "1890", "1891", "1892", "1893", "1894", "1895", "1896", "1897", "1898", "1899", "1900", "1901", "1902", "1903", "1904", "1905", "1906", "1907", "1908", "1909", "1910", "1911", "1912"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ButtonSetting.tsx", ["1913", "1914", "1915", "1916", "1917", "1918", "1919", "1920", "1921", "1922", "1923", "1924", "1925"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\i18n.ts", ["1926", "1927", "1928"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\contexts\\TranslationContext.tsx", ["1929", "1930"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\LanguageService.ts", [], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\LanguageSelector.tsx", ["1931"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\AgentTraining.tsx", ["1932", "1933", "1934", "1935", "1936", "1937", "1938", "1939", "1940", "1941", "1942", "1943", "1944", "1945", "1946", "1947", "1948", "1949", "1950", "1951"], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\AgentAdditionalContextPopup.tsx", [], [], "E:\\Code\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\ImageGenerationPopup.tsx", ["1952", "1953", "1954", "1955", "1956"], [], {"ruleId": "1957", "severity": 1, "message": "1958", "line": 3, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "1961", "line": 9, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "1962", "line": 16, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 16, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "1963", "line": 1, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "1964", "line": 2, "column": 16, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "1965", "line": 7, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 7, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "1966", "line": 7, "column": 23, "nodeType": "1959", "messageId": "1960", "endLine": 7, "endColumn": 34}, {"ruleId": "1957", "severity": 1, "message": "1967", "line": 98, "column": 13, "nodeType": "1959", "messageId": "1960", "endLine": 98, "endColumn": 25}, {"ruleId": "1968", "severity": 1, "message": "1969", "line": 103, "column": 6, "nodeType": "1970", "endLine": 103, "endColumn": 8, "suggestions": "1971"}, {"ruleId": "1957", "severity": 1, "message": "1972", "line": 148, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 148, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "1973", "line": 1, "column": 58, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 65}, {"ruleId": "1957", "severity": 1, "message": "1974", "line": 5, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "1975", "line": 6, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 6, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "1976", "line": 7, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 7, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "1977", "line": 14, "column": 28, "nodeType": "1959", "messageId": "1960", "endLine": 14, "endColumn": 40}, {"ruleId": "1957", "severity": 1, "message": "1978", "line": 19, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 19, "endColumn": 9}, {"ruleId": "1957", "severity": 1, "message": "1979", "line": 24, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 24, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "1980", "line": 25, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 25, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "1981", "line": 26, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 26, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "1982", "line": 27, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 27, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "1983", "line": 28, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 28, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "1984", "line": 29, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 29, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "1985", "line": 30, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 30, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "1986", "line": 31, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 31, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "1987", "line": 32, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 32, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "1988", "line": 33, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 33, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "1989", "line": 34, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 34, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "1990", "line": 35, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 35, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "1991", "line": 36, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 36, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "1992", "line": 37, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 37, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "1993", "line": 39, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 39, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "1994", "line": 40, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 40, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "1995", "line": 41, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 41, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "1996", "line": 42, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 42, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "1997", "line": 46, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 46, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "1998", "line": 52, "column": 20, "nodeType": "1959", "messageId": "1960", "endLine": 52, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "1999", "line": 62, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 62, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2000", "line": 63, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 63, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2001", "line": 70, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 70, "endColumn": 6}, {"ruleId": "1957", "severity": 1, "message": "2002", "line": 71, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 71, "endColumn": 6}, {"ruleId": "1957", "severity": 1, "message": "2003", "line": 78, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 78, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2004", "line": 80, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 80, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2005", "line": 81, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 81, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2006", "line": 81, "column": 30, "nodeType": "1959", "messageId": "1960", "endLine": 81, "endColumn": 39}, {"ruleId": "1957", "severity": 1, "message": "2007", "line": 84, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 84, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2008", "line": 85, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 85, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2009", "line": 86, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 86, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2010", "line": 90, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 90, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2011", "line": 92, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 92, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2012", "line": 98, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 98, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2013", "line": 105, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 105, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2014", "line": 108, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 108, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2015", "line": 109, "column": 26, "nodeType": "1959", "messageId": "1960", "endLine": 109, "endColumn": 37}, {"ruleId": "1957", "severity": 1, "message": "2016", "line": 114, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 114, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2017", "line": 114, "column": 21, "nodeType": "1959", "messageId": "1960", "endLine": 114, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2018", "line": 117, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 117, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2019", "line": 124, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 124, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2020", "line": 132, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 132, "endColumn": 35}, {"ruleId": "1957", "severity": 1, "message": "2021", "line": 138, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 138, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2022", "line": 201, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 201, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2023", "line": 218, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 218, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2024", "line": 226, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 226, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2025", "line": 382, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 382, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2026", "line": 417, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 417, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2027", "line": 419, "column": 6, "nodeType": "1959", "messageId": "1960", "endLine": 419, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2028", "line": 422, "column": 6, "nodeType": "1959", "messageId": "1960", "endLine": 422, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2029", "line": 438, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 438, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2030", "line": 439, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 439, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2031", "line": 441, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 441, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2032", "line": 444, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 444, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2033", "line": 448, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 448, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2034", "line": 449, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 449, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2035", "line": 460, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 460, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2036", "line": 461, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 461, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2037", "line": 462, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 462, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2038", "line": 464, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 464, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2039", "line": 464, "column": 26, "nodeType": "1959", "messageId": "1960", "endLine": 464, "endColumn": 44}, {"ruleId": "1957", "severity": 1, "message": "2040", "line": 469, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 469, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2041", "line": 469, "column": 23, "nodeType": "1959", "messageId": "1960", "endLine": 469, "endColumn": 38}, {"ruleId": "1957", "severity": 1, "message": "2042", "line": 471, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 471, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2043", "line": 471, "column": 19, "nodeType": "1959", "messageId": "1960", "endLine": 471, "endColumn": 30}, {"ruleId": "1957", "severity": 1, "message": "2044", "line": 474, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 474, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2045", "line": 474, "column": 24, "nodeType": "1959", "messageId": "1960", "endLine": 474, "endColumn": 40}, {"ruleId": "1957", "severity": 1, "message": "2046", "line": 475, "column": 19, "nodeType": "1959", "messageId": "1960", "endLine": 475, "endColumn": 30}, {"ruleId": "1957", "severity": 1, "message": "2047", "line": 480, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 480, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2048", "line": 480, "column": 29, "nodeType": "1959", "messageId": "1960", "endLine": 480, "endColumn": 50}, {"ruleId": "1957", "severity": 1, "message": "2049", "line": 487, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 487, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2050", "line": 487, "column": 16, "nodeType": "1959", "messageId": "1960", "endLine": 487, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2051", "line": 489, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 489, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2052", "line": 489, "column": 27, "nodeType": "1959", "messageId": "1960", "endLine": 489, "endColumn": 41}, {"ruleId": "1957", "severity": 1, "message": "2053", "line": 491, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 491, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2054", "line": 491, "column": 16, "nodeType": "1959", "messageId": "1960", "endLine": 491, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2055", "line": 505, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 505, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2056", "line": 506, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 506, "endColumn": 31}, {"ruleId": "1957", "severity": 1, "message": "2057", "line": 506, "column": 33, "nodeType": "1959", "messageId": "1960", "endLine": 506, "endColumn": 58}, {"ruleId": "1957", "severity": 1, "message": "2058", "line": 509, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 509, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2059", "line": 509, "column": 19, "nodeType": "1959", "messageId": "1960", "endLine": 509, "endColumn": 30}, {"ruleId": "1957", "severity": 1, "message": "2060", "line": 510, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 510, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2061", "line": 510, "column": 17, "nodeType": "1959", "messageId": "1960", "endLine": 510, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2062", "line": 511, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 511, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2063", "line": 511, "column": 21, "nodeType": "1959", "messageId": "1960", "endLine": 511, "endColumn": 34}, {"ruleId": "1957", "severity": 1, "message": "2064", "line": 520, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 520, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2065", "line": 521, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 521, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2066", "line": 527, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 527, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2067", "line": 531, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 531, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2068", "line": 531, "column": 26, "nodeType": "1959", "messageId": "1960", "endLine": 531, "endColumn": 44}, {"ruleId": "1957", "severity": 1, "message": "2069", "line": 534, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 534, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2070", "line": 534, "column": 20, "nodeType": "1959", "messageId": "1960", "endLine": 534, "endColumn": 32}, {"ruleId": "1968", "severity": 1, "message": "2071", "line": 574, "column": 5, "nodeType": "1970", "endLine": 574, "endColumn": 27, "suggestions": "2072"}, {"ruleId": "1957", "severity": 1, "message": "2073", "line": 584, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 584, "endColumn": 6}, {"ruleId": "1957", "severity": 1, "message": "2074", "line": 585, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 585, "endColumn": 7}, {"ruleId": "1957", "severity": 1, "message": "2075", "line": 586, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 586, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2076", "line": 588, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 588, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2077", "line": 589, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 589, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2078", "line": 594, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 594, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2079", "line": 595, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 595, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2080", "line": 630, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 630, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2081", "line": 631, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 631, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2082", "line": 632, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 632, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2083", "line": 640, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 640, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "2084", "line": 642, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 642, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2085", "line": 643, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 643, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2086", "line": 644, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 644, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2087", "line": 645, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 645, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2088", "line": 650, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 650, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "2089", "line": 652, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 652, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2090", "line": 654, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 654, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2091", "line": 664, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 664, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2092", "line": 665, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 665, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2093", "line": 668, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 668, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2094", "line": 672, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 672, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2095", "line": 674, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 674, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2096", "line": 675, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 675, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2097", "line": 677, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 677, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "2098", "line": 684, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 684, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2099", "line": 685, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 685, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "2100", "line": 690, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 690, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2101", "line": 691, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 691, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2102", "line": 692, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 692, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2103", "line": 702, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 702, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2104", "line": 705, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 705, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2105", "line": 706, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 706, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2106", "line": 710, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 710, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2107", "line": 712, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 712, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2108", "line": 714, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 714, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2109", "line": 715, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 715, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2110", "line": 720, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 720, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "2111", "line": 721, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 721, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2112", "line": 739, "column": 18, "nodeType": "1959", "messageId": "1960", "endLine": 739, "endColumn": 37}, {"ruleId": "1957", "severity": 1, "message": "2113", "line": 740, "column": 18, "nodeType": "1959", "messageId": "1960", "endLine": 740, "endColumn": 37}, {"ruleId": "1957", "severity": 1, "message": "2114", "line": 744, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 744, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2115", "line": 756, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 756, "endColumn": 35}, {"ruleId": "1957", "severity": 1, "message": "2116", "line": 789, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 789, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2117", "line": 800, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 800, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2118", "line": 805, "column": 25, "nodeType": "1959", "messageId": "1960", "endLine": 805, "endColumn": 42}, {"ruleId": "2119", "severity": 1, "message": "2120", "line": 810, "column": 22, "nodeType": "2121", "messageId": "2122", "endLine": 810, "endColumn": 24}, {"ruleId": "1968", "severity": 1, "message": "2123", "line": 900, "column": 5, "nodeType": "1970", "endLine": 900, "endColumn": 56, "suggestions": "2124"}, {"ruleId": "1968", "severity": 1, "message": "2125", "line": 918, "column": 5, "nodeType": "1970", "endLine": 918, "endColumn": 18, "suggestions": "2126"}, {"ruleId": "1957", "severity": 1, "message": "2127", "line": 920, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 920, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2128", "line": 921, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 921, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2129", "line": 942, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 942, "endColumn": 24}, {"ruleId": "1968", "severity": 1, "message": "2130", "line": 1003, "column": 5, "nodeType": "1970", "endLine": 1011, "endColumn": 3, "suggestions": "2131"}, {"ruleId": "1968", "severity": 1, "message": "2132", "line": 1039, "column": 5, "nodeType": "1970", "endLine": 1062, "endColumn": 3, "suggestions": "2133"}, {"ruleId": "1968", "severity": 1, "message": "2134", "line": 1074, "column": 5, "nodeType": "1970", "endLine": 1074, "endColumn": 19, "suggestions": "2135"}, {"ruleId": "1968", "severity": 1, "message": "2136", "line": 1081, "column": 7, "nodeType": "1970", "endLine": 1081, "endColumn": 42, "suggestions": "2137"}, {"ruleId": "1968", "severity": 1, "message": "2138", "line": 1192, "column": 5, "nodeType": "1970", "endLine": 1192, "endColumn": 39, "suggestions": "2139"}, {"ruleId": "1957", "severity": 1, "message": "2140", "line": 1311, "column": 16, "nodeType": "1959", "messageId": "1960", "endLine": 1311, "endColumn": 24}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 1405, "column": 25, "nodeType": "2121", "messageId": "2122", "endLine": 1405, "endColumn": 27}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 1412, "column": 25, "nodeType": "2121", "messageId": "2122", "endLine": 1412, "endColumn": 27}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 1412, "column": 50, "nodeType": "2121", "messageId": "2122", "endLine": 1412, "endColumn": 52}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 1415, "column": 26, "nodeType": "2121", "messageId": "2122", "endLine": 1415, "endColumn": 28}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 1415, "column": 58, "nodeType": "2121", "messageId": "2122", "endLine": 1415, "endColumn": 60}, {"ruleId": "1957", "severity": 1, "message": "2142", "line": 1546, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 1546, "endColumn": 33}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 1626, "column": 19, "nodeType": "2121", "messageId": "2122", "endLine": 1626, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2143", "line": 1773, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 1773, "endColumn": 30}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 2038, "column": 19, "nodeType": "2121", "messageId": "2122", "endLine": 2038, "endColumn": 21}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 2210, "column": 25, "nodeType": "2121", "messageId": "2122", "endLine": 2210, "endColumn": 27}, {"ruleId": "1968", "severity": 1, "message": "2144", "line": 2249, "column": 5, "nodeType": "1970", "endLine": 2249, "endColumn": 69, "suggestions": "2145"}, {"ruleId": "1957", "severity": 1, "message": "2146", "line": 2306, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 2306, "endColumn": 36}, {"ruleId": "1957", "severity": 1, "message": "2147", "line": 2313, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 2313, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2148", "line": 2316, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 2316, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2149", "line": 2316, "column": 29, "nodeType": "1959", "messageId": "1960", "endLine": 2316, "endColumn": 48}, {"ruleId": "1957", "severity": 1, "message": "2150", "line": 2709, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 2709, "endColumn": 27}, {"ruleId": "1968", "severity": 1, "message": "2151", "line": 2744, "column": 5, "nodeType": "1970", "endLine": 2744, "endColumn": 38, "suggestions": "2152"}, {"ruleId": "1957", "severity": 1, "message": "2153", "line": 2761, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 2761, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2154", "line": 2795, "column": 6, "nodeType": "1959", "messageId": "1960", "endLine": 2795, "endColumn": 18}, {"ruleId": "1968", "severity": 1, "message": "2155", "line": 3195, "column": 4, "nodeType": "1970", "endLine": 3195, "endColumn": 18, "suggestions": "2156"}, {"ruleId": "1957", "severity": 1, "message": "2157", "line": 3542, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 3542, "endColumn": 33}, {"ruleId": "1968", "severity": 1, "message": "2158", "line": 3629, "column": 16, "nodeType": "1959", "endLine": 3629, "endColumn": 31}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 3630, "column": 56, "nodeType": "2121", "messageId": "2122", "endLine": 3630, "endColumn": 58}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 3634, "column": 49, "nodeType": "2121", "messageId": "2122", "endLine": 3634, "endColumn": 51}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 3638, "column": 50, "nodeType": "2121", "messageId": "2122", "endLine": 3638, "endColumn": 52}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 3644, "column": 51, "nodeType": "2121", "messageId": "2122", "endLine": 3644, "endColumn": 53}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 3651, "column": 51, "nodeType": "2121", "messageId": "2122", "endLine": 3651, "endColumn": 53}, {"ruleId": "1957", "severity": 1, "message": "2159", "line": 3877, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 3877, "endColumn": 23}, {"ruleId": "2119", "severity": 1, "message": "2120", "line": 3885, "column": 30, "nodeType": "2121", "messageId": "2122", "endLine": 3885, "endColumn": 32}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 3898, "column": 39, "nodeType": "2121", "messageId": "2122", "endLine": 3898, "endColumn": 41}, {"ruleId": "1968", "severity": 1, "message": "2160", "line": 3912, "column": 5, "nodeType": "1970", "endLine": 3912, "endColumn": 33, "suggestions": "2161"}, {"ruleId": "1957", "severity": 1, "message": "2162", "line": 3916, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 3916, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2163", "line": 3916, "column": 30, "nodeType": "1959", "messageId": "1960", "endLine": 3916, "endColumn": 52}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 4016, "column": 55, "nodeType": "2121", "messageId": "2122", "endLine": 4016, "endColumn": 57}, {"ruleId": "1957", "severity": 1, "message": "2164", "line": 4035, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 4035, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2165", "line": 4037, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 4037, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2166", "line": 4041, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 4041, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2167", "line": 4060, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 4060, "endColumn": 26}, {"ruleId": "2119", "severity": 1, "message": "2120", "line": 4094, "column": 66, "nodeType": "2121", "messageId": "2122", "endLine": 4094, "endColumn": 68}, {"ruleId": "1968", "severity": 1, "message": "2168", "line": 4101, "column": 5, "nodeType": "1970", "endLine": 4108, "endColumn": 3, "suggestions": "2169"}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 4342, "column": 17, "nodeType": "2121", "messageId": "2122", "endLine": 4342, "endColumn": 19}, {"ruleId": "2119", "severity": 1, "message": "2120", "line": 4598, "column": 21, "nodeType": "2121", "messageId": "2122", "endLine": 4598, "endColumn": 23}, {"ruleId": "2119", "severity": 1, "message": "2120", "line": 4606, "column": 21, "nodeType": "2121", "messageId": "2122", "endLine": 4606, "endColumn": 23}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 4619, "column": 15, "nodeType": "2121", "messageId": "2122", "endLine": 4619, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2170", "line": 4921, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 4921, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2171", "line": 4932, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 4932, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2172", "line": 4933, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 4933, "endColumn": 20}, {"ruleId": "1968", "severity": 1, "message": "2173", "line": 4939, "column": 5, "nodeType": "1970", "endLine": 4939, "endColumn": 62, "suggestions": "2174"}, {"ruleId": "1968", "severity": 1, "message": "2175", "line": 4939, "column": 6, "nodeType": "2176", "endLine": 4939, "endColumn": 48}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 4962, "column": 25, "nodeType": "2121", "messageId": "2122", "endLine": 4962, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2177", "line": 4966, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 4966, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2178", "line": 4989, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 4989, "endColumn": 23}, {"ruleId": "2119", "severity": 1, "message": "2120", "line": 5064, "column": 25, "nodeType": "2121", "messageId": "2122", "endLine": 5064, "endColumn": 27}, {"ruleId": "1968", "severity": 1, "message": "2179", "line": 5097, "column": 5, "nodeType": "1970", "endLine": 5097, "endColumn": 22, "suggestions": "2180"}, {"ruleId": "1957", "severity": 1, "message": "2181", "line": 5099, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 5099, "endColumn": 18}, {"ruleId": "2119", "severity": 1, "message": "2120", "line": 5101, "column": 40, "nodeType": "2121", "messageId": "2122", "endLine": 5101, "endColumn": 42}, {"ruleId": "2119", "severity": 1, "message": "2120", "line": 5166, "column": 69, "nodeType": "2121", "messageId": "2122", "endLine": 5166, "endColumn": 71}, {"ruleId": "1957", "severity": 1, "message": "2182", "line": 5216, "column": 12, "nodeType": "1959", "messageId": "1960", "endLine": 5216, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2183", "line": 5217, "column": 12, "nodeType": "1959", "messageId": "1960", "endLine": 5217, "endColumn": 22}, {"ruleId": "1968", "severity": 1, "message": "2184", "line": 5247, "column": 5, "nodeType": "1970", "endLine": 5247, "endColumn": 38, "suggestions": "2185"}, {"ruleId": "2119", "severity": 1, "message": "2120", "line": 5250, "column": 40, "nodeType": "2121", "messageId": "2122", "endLine": 5250, "endColumn": 42}, {"ruleId": "1957", "severity": 1, "message": "2182", "line": 5256, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 5256, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2183", "line": 5257, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 5257, "endColumn": 20}, {"ruleId": "1968", "severity": 1, "message": "2186", "line": 5263, "column": 5, "nodeType": "1970", "endLine": 5263, "endColumn": 106, "suggestions": "2187"}, {"ruleId": "1968", "severity": 1, "message": "2188", "line": 5412, "column": 5, "nodeType": "1970", "endLine": 5412, "endColumn": 17, "suggestions": "2189"}, {"ruleId": "1968", "severity": 1, "message": "2190", "line": 5428, "column": 5, "nodeType": "1970", "endLine": 5428, "endColumn": 78, "suggestions": "2191"}, {"ruleId": "1957", "severity": 1, "message": "2192", "line": 5431, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 5431, "endColumn": 29}, {"ruleId": "1968", "severity": 1, "message": "2193", "line": 5442, "column": 8, "nodeType": "1970", "endLine": 5442, "endColumn": 15, "suggestions": "2194"}, {"ruleId": "1957", "severity": 1, "message": "2195", "line": 5452, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 5452, "endColumn": 32}, {"ruleId": "2196", "severity": 1, "message": "2197", "line": 6101, "column": 80, "nodeType": "2198", "messageId": "2199", "endLine": 6101, "endColumn": 81, "suggestions": "2200"}, {"ruleId": "1957", "severity": 1, "message": "2201", "line": 6316, "column": 25, "nodeType": "1959", "messageId": "1960", "endLine": 6316, "endColumn": 42}, {"ruleId": "1957", "severity": 1, "message": "2202", "line": 6721, "column": 22, "nodeType": "1959", "messageId": "1960", "endLine": 6721, "endColumn": 31}, {"ruleId": "1957", "severity": 1, "message": "2203", "line": 3, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 31}, {"ruleId": "1957", "severity": 1, "message": "2204", "line": 4, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 32}, {"ruleId": "1957", "severity": 1, "message": "2205", "line": 3, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2206", "line": 8, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 8, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2207", "line": 9, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2208", "line": 13, "column": 24, "nodeType": "1959", "messageId": "1960", "endLine": 13, "endColumn": 46}, {"ruleId": "2209", "severity": 1, "message": "2210", "line": 2498, "column": 5, "nodeType": "2211", "messageId": "2122", "endLine": 2498, "endColumn": 17}, {"ruleId": "2209", "severity": 1, "message": "2212", "line": 2499, "column": 5, "nodeType": "2211", "messageId": "2122", "endLine": 2499, "endColumn": 20}, {"ruleId": "2209", "severity": 1, "message": "2213", "line": 2830, "column": 5, "nodeType": "2211", "messageId": "2122", "endLine": 2830, "endColumn": 24}, {"ruleId": "2214", "severity": 1, "message": "2215", "line": 2836, "column": 31, "nodeType": "2216", "messageId": "2217", "endLine": 2836, "endColumn": 51}, {"ruleId": "2209", "severity": 1, "message": "2218", "line": 3007, "column": 5, "nodeType": "2211", "messageId": "2122", "endLine": 3007, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2219", "line": 3720, "column": 16, "nodeType": "1959", "messageId": "1960", "endLine": 3720, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2219", "line": 3919, "column": 16, "nodeType": "1959", "messageId": "1960", "endLine": 3919, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2220", "line": 5215, "column": 14, "nodeType": "1959", "messageId": "1960", "endLine": 5215, "endColumn": 35}, {"ruleId": "2209", "severity": 1, "message": "2221", "line": 5391, "column": 5, "nodeType": "2211", "messageId": "2122", "endLine": 5391, "endColumn": 30}, {"ruleId": "1957", "severity": 1, "message": "2222", "line": 5455, "column": 14, "nodeType": "1959", "messageId": "1960", "endLine": 5455, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2223", "line": 6677, "column": 13, "nodeType": "1959", "messageId": "1960", "endLine": 6677, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2223", "line": 6703, "column": 13, "nodeType": "1959", "messageId": "1960", "endLine": 6703, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2223", "line": 6709, "column": 13, "nodeType": "1959", "messageId": "1960", "endLine": 6709, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2223", "line": 6724, "column": 13, "nodeType": "1959", "messageId": "1960", "endLine": 6724, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2222", "line": 7501, "column": 13, "nodeType": "1959", "messageId": "1960", "endLine": 7501, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2222", "line": 7745, "column": 13, "nodeType": "1959", "messageId": "1960", "endLine": 7745, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2222", "line": 7916, "column": 13, "nodeType": "1959", "messageId": "1960", "endLine": 7916, "endColumn": 16}, {"ruleId": "2119", "severity": 1, "message": "2120", "line": 8581, "column": 66, "nodeType": "2121", "messageId": "2122", "endLine": 8581, "endColumn": 68}, {"ruleId": "1957", "severity": 1, "message": "2224", "line": 70, "column": 23, "nodeType": "1959", "messageId": "1960", "endLine": 70, "endColumn": 29}, {"ruleId": "1957", "severity": 1, "message": "2225", "line": 1, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2226", "line": 2, "column": 44, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 56}, {"ruleId": "1957", "severity": 1, "message": "2227", "line": 18, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 18, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "2228", "line": 19, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 19, "endColumn": 7}, {"ruleId": "1957", "severity": 1, "message": "2229", "line": 20, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 20, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2230", "line": 21, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 21, "endColumn": 7}, {"ruleId": "1957", "severity": 1, "message": "2231", "line": 24, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 24, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2232", "line": 25, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 25, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2233", "line": 26, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 26, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2234", "line": 31, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 31, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2235", "line": 38, "column": 49, "nodeType": "1959", "messageId": "1960", "endLine": 38, "endColumn": 55}, {"ruleId": "1957", "severity": 1, "message": "2236", "line": 38, "column": 63, "nodeType": "1959", "messageId": "1960", "endLine": 38, "endColumn": 70}, {"ruleId": "1957", "severity": 1, "message": "2237", "line": 46, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 46, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2238", "line": 48, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 48, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2239", "line": 92, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 92, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2240", "line": 93, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 93, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2241", "line": 99, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 99, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2242", "line": 100, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 100, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2243", "line": 104, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 104, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "2244", "line": 108, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 108, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2245", "line": 112, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 112, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2246", "line": 113, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 113, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2247", "line": 114, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 114, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2248", "line": 115, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 115, "endColumn": 33}, {"ruleId": "1957", "severity": 1, "message": "2249", "line": 116, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 116, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2250", "line": 119, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 119, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2251", "line": 120, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 120, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2252", "line": 165, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 165, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2253", "line": 175, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 175, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2254", "line": 183, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 183, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "2255", "line": 184, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 184, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2256", "line": 185, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 185, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2257", "line": 186, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 186, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2258", "line": 187, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 187, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "2259", "line": 208, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 208, "endColumn": 30}, {"ruleId": "1957", "severity": 1, "message": "2260", "line": 212, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 212, "endColumn": 29}, {"ruleId": "1957", "severity": 1, "message": "2261", "line": 459, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 459, "endColumn": 26}, {"ruleId": "1968", "severity": 1, "message": "2262", "line": 551, "column": 5, "nodeType": "1970", "endLine": 551, "endColumn": 60, "suggestions": "2263"}, {"ruleId": "1957", "severity": 1, "message": "2264", "line": 565, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 565, "endColumn": 22}, {"ruleId": "1968", "severity": 1, "message": "2265", "line": 585, "column": 5, "nodeType": "1970", "endLine": 585, "endColumn": 60, "suggestions": "2266"}, {"ruleId": "1968", "severity": 1, "message": "2267", "line": 602, "column": 4, "nodeType": "1970", "endLine": 602, "endColumn": 6, "suggestions": "2268"}, {"ruleId": "1957", "severity": 1, "message": "2269", "line": 1, "column": 17, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2270", "line": 2, "column": 29, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 39}, {"ruleId": "1957", "severity": 1, "message": "2007", "line": 3, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2008", "line": 4, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2005", "line": 5, "column": 26, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 44}, {"ruleId": "1957", "severity": 1, "message": "2006", "line": 5, "column": 46, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 55}, {"ruleId": "1957", "severity": 1, "message": "2271", "line": 6, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 6, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2272", "line": 6, "column": 24, "nodeType": "1959", "messageId": "1960", "endLine": 6, "endColumn": 31}, {"ruleId": "1957", "severity": 1, "message": "2017", "line": 11, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 11, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2273", "line": 17, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 17, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2274", "line": 21, "column": 19, "nodeType": "1959", "messageId": "1960", "endLine": 21, "endColumn": 35}, {"ruleId": "1957", "severity": 1, "message": "2275", "line": 24, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 24, "endColumn": 42}, {"ruleId": "1957", "severity": 1, "message": "2276", "line": 25, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 25, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2277", "line": 26, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 26, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2051", "line": 35, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 35, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2052", "line": 35, "column": 28, "nodeType": "1959", "messageId": "1960", "endLine": 35, "endColumn": 42}, {"ruleId": "1957", "severity": 1, "message": "2278", "line": 2, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "1982", "line": 6, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 6, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2279", "line": 9, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 4}, {"ruleId": "1957", "severity": 1, "message": "2280", "line": 13, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 13, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2281", "line": 26, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 26, "endColumn": 38}, {"ruleId": "1957", "severity": 1, "message": "2282", "line": 54, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 54, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2283", "line": 55, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 55, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2088", "line": 56, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 56, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "2284", "line": 57, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 57, "endColumn": 8}, {"ruleId": "1957", "severity": 1, "message": "2285", "line": 58, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 58, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2106", "line": 59, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 59, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2286", "line": 60, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 60, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2287", "line": 61, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 61, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2107", "line": 62, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 62, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2288", "line": 63, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 63, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2289", "line": 64, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 64, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2290", "line": 65, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 65, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2291", "line": 66, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 66, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2292", "line": 67, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 67, "endColumn": 29}, {"ruleId": "1957", "severity": 1, "message": "2246", "line": 68, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 68, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2249", "line": 69, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 69, "endColumn": 23}, {"ruleId": "1968", "severity": 1, "message": "2293", "line": 86, "column": 5, "nodeType": "1970", "endLine": 86, "endColumn": 7, "suggestions": "2294"}, {"ruleId": "1968", "severity": 1, "message": "2295", "line": 104, "column": 5, "nodeType": "1970", "endLine": 104, "endColumn": 28, "suggestions": "2296"}, {"ruleId": "1968", "severity": 1, "message": "2297", "line": 115, "column": 5, "nodeType": "1970", "endLine": 115, "endColumn": 48, "suggestions": "2298"}, {"ruleId": "1957", "severity": 1, "message": "2299", "line": 194, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 194, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2300", "line": 1, "column": 27, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 41}, {"ruleId": "1957", "severity": 1, "message": "2301", "line": 3, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2015", "line": 3, "column": 26, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 37}, {"ruleId": "1957", "severity": 1, "message": "2302", "line": 5, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2303", "line": 457, "column": 7, "nodeType": "1959", "messageId": "1960", "endLine": 457, "endColumn": 29}, {"ruleId": "1957", "severity": 1, "message": "2304", "line": 530, "column": 7, "nodeType": "1959", "messageId": "1960", "endLine": 530, "endColumn": 24}, {"ruleId": "2196", "severity": 1, "message": "2197", "line": 682, "column": 41, "nodeType": "2198", "messageId": "2199", "endLine": 682, "endColumn": 42, "suggestions": "2305"}, {"ruleId": "2196", "severity": 1, "message": "2306", "line": 682, "column": 45, "nodeType": "2198", "messageId": "2199", "endLine": 682, "endColumn": 46, "suggestions": "2307"}, {"ruleId": "2196", "severity": 1, "message": "2197", "line": 682, "column": 56, "nodeType": "2198", "messageId": "2199", "endLine": 682, "endColumn": 57, "suggestions": "2308"}, {"ruleId": "2196", "severity": 1, "message": "2306", "line": 682, "column": 60, "nodeType": "2198", "messageId": "2199", "endLine": 682, "endColumn": 61, "suggestions": "2309"}, {"ruleId": "2196", "severity": 1, "message": "2197", "line": 682, "column": 89, "nodeType": "2198", "messageId": "2199", "endLine": 682, "endColumn": 90, "suggestions": "2310"}, {"ruleId": "2196", "severity": 1, "message": "2306", "line": 682, "column": 93, "nodeType": "2198", "messageId": "2199", "endLine": 682, "endColumn": 94, "suggestions": "2311"}, {"ruleId": "2196", "severity": 1, "message": "2197", "line": 682, "column": 104, "nodeType": "2198", "messageId": "2199", "endLine": 682, "endColumn": 105, "suggestions": "2312"}, {"ruleId": "2196", "severity": 1, "message": "2306", "line": 682, "column": 108, "nodeType": "2198", "messageId": "2199", "endLine": 682, "endColumn": 109, "suggestions": "2313"}, {"ruleId": "1957", "severity": 1, "message": "2314", "line": 1316, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 1316, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2315", "line": 1321, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 1321, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2316", "line": 1, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2317", "line": 3, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2301", "line": 4, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2318", "line": 3, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2319", "line": 4, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2320", "line": 31, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 31, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2321", "line": 32, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 32, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "1973", "line": 2, "column": 28, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 35}, {"ruleId": "1957", "severity": 1, "message": "2322", "line": 4, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2323", "line": 8, "column": 33, "nodeType": "1959", "messageId": "1960", "endLine": 8, "endColumn": 42}, {"ruleId": "1957", "severity": 1, "message": "2324", "line": 8, "column": 44, "nodeType": "1959", "messageId": "1960", "endLine": 8, "endColumn": 59}, {"ruleId": "1957", "severity": 1, "message": "2325", "line": 10, "column": 34, "nodeType": "1959", "messageId": "1960", "endLine": 10, "endColumn": 57}, {"ruleId": "1957", "severity": 1, "message": "2326", "line": 10, "column": 59, "nodeType": "1959", "messageId": "1960", "endLine": 10, "endColumn": 79}, {"ruleId": "1957", "severity": 1, "message": "2327", "line": 59, "column": 16, "nodeType": "1959", "messageId": "1960", "endLine": 59, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2328", "line": 124, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 124, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2269", "line": 1, "column": 17, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "1977", "line": 2, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2273", "line": 4, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "1999", "line": 9, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2329", "line": 11, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 11, "endColumn": 9}, {"ruleId": "1957", "severity": 1, "message": "2278", "line": 12, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 12, "endColumn": 8}, {"ruleId": "1957", "severity": 1, "message": "2330", "line": 14, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 14, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2233", "line": 16, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 16, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2331", "line": 18, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 18, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2332", "line": 19, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 19, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2333", "line": 20, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 20, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "2334", "line": 21, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 21, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2335", "line": 22, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 22, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2336", "line": 23, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 23, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "2337", "line": 24, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 24, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "2338", "line": 25, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 25, "endColumn": 9}, {"ruleId": "1957", "severity": 1, "message": "1973", "line": 1, "column": 28, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 35}, {"ruleId": "1957", "severity": 1, "message": "2339", "line": 8, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 8, "endColumn": 33}, {"ruleId": "1957", "severity": 1, "message": "2323", "line": 9, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2340", "line": 80, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 80, "endColumn": 58}, {"ruleId": "1968", "severity": 1, "message": "2341", "line": 86, "column": 8, "nodeType": "2342", "endLine": 90, "endColumn": 12}, {"ruleId": "1968", "severity": 1, "message": "2343", "line": 86, "column": 8, "nodeType": "2342", "endLine": 90, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "2344", "line": 92, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 92, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2345", "line": 92, "column": 25, "nodeType": "1959", "messageId": "1960", "endLine": 92, "endColumn": 42}, {"ruleId": "2346", "severity": 1, "message": "2347", "line": 113, "column": 113, "nodeType": "2348", "messageId": "2349", "endLine": 113, "endColumn": 397}, {"ruleId": "1968", "severity": 1, "message": "2350", "line": 154, "column": 5, "nodeType": "1970", "endLine": 154, "endColumn": 38, "suggestions": "2351"}, {"ruleId": "1968", "severity": 1, "message": "2175", "line": 154, "column": 6, "nodeType": "2121", "endLine": 154, "endColumn": 37}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 154, "column": 33, "nodeType": "2121", "messageId": "2122", "endLine": 154, "endColumn": 35}, {"ruleId": "1957", "severity": 1, "message": "2327", "line": 156, "column": 16, "nodeType": "1959", "messageId": "1960", "endLine": 156, "endColumn": 24}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 174, "column": 56, "nodeType": "2121", "messageId": "2122", "endLine": 174, "endColumn": 58}, {"ruleId": "1957", "severity": 1, "message": "2352", "line": 181, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 181, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2353", "line": 182, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 182, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2354", "line": 305, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 305, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2327", "line": 771, "column": 16, "nodeType": "1959", "messageId": "1960", "endLine": 771, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2355", "line": 806, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 806, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2356", "line": 806, "column": 19, "nodeType": "1959", "messageId": "1960", "endLine": 806, "endColumn": 30}, {"ruleId": "1957", "severity": 1, "message": "2357", "line": 807, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 807, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2358", "line": 807, "column": 22, "nodeType": "1959", "messageId": "1960", "endLine": 807, "endColumn": 36}, {"ruleId": "1957", "severity": 1, "message": "2148", "line": 808, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 808, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2149", "line": 808, "column": 29, "nodeType": "1959", "messageId": "1960", "endLine": 808, "endColumn": 48}, {"ruleId": "1957", "severity": 1, "message": "2359", "line": 809, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 809, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2360", "line": 809, "column": 27, "nodeType": "1959", "messageId": "1960", "endLine": 809, "endColumn": 46}, {"ruleId": "1957", "severity": 1, "message": "2065", "line": 810, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 810, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2361", "line": 5, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2362", "line": 13, "column": 26, "nodeType": "1959", "messageId": "1960", "endLine": 13, "endColumn": 47}, {"ruleId": "1957", "severity": 1, "message": "2363", "line": 15, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 15, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2364", "line": 79, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 79, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2365", "line": 82, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 82, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2366", "line": 83, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 83, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2367", "line": 84, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 84, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2083", "line": 88, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 88, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "2368", "line": 89, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 89, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2369", "line": 91, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 91, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2085", "line": 92, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 92, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2086", "line": 93, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 93, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2084", "line": 94, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 94, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2250", "line": 104, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 104, "endColumn": 19}, {"ruleId": "1968", "severity": 1, "message": "2370", "line": 209, "column": 7, "nodeType": "1970", "endLine": 209, "endColumn": 9, "suggestions": "2371"}, {"ruleId": "1968", "severity": 1, "message": "2372", "line": 244, "column": 7, "nodeType": "1970", "endLine": 244, "endColumn": 29, "suggestions": "2373"}, {"ruleId": "1968", "severity": 1, "message": "2374", "line": 249, "column": 7, "nodeType": "1970", "endLine": 249, "endColumn": 18, "suggestions": "2375"}, {"ruleId": "1968", "severity": 1, "message": "2376", "line": 292, "column": 7, "nodeType": "1970", "endLine": 292, "endColumn": 72, "suggestions": "2377"}, {"ruleId": "1957", "severity": 1, "message": "2378", "line": 331, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 331, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2379", "line": 334, "column": 26, "nodeType": "1959", "messageId": "1960", "endLine": 334, "endColumn": 44}, {"ruleId": "1957", "severity": 1, "message": "2380", "line": 465, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 465, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2002", "line": 3, "column": 65, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 69}, {"ruleId": "1957", "severity": 1, "message": "2381", "line": 6, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 6, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2382", "line": 7, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 7, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2336", "line": 16, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 16, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2383", "line": 20, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 20, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2356", "line": 84, "column": 19, "nodeType": "1959", "messageId": "1960", "endLine": 84, "endColumn": 30}, {"ruleId": "1957", "severity": 1, "message": "2384", "line": 85, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 85, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2385", "line": 85, "column": 26, "nodeType": "1959", "messageId": "1960", "endLine": 85, "endColumn": 44}, {"ruleId": "1957", "severity": 1, "message": "2386", "line": 86, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 86, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2387", "line": 86, "column": 26, "nodeType": "1959", "messageId": "1960", "endLine": 86, "endColumn": 44}, {"ruleId": "1957", "severity": 1, "message": "2388", "line": 90, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 90, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2389", "line": 90, "column": 45, "nodeType": "1959", "messageId": "1960", "endLine": 90, "endColumn": 62}, {"ruleId": "1957", "severity": 1, "message": "2390", "line": 93, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 93, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "2391", "line": 94, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 94, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2058", "line": 95, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 95, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "2059", "line": 96, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 96, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2060", "line": 97, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 97, "endColumn": 9}, {"ruleId": "1957", "severity": 1, "message": "2061", "line": 98, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 98, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "2062", "line": 99, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 99, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2063", "line": 100, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 100, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2392", "line": 101, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 101, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2393", "line": 102, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 102, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2394", "line": 103, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 103, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2075", "line": 104, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 104, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2378", "line": 105, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 105, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2395", "line": 107, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 107, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2396", "line": 108, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 108, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2397", "line": 115, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 115, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2398", "line": 116, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 116, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2399", "line": 118, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 118, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2400", "line": 119, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 119, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2401", "line": 120, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 120, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2402", "line": 121, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 121, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2403", "line": 122, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 122, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2404", "line": 123, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 123, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2405", "line": 132, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 132, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2406", "line": 137, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 137, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2240", "line": 139, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 139, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2247", "line": 140, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 140, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2407", "line": 141, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 141, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2408", "line": 144, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 144, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "2245", "line": 145, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 145, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2409", "line": 146, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 146, "endColumn": 20}, {"ruleId": "1968", "severity": 1, "message": "2410", "line": 170, "column": 5, "nodeType": "1970", "endLine": 170, "endColumn": 45, "suggestions": "2411"}, {"ruleId": "1957", "severity": 1, "message": "2412", "line": 221, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 221, "endColumn": 29}, {"ruleId": "2119", "severity": 1, "message": "2120", "line": 241, "column": 24, "nodeType": "2121", "messageId": "2122", "endLine": 241, "endColumn": 26}, {"ruleId": "1968", "severity": 1, "message": "2413", "line": 315, "column": 7, "nodeType": "1970", "endLine": 315, "endColumn": 42, "suggestions": "2414"}, {"ruleId": "1957", "severity": 1, "message": "2415", "line": 339, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 339, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2416", "line": 340, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 340, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2417", "line": 488, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 488, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2418", "line": 491, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 491, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2419", "line": 500, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 500, "endColumn": 31}, {"ruleId": "2119", "severity": 1, "message": "2120", "line": 811, "column": 26, "nodeType": "2121", "messageId": "2122", "endLine": 811, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2420", "line": 1032, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 1032, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2421", "line": 1036, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 1036, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2422", "line": 1040, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 1040, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2423", "line": 1044, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 1044, "endColumn": 32}, {"ruleId": "1957", "severity": 1, "message": "2424", "line": 1048, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 1048, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2425", "line": 1052, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 1052, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2426", "line": 1056, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 1056, "endColumn": 31}, {"ruleId": "1957", "severity": 1, "message": "2427", "line": 1060, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 1060, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2428", "line": 4, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2429", "line": 6, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 6, "endColumn": 35}, {"ruleId": "1957", "severity": 1, "message": "2430", "line": 7, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 7, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2431", "line": 7, "column": 17, "nodeType": "1959", "messageId": "1960", "endLine": 7, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2432", "line": 8, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 8, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2433", "line": 9, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2085", "line": 13, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 13, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2086", "line": 14, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 14, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2084", "line": 15, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 15, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2434", "line": 17, "column": 21, "nodeType": "1959", "messageId": "1960", "endLine": 17, "endColumn": 34}, {"ruleId": "1957", "severity": 1, "message": "2435", "line": 18, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 18, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2436", "line": 18, "column": 33, "nodeType": "1959", "messageId": "1960", "endLine": 18, "endColumn": 44}, {"ruleId": "1957", "severity": 1, "message": "2437", "line": 19, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 19, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2438", "line": 96, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 96, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2439", "line": 97, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 97, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2440", "line": 100, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 100, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2441", "line": 101, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 101, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2442", "line": 109, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 109, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2443", "line": 129, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 129, "endColumn": 16}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 176, "column": 45, "nodeType": "2121", "messageId": "2122", "endLine": 176, "endColumn": 47}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 176, "column": 104, "nodeType": "2121", "messageId": "2122", "endLine": 176, "endColumn": 106}, {"ruleId": "1957", "severity": 1, "message": "2234", "line": 2, "column": 60, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 73}, {"ruleId": "1957", "severity": 1, "message": "2428", "line": 3, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2444", "line": 8, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 8, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2445", "line": 9, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2446", "line": 133, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 133, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2447", "line": 134, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 134, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2448", "line": 135, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 135, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2449", "line": 135, "column": 30, "nodeType": "1959", "messageId": "1960", "endLine": 135, "endColumn": 52}, {"ruleId": "1957", "severity": 1, "message": "2250", "line": 137, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 137, "endColumn": 19}, {"ruleId": "1968", "severity": 1, "message": "2450", "line": 163, "column": 8, "nodeType": "1970", "endLine": 163, "endColumn": 10, "suggestions": "2451"}, {"ruleId": "1957", "severity": 1, "message": "2452", "line": 299, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 299, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2453", "line": 342, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 342, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2454", "line": 343, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 343, "endColumn": 35}, {"ruleId": "1957", "severity": 1, "message": "2455", "line": 344, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 344, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2456", "line": 346, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 346, "endColumn": 20}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 465, "column": 22, "nodeType": "2121", "messageId": "2122", "endLine": 465, "endColumn": 24}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 465, "column": 53, "nodeType": "2121", "messageId": "2122", "endLine": 465, "endColumn": 55}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 465, "column": 89, "nodeType": "2121", "messageId": "2122", "endLine": 465, "endColumn": 91}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 465, "column": 125, "nodeType": "2121", "messageId": "2122", "endLine": 465, "endColumn": 127}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 467, "column": 29, "nodeType": "2121", "messageId": "2122", "endLine": 467, "endColumn": 31}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 467, "column": 56, "nodeType": "2121", "messageId": "2122", "endLine": 467, "endColumn": 58}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 467, "column": 88, "nodeType": "2121", "messageId": "2122", "endLine": 467, "endColumn": 90}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 467, "column": 120, "nodeType": "2121", "messageId": "2122", "endLine": 467, "endColumn": 122}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 469, "column": 29, "nodeType": "2121", "messageId": "2122", "endLine": 469, "endColumn": 31}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 469, "column": 64, "nodeType": "2121", "messageId": "2122", "endLine": 469, "endColumn": 66}, {"ruleId": "1957", "severity": 1, "message": "2457", "line": 111, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 111, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2404", "line": 152, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 152, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2097", "line": 153, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 153, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "2458", "line": 159, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 159, "endColumn": 23}, {"ruleId": "2459", "severity": 1, "message": "2460", "line": 226, "column": 25, "nodeType": "1959", "messageId": "2461", "endLine": 226, "endColumn": 34, "suggestions": "2462"}, {"ruleId": "1968", "severity": 1, "message": "2463", "line": 232, "column": 5, "nodeType": "1970", "endLine": 232, "endColumn": 12, "suggestions": "2464"}, {"ruleId": "1968", "severity": 1, "message": "2465", "line": 238, "column": 5, "nodeType": "1970", "endLine": 238, "endColumn": 21, "suggestions": "2466"}, {"ruleId": "1968", "severity": 1, "message": "2467", "line": 494, "column": 5, "nodeType": "1970", "endLine": 494, "endColumn": 70, "suggestions": "2468"}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 572, "column": 19, "nodeType": "2121", "messageId": "2122", "endLine": 572, "endColumn": 21}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 573, "column": 19, "nodeType": "2121", "messageId": "2122", "endLine": 573, "endColumn": 21}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 574, "column": 24, "nodeType": "2121", "messageId": "2122", "endLine": 574, "endColumn": 26}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 575, "column": 24, "nodeType": "2121", "messageId": "2122", "endLine": 575, "endColumn": 26}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 579, "column": 19, "nodeType": "2121", "messageId": "2122", "endLine": 579, "endColumn": 21}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 580, "column": 19, "nodeType": "2121", "messageId": "2122", "endLine": 580, "endColumn": 21}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 581, "column": 24, "nodeType": "2121", "messageId": "2122", "endLine": 581, "endColumn": 26}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 582, "column": 24, "nodeType": "2121", "messageId": "2122", "endLine": 582, "endColumn": 26}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 586, "column": 19, "nodeType": "2121", "messageId": "2122", "endLine": 586, "endColumn": 21}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 587, "column": 24, "nodeType": "2121", "messageId": "2122", "endLine": 587, "endColumn": 26}, {"ruleId": "1968", "severity": 1, "message": "2469", "line": 607, "column": 5, "nodeType": "1970", "endLine": 607, "endColumn": 64, "suggestions": "2470"}, {"ruleId": "1968", "severity": 1, "message": "2175", "line": 607, "column": 6, "nodeType": "2176", "endLine": 607, "endColumn": 34}, {"ruleId": "1968", "severity": 1, "message": "2471", "line": 627, "column": 5, "nodeType": "1970", "endLine": 627, "endColumn": 47, "suggestions": "2472"}, {"ruleId": "1968", "severity": 1, "message": "2471", "line": 646, "column": 5, "nodeType": "1970", "endLine": 646, "endColumn": 47, "suggestions": "2473"}, {"ruleId": "1968", "severity": 1, "message": "2474", "line": 1037, "column": 17, "nodeType": "1959", "endLine": 1037, "endColumn": 32}, {"ruleId": "2119", "severity": 1, "message": "2120", "line": 1243, "column": 43, "nodeType": "2121", "messageId": "2122", "endLine": 1243, "endColumn": 45}, {"ruleId": "2119", "severity": 1, "message": "2120", "line": 1248, "column": 78, "nodeType": "2121", "messageId": "2122", "endLine": 1248, "endColumn": 80}, {"ruleId": "1957", "severity": 1, "message": "2475", "line": 1, "column": 28, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 34}, {"ruleId": "1957", "severity": 1, "message": "2278", "line": 5, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 5}, {"ruleId": "1957", "severity": 1, "message": "2476", "line": 6, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 6, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2477", "line": 10, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 10, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2478", "line": 12, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 12, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2270", "line": 13, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 13, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "2479", "line": 17, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 17, "endColumn": 32}, {"ruleId": "1957", "severity": 1, "message": "2339", "line": 19, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 19, "endColumn": 33}, {"ruleId": "1957", "severity": 1, "message": "2480", "line": 34, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 34, "endColumn": 31}, {"ruleId": "1957", "severity": 1, "message": "2357", "line": 34, "column": 33, "nodeType": "1959", "messageId": "1960", "endLine": 34, "endColumn": 44}, {"ruleId": "2481", "severity": 1, "message": "2482", "line": 96, "column": 2, "nodeType": "2483", "messageId": "2484", "endLine": 112, "endColumn": 4}, {"ruleId": "1957", "severity": 1, "message": "2485", "line": 133, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 133, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "2486", "line": 136, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 136, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "2393", "line": 137, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 137, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2075", "line": 138, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 138, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2487", "line": 140, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 140, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2390", "line": 141, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 141, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "2488", "line": 142, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 142, "endColumn": 8}, {"ruleId": "1957", "severity": 1, "message": "2489", "line": 145, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 145, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2490", "line": 146, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 146, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2491", "line": 147, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 147, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2492", "line": 148, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 148, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2493", "line": 149, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 149, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2494", "line": 150, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 150, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2399", "line": 151, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 151, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2398", "line": 152, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 152, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2397", "line": 153, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 153, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2495", "line": 156, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 156, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2496", "line": 159, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 159, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2497", "line": 160, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 160, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2498", "line": 161, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 161, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2284", "line": 162, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 162, "endColumn": 8}, {"ruleId": "1957", "severity": 1, "message": "2244", "line": 163, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 163, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2499", "line": 166, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 166, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2500", "line": 167, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 167, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2458", "line": 169, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 169, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2501", "line": 174, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 174, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2097", "line": 175, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 175, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "2502", "line": 177, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 177, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2503", "line": 186, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 186, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2504", "line": 186, "column": 25, "nodeType": "1959", "messageId": "1960", "endLine": 186, "endColumn": 42}, {"ruleId": "1957", "severity": 1, "message": "2505", "line": 188, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 188, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2506", "line": 188, "column": 26, "nodeType": "1959", "messageId": "1960", "endLine": 188, "endColumn": 44}, {"ruleId": "2507", "severity": 1, "message": "2508", "line": 350, "column": 5, "nodeType": "2509", "messageId": "2510", "endLine": 350, "endColumn": 52}, {"ruleId": "1957", "severity": 1, "message": "2511", "line": 505, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 505, "endColumn": 31}, {"ruleId": "1957", "severity": 1, "message": "2512", "line": 575, "column": 7, "nodeType": "1959", "messageId": "1960", "endLine": 575, "endColumn": 21}, {"ruleId": "1968", "severity": 1, "message": "2513", "line": 742, "column": 5, "nodeType": "1970", "endLine": 742, "endColumn": 100, "suggestions": "2514"}, {"ruleId": "1968", "severity": 1, "message": "2515", "line": 760, "column": 5, "nodeType": "1970", "endLine": 760, "endColumn": 83, "suggestions": "2516"}, {"ruleId": "1957", "severity": 1, "message": "2517", "line": 940, "column": 7, "nodeType": "1959", "messageId": "1960", "endLine": 940, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2518", "line": 947, "column": 7, "nodeType": "1959", "messageId": "1960", "endLine": 947, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2519", "line": 1, "column": 17, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2520", "line": 2, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2521", "line": 2, "column": 16, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2205", "line": 3, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2522", "line": 11, "column": 62, "nodeType": "1959", "messageId": "1960", "endLine": 11, "endColumn": 67}, {"ruleId": "1957", "severity": 1, "message": "2291", "line": 25, "column": 7, "nodeType": "1959", "messageId": "1960", "endLine": 25, "endColumn": 29}, {"ruleId": "1957", "severity": 1, "message": "2249", "line": 28, "column": 7, "nodeType": "1959", "messageId": "1960", "endLine": 28, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2523", "line": 31, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 31, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2524", "line": 144, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 144, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2525", "line": 145, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 145, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2519", "line": 1, "column": 17, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2526", "line": 1, "column": 28, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 36}, {"ruleId": "1957", "severity": 1, "message": "2527", "line": 4, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2528", "line": 11, "column": 98, "nodeType": "1959", "messageId": "1960", "endLine": 11, "endColumn": 106}, {"ruleId": "1957", "severity": 1, "message": "2282", "line": 14, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 14, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2288", "line": 15, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 15, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2283", "line": 16, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 16, "endColumn": 32}, {"ruleId": "1957", "severity": 1, "message": "2249", "line": 17, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 17, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2292", "line": 20, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 20, "endColumn": 29}, {"ruleId": "1957", "severity": 1, "message": "2529", "line": 27, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 27, "endColumn": 29}, {"ruleId": "1957", "severity": 1, "message": "2526", "line": 1, "column": 17, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2530", "line": 2, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2205", "line": 2, "column": 19, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2270", "line": 2, "column": 27, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 37}, {"ruleId": "1957", "severity": 1, "message": "2278", "line": 2, "column": 39, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 42}, {"ruleId": "1957", "severity": 1, "message": "2477", "line": 2, "column": 44, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 58}, {"ruleId": "1957", "severity": 1, "message": "2234", "line": 2, "column": 60, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 73}, {"ruleId": "1957", "severity": 1, "message": "2335", "line": 2, "column": 74, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 84}, {"ruleId": "1957", "severity": 1, "message": "2531", "line": 3, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2532", "line": 4, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2282", "line": 98, "column": 13, "nodeType": "1959", "messageId": "1960", "endLine": 98, "endColumn": 32}, {"ruleId": "1957", "severity": 1, "message": "2288", "line": 99, "column": 13, "nodeType": "1959", "messageId": "1960", "endLine": 99, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2283", "line": 100, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 100, "endColumn": 34}, {"ruleId": "1957", "severity": 1, "message": "2088", "line": 101, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 101, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2284", "line": 102, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 102, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2285", "line": 103, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 103, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2106", "line": 104, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 104, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2107", "line": 105, "column": 13, "nodeType": "1959", "messageId": "1960", "endLine": 105, "endColumn": 32}, {"ruleId": "1957", "severity": 1, "message": "2358", "line": 110, "column": 13, "nodeType": "1959", "messageId": "1960", "endLine": 110, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2250", "line": 113, "column": 13, "nodeType": "1959", "messageId": "1960", "endLine": 113, "endColumn": 29}, {"ruleId": "1968", "severity": 1, "message": "2533", "line": 172, "column": 12, "nodeType": "1970", "endLine": 172, "endColumn": 35, "suggestions": "2534"}, {"ruleId": "1957", "severity": 1, "message": "1973", "line": 1, "column": 27, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 34}, {"ruleId": "1957", "severity": 1, "message": "1966", "line": 4, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2301", "line": 7, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 7, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2015", "line": 7, "column": 26, "nodeType": "1959", "messageId": "1960", "endLine": 7, "endColumn": 37}, {"ruleId": "1957", "severity": 1, "message": "2535", "line": 43, "column": 21, "nodeType": "1959", "messageId": "1960", "endLine": 43, "endColumn": 34}, {"ruleId": "1968", "severity": 1, "message": "2536", "line": 63, "column": 21, "nodeType": "2537", "endLine": 63, "endColumn": 111}, {"ruleId": "1957", "severity": 1, "message": "2300", "line": 2, "column": 25, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 39}, {"ruleId": "1957", "severity": 1, "message": "2538", "line": 4, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "1965", "line": 5, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2539", "line": 11, "column": 7, "nodeType": "1959", "messageId": "1960", "endLine": 11, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2278", "line": 2, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2270", "line": 2, "column": 15, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2335", "line": 2, "column": 50, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 60}, {"ruleId": "1957", "severity": 1, "message": "2004", "line": 1, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2540", "line": 2, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2541", "line": 2, "column": 27, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 40}, {"ruleId": "1957", "severity": 1, "message": "2542", "line": 3, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2270", "line": 2, "column": 23, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 33}, {"ruleId": "1957", "severity": 1, "message": "2542", "line": 3, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2531", "line": 29, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 29, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2272", "line": 34, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 34, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2501", "line": 67, "column": 29, "nodeType": "1959", "messageId": "1960", "endLine": 67, "endColumn": 43}, {"ruleId": "1957", "severity": 1, "message": "2543", "line": 75, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 75, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2544", "line": 77, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 77, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2545", "line": 80, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 80, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2546", "line": 81, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 81, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2547", "line": 100, "column": 15, "nodeType": "1959", "messageId": "1960", "endLine": 100, "endColumn": 22}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 110, "column": 34, "nodeType": "2121", "messageId": "2122", "endLine": 110, "endColumn": 36}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 158, "column": 44, "nodeType": "2121", "messageId": "2122", "endLine": 158, "endColumn": 46}, {"ruleId": "1957", "severity": 1, "message": "2548", "line": 177, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 177, "endColumn": 21}, {"ruleId": "1968", "severity": 1, "message": "2549", "line": 306, "column": 5, "nodeType": "1970", "endLine": 306, "endColumn": 50, "suggestions": "2550"}, {"ruleId": "1968", "severity": 1, "message": "2549", "line": 322, "column": 5, "nodeType": "1970", "endLine": 322, "endColumn": 18, "suggestions": "2551"}, {"ruleId": "2119", "severity": 1, "message": "2120", "line": 483, "column": 48, "nodeType": "2121", "messageId": "2122", "endLine": 483, "endColumn": 50}, {"ruleId": "1957", "severity": 1, "message": "2552", "line": 1, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2540", "line": 2, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "1973", "line": 5, "column": 28, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 35}, {"ruleId": "1957", "severity": 1, "message": "2553", "line": 8, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 8, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2344", "line": 85, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 85, "endColumn": 24}, {"ruleId": "1968", "severity": 1, "message": "2554", "line": 98, "column": 6, "nodeType": "1970", "endLine": 98, "endColumn": 8, "suggestions": "2555"}, {"ruleId": "1968", "severity": 1, "message": "2556", "line": 121, "column": 6, "nodeType": "1970", "endLine": 121, "endColumn": 32, "suggestions": "2557"}, {"ruleId": "1968", "severity": 1, "message": "2350", "line": 125, "column": 6, "nodeType": "1970", "endLine": 125, "endColumn": 40, "suggestions": "2558"}, {"ruleId": "1968", "severity": 1, "message": "2175", "line": 125, "column": 7, "nodeType": "2121", "endLine": 125, "endColumn": 39}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 125, "column": 35, "nodeType": "2121", "messageId": "2122", "endLine": 125, "endColumn": 37}, {"ruleId": "1968", "severity": 1, "message": "2559", "line": 148, "column": 6, "nodeType": "1970", "endLine": 148, "endColumn": 33, "suggestions": "2560"}, {"ruleId": "1968", "severity": 1, "message": "2175", "line": 148, "column": 7, "nodeType": "2216", "endLine": 148, "endColumn": 32}, {"ruleId": "1957", "severity": 1, "message": "2561", "line": 156, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 156, "endColumn": 29}, {"ruleId": "1957", "severity": 1, "message": "2562", "line": 1, "column": 17, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2329", "line": 2, "column": 38, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 42}, {"ruleId": "1957", "severity": 1, "message": "1999", "line": 2, "column": 64, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 78}, {"ruleId": "1957", "severity": 1, "message": "2331", "line": 2, "column": 80, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 91}, {"ruleId": "1957", "severity": 1, "message": "2332", "line": 2, "column": 93, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 103}, {"ruleId": "1957", "severity": 1, "message": "2333", "line": 2, "column": 105, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 111}, {"ruleId": "1957", "severity": 1, "message": "2334", "line": 2, "column": 113, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 121}, {"ruleId": "1957", "severity": 1, "message": "2563", "line": 2, "column": 123, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 140}, {"ruleId": "1957", "severity": 1, "message": "2229", "line": 2, "column": 142, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 158}, {"ruleId": "1957", "severity": 1, "message": "2564", "line": 2, "column": 160, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 166}, {"ruleId": "1957", "severity": 1, "message": "2565", "line": 2, "column": 168, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 180}, {"ruleId": "1957", "severity": 1, "message": "2566", "line": 2, "column": 182, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 199}, {"ruleId": "1957", "severity": 1, "message": "2567", "line": 4, "column": 26, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 49}, {"ruleId": "1957", "severity": 1, "message": "2568", "line": 4, "column": 51, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 71}, {"ruleId": "1957", "severity": 1, "message": "2569", "line": 4, "column": 73, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 91}, {"ruleId": "1957", "severity": 1, "message": "2570", "line": 5, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 31}, {"ruleId": "1957", "severity": 1, "message": "2571", "line": 13, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 13, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "2572", "line": 14, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 14, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2573", "line": 15, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 15, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "1997", "line": 16, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 16, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "2574", "line": 17, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 17, "endColumn": 7}, {"ruleId": "1957", "severity": 1, "message": "2575", "line": 24, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 24, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2576", "line": 25, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 25, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2577", "line": 26, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 26, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2578", "line": 27, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 27, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2579", "line": 28, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 28, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2580", "line": 29, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 29, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2581", "line": 30, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 30, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2582", "line": 40, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 40, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2583", "line": 41, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 41, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2584", "line": 43, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 43, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2585", "line": 45, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 45, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2352", "line": 47, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 47, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "2586", "line": 94, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 94, "endColumn": 19}, {"ruleId": "1968", "severity": 1, "message": "2587", "line": 125, "column": 5, "nodeType": "1970", "endLine": 125, "endColumn": 7, "suggestions": "2588"}, {"ruleId": "1957", "severity": 1, "message": "2589", "line": 145, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 145, "endColumn": 30}, {"ruleId": "1957", "severity": 1, "message": "2590", "line": 162, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 162, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2591", "line": 165, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 165, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2592", "line": 170, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 170, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2593", "line": 211, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 211, "endColumn": 29}, {"ruleId": "1957", "severity": 1, "message": "2594", "line": 214, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 214, "endColumn": 33}, {"ruleId": "1957", "severity": 1, "message": "2595", "line": 227, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 227, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2596", "line": 228, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 228, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2597", "line": 228, "column": 15, "nodeType": "1959", "messageId": "1960", "endLine": 228, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2598", "line": 229, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 229, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2599", "line": 229, "column": 20, "nodeType": "1959", "messageId": "1960", "endLine": 229, "endColumn": 32}, {"ruleId": "1957", "severity": 1, "message": "2053", "line": 245, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 245, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2054", "line": 245, "column": 16, "nodeType": "1959", "messageId": "1960", "endLine": 245, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2600", "line": 247, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 247, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2601", "line": 261, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 261, "endColumn": 36}, {"ruleId": "1968", "severity": 1, "message": "2602", "line": 281, "column": 4, "nodeType": "1970", "endLine": 281, "endColumn": 6, "suggestions": "2603"}, {"ruleId": "1957", "severity": 1, "message": "2601", "line": 334, "column": 12, "nodeType": "1959", "messageId": "1960", "endLine": 334, "endColumn": 38}, {"ruleId": "1957", "severity": 1, "message": "2604", "line": 347, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 347, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2605", "line": 347, "column": 27, "nodeType": "1959", "messageId": "1960", "endLine": 347, "endColumn": 45}, {"ruleId": "1957", "severity": 1, "message": "2562", "line": 1, "column": 17, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2329", "line": 2, "column": 38, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 42}, {"ruleId": "1957", "severity": 1, "message": "1999", "line": 2, "column": 64, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 78}, {"ruleId": "1957", "severity": 1, "message": "2331", "line": 2, "column": 80, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 91}, {"ruleId": "1957", "severity": 1, "message": "2332", "line": 2, "column": 93, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 103}, {"ruleId": "1957", "severity": 1, "message": "2333", "line": 2, "column": 105, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 111}, {"ruleId": "1957", "severity": 1, "message": "2334", "line": 2, "column": 113, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 121}, {"ruleId": "1957", "severity": 1, "message": "2563", "line": 2, "column": 123, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 140}, {"ruleId": "1957", "severity": 1, "message": "2229", "line": 2, "column": 142, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 158}, {"ruleId": "1957", "severity": 1, "message": "2564", "line": 2, "column": 160, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 166}, {"ruleId": "1957", "severity": 1, "message": "2336", "line": 2, "column": 168, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 175}, {"ruleId": "1957", "severity": 1, "message": "2567", "line": 4, "column": 26, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 49}, {"ruleId": "1957", "severity": 1, "message": "2568", "line": 4, "column": 51, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 71}, {"ruleId": "1957", "severity": 1, "message": "2569", "line": 4, "column": 73, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 91}, {"ruleId": "1957", "severity": 1, "message": "2570", "line": 5, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 31}, {"ruleId": "1957", "severity": 1, "message": "2571", "line": 8, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 8, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2572", "line": 9, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2573", "line": 10, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 10, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "2574", "line": 11, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 11, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "1997", "line": 12, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 12, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2606", "line": 13, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 13, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "2607", "line": 14, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 14, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "2608", "line": 15, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 15, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "2609", "line": 22, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 22, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2610", "line": 31, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 31, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2325", "line": 32, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 32, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2582", "line": 35, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 35, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2583", "line": 36, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 36, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2584", "line": 38, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 38, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2611", "line": 39, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 39, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2612", "line": 40, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 40, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2613", "line": 42, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 42, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2614", "line": 43, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 43, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2615", "line": 44, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 44, "endColumn": 29}, {"ruleId": "1957", "severity": 1, "message": "2616", "line": 45, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 45, "endColumn": 32}, {"ruleId": "1957", "severity": 1, "message": "2617", "line": 46, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 46, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2618", "line": 47, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 47, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2619", "line": 48, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 48, "endColumn": 29}, {"ruleId": "1957", "severity": 1, "message": "2620", "line": 49, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 49, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2621", "line": 50, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 50, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2622", "line": 51, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 51, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2543", "line": 58, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 58, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2202", "line": 60, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 60, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2586", "line": 75, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 75, "endColumn": 19}, {"ruleId": "1968", "severity": 1, "message": "2559", "line": 87, "column": 5, "nodeType": "1970", "endLine": 87, "endColumn": 45, "suggestions": "2623"}, {"ruleId": "1968", "severity": 1, "message": "2175", "line": 87, "column": 6, "nodeType": "2176", "endLine": 87, "endColumn": 44}, {"ruleId": "1957", "severity": 1, "message": "2624", "line": 106, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 106, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2625", "line": 106, "column": 23, "nodeType": "1959", "messageId": "1960", "endLine": 106, "endColumn": 38}, {"ruleId": "1957", "severity": 1, "message": "2626", "line": 107, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 107, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2627", "line": 107, "column": 15, "nodeType": "1959", "messageId": "1960", "endLine": 107, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2628", "line": 108, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 108, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "2243", "line": 109, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 109, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2629", "line": 109, "column": 18, "nodeType": "1959", "messageId": "1960", "endLine": 109, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2630", "line": 110, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 110, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2590", "line": 115, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 115, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2592", "line": 119, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 119, "endColumn": 25}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 134, "column": 11, "nodeType": "2121", "messageId": "2122", "endLine": 134, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2336", "line": 2, "column": 64, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 71}, {"ruleId": "1957", "severity": 1, "message": "2631", "line": 4, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 32}, {"ruleId": "1957", "severity": 1, "message": "2632", "line": 5, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 30}, {"ruleId": "1957", "severity": 1, "message": "2633", "line": 10, "column": 30, "nodeType": "1959", "messageId": "1960", "endLine": 10, "endColumn": 39}, {"ruleId": "1957", "severity": 1, "message": "2634", "line": 2, "column": 44, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 53}, {"ruleId": "1957", "severity": 1, "message": "2635", "line": 4, "column": 46, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 65}, {"ruleId": "1957", "severity": 1, "message": "1997", "line": 4, "column": 67, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 75}, {"ruleId": "1957", "severity": 1, "message": "2531", "line": 7, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 7, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2636", "line": 8, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 8, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2282", "line": 30, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 30, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2637", "line": 31, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 31, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2084", "line": 34, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 34, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2638", "line": 45, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 45, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2639", "line": 46, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 46, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2640", "line": 47, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 47, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2641", "line": 48, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 48, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2642", "line": 52, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 52, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2643", "line": 52, "column": 21, "nodeType": "1959", "messageId": "1960", "endLine": 52, "endColumn": 34}, {"ruleId": "1957", "severity": 1, "message": "2644", "line": 53, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 53, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2645", "line": 54, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 54, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2646", "line": 57, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 57, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2647", "line": 58, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 58, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2648", "line": 58, "column": 20, "nodeType": "1959", "messageId": "1960", "endLine": 58, "endColumn": 32}, {"ruleId": "1968", "severity": 1, "message": "2649", "line": 66, "column": 5, "nodeType": "1970", "endLine": 66, "endColumn": 7, "suggestions": "2650"}, {"ruleId": "1957", "severity": 1, "message": "2651", "line": 94, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 94, "endColumn": 32}, {"ruleId": "1957", "severity": 1, "message": "2652", "line": 98, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 98, "endColumn": 32}, {"ruleId": "1957", "severity": 1, "message": "2653", "line": 127, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 127, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2654", "line": 135, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 135, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2655", "line": 139, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 139, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2656", "line": 153, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 153, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2657", "line": 156, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 156, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2634", "line": 2, "column": 15, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2658", "line": 6, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 6, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2659", "line": 42, "column": 16, "nodeType": "1959", "messageId": "1960", "endLine": 42, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2660", "line": 44, "column": 16, "nodeType": "1959", "messageId": "1960", "endLine": 44, "endColumn": 28}, {"ruleId": "1968", "severity": 1, "message": "2661", "line": 113, "column": 12, "nodeType": "1970", "endLine": 113, "endColumn": 14, "suggestions": "2662"}, {"ruleId": "1968", "severity": 1, "message": "2663", "line": 154, "column": 12, "nodeType": "1970", "endLine": 154, "endColumn": 33, "suggestions": "2664"}, {"ruleId": "1968", "severity": 1, "message": "2663", "line": 222, "column": 12, "nodeType": "1970", "endLine": 222, "endColumn": 33, "suggestions": "2665"}, {"ruleId": "1968", "severity": 1, "message": "2666", "line": 375, "column": 15, "nodeType": "2342", "endLine": 392, "endColumn": 10}, {"ruleId": "1968", "severity": 1, "message": "2667", "line": 521, "column": 13, "nodeType": "2342", "endLine": 521, "endColumn": 43}, {"ruleId": "1957", "severity": 1, "message": "2668", "line": 18, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 18, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2669", "line": 64, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 64, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2670", "line": 66, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 66, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2671", "line": 77, "column": 20, "nodeType": "1959", "messageId": "1960", "endLine": 77, "endColumn": 32}, {"ruleId": "1957", "severity": 1, "message": "2672", "line": 93, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 93, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2673", "line": 313, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 313, "endColumn": 24}, {"ruleId": "1968", "severity": 1, "message": "2674", "line": 529, "column": 5, "nodeType": "1970", "endLine": 529, "endColumn": 32, "suggestions": "2675"}, {"ruleId": "1957", "severity": 1, "message": "2676", "line": 2, "column": 14, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2677", "line": 16, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 16, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2202", "line": 19, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 19, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2678", "line": 22, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 22, "endColumn": 20}, {"ruleId": "2119", "severity": 1, "message": "2120", "line": 43, "column": 100, "nodeType": "2121", "messageId": "2122", "endLine": 43, "endColumn": 102}, {"ruleId": "1957", "severity": 1, "message": "2679", "line": 4, "column": 23, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 30}, {"ruleId": "1957", "severity": 1, "message": "2680", "line": 4, "column": 32, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 45}, {"ruleId": "1957", "severity": 1, "message": "2681", "line": 10, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 10, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2682", "line": 65, "column": 12, "nodeType": "1959", "messageId": "1960", "endLine": 65, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2683", "line": 65, "column": 24, "nodeType": "1959", "messageId": "1960", "endLine": 65, "endColumn": 37}, {"ruleId": "1957", "severity": 1, "message": "2684", "line": 78, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 78, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2685", "line": 78, "column": 24, "nodeType": "1959", "messageId": "1960", "endLine": 78, "endColumn": 39}, {"ruleId": "1968", "severity": 1, "message": "2686", "line": 120, "column": 6, "nodeType": "1970", "endLine": 120, "endColumn": 8, "suggestions": "2687"}, {"ruleId": "1957", "severity": 1, "message": "2688", "line": 157, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 157, "endColumn": 32}, {"ruleId": "1957", "severity": 1, "message": "2689", "line": 280, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 280, "endColumn": 29}, {"ruleId": "1957", "severity": 1, "message": "2690", "line": 296, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 296, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2691", "line": 461, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 461, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2692", "line": 462, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 462, "endColumn": 20}, {"ruleId": "1968", "severity": 1, "message": "2693", "line": 467, "column": 3, "nodeType": "1970", "endLine": 467, "endColumn": 5, "suggestions": "2694"}, {"ruleId": "1957", "severity": 1, "message": "2270", "line": 2, "column": 15, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2565", "line": 2, "column": 27, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 39}, {"ruleId": "1957", "severity": 1, "message": "2566", "line": 2, "column": 41, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 58}, {"ruleId": "1957", "severity": 1, "message": "2229", "line": 2, "column": 72, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 88}, {"ruleId": "1957", "severity": 1, "message": "2564", "line": 2, "column": 90, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 96}, {"ruleId": "1957", "severity": 1, "message": "2695", "line": 9, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 31}, {"ruleId": "1957", "severity": 1, "message": "2278", "line": 2, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2270", "line": 2, "column": 23, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 33}, {"ruleId": "1957", "severity": 1, "message": "2278", "line": 3, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 5}, {"ruleId": "1957", "severity": 1, "message": "2270", "line": 4, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "2332", "line": 9, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "2564", "line": 6, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 6, "endColumn": 8}, {"ruleId": "1957", "severity": 1, "message": "2333", "line": 9, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 8}, {"ruleId": "1957", "severity": 1, "message": "2334", "line": 10, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 10, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "2331", "line": 11, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 11, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2336", "line": 12, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 12, "endColumn": 9}, {"ruleId": "1957", "severity": 1, "message": "2696", "line": 19, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 19, "endColumn": 32}, {"ruleId": "1957", "severity": 1, "message": "2409", "line": 35, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 35, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2697", "line": 37, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 37, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2480", "line": 38, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 38, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2357", "line": 39, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 39, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2698", "line": 40, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 40, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2245", "line": 42, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 42, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2250", "line": 48, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 48, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2699", "line": 55, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 55, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2700", "line": 56, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 56, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2701", "line": 57, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 57, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2702", "line": 86, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 86, "endColumn": 31}, {"ruleId": "1957", "severity": 1, "message": "2703", "line": 90, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 90, "endColumn": 31}, {"ruleId": "1957", "severity": 1, "message": "2704", "line": 95, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 95, "endColumn": 33}, {"ruleId": "1968", "severity": 1, "message": "2705", "line": 195, "column": 5, "nodeType": "1970", "endLine": 195, "endColumn": 30, "suggestions": "2706"}, {"ruleId": "1957", "severity": 1, "message": "2329", "line": 2, "column": 38, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 42}, {"ruleId": "1957", "severity": 1, "message": "2336", "line": 2, "column": 64, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 71}, {"ruleId": "1957", "severity": 1, "message": "2631", "line": 4, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 32}, {"ruleId": "1957", "severity": 1, "message": "2632", "line": 5, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 30}, {"ruleId": "1957", "severity": 1, "message": "2707", "line": 9, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2708", "line": 9, "column": 23, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 30}, {"ruleId": "1957", "severity": 1, "message": "2633", "line": 9, "column": 32, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 41}, {"ruleId": "1957", "severity": 1, "message": "2709", "line": 9, "column": 43, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 51}, {"ruleId": "1957", "severity": 1, "message": "2710", "line": 9, "column": 53, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 63}, {"ruleId": "1957", "severity": 1, "message": "2711", "line": 9, "column": 65, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 77}, {"ruleId": "1957", "severity": 1, "message": "2712", "line": 9, "column": 79, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 90}, {"ruleId": "1957", "severity": 1, "message": "2713", "line": 9, "column": 92, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 102}, {"ruleId": "1957", "severity": 1, "message": "2714", "line": 9, "column": 104, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 116}, {"ruleId": "1957", "severity": 1, "message": "2715", "line": 9, "column": 118, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 129}, {"ruleId": "1957", "severity": 1, "message": "2716", "line": 9, "column": 131, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 140}, {"ruleId": "1957", "severity": 1, "message": "2717", "line": 15, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 15, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2393", "line": 16, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 16, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2488", "line": 17, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 17, "endColumn": 8}, {"ruleId": "1957", "severity": 1, "message": "2718", "line": 18, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 18, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "2075", "line": 19, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 19, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2487", "line": 22, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 22, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2719", "line": 23, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 23, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2720", "line": 24, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 24, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2721", "line": 25, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 25, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2722", "line": 26, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 26, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2723", "line": 27, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 27, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2724", "line": 28, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 28, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "2725", "line": 29, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 29, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2543", "line": 33, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 33, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2586", "line": 61, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 61, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2703", "line": 81, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 81, "endColumn": 31}, {"ruleId": "1957", "severity": 1, "message": "2726", "line": 82, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 82, "endColumn": 35}, {"ruleId": "1957", "severity": 1, "message": "2562", "line": 1, "column": 17, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2329", "line": 3, "column": 38, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 42}, {"ruleId": "1957", "severity": 1, "message": "1999", "line": 3, "column": 64, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 78}, {"ruleId": "1957", "severity": 1, "message": "2331", "line": 3, "column": 80, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 91}, {"ruleId": "1957", "severity": 1, "message": "2332", "line": 3, "column": 93, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 103}, {"ruleId": "1957", "severity": 1, "message": "2333", "line": 3, "column": 105, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 111}, {"ruleId": "1957", "severity": 1, "message": "2334", "line": 3, "column": 113, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 121}, {"ruleId": "1957", "severity": 1, "message": "2563", "line": 3, "column": 123, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 140}, {"ruleId": "1957", "severity": 1, "message": "2229", "line": 3, "column": 142, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 158}, {"ruleId": "1957", "severity": 1, "message": "2564", "line": 3, "column": 160, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 166}, {"ruleId": "1957", "severity": 1, "message": "2567", "line": 5, "column": 26, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 49}, {"ruleId": "1957", "severity": 1, "message": "2568", "line": 5, "column": 51, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 71}, {"ruleId": "1957", "severity": 1, "message": "2569", "line": 5, "column": 73, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 91}, {"ruleId": "1957", "severity": 1, "message": "2570", "line": 6, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 6, "endColumn": 31}, {"ruleId": "1957", "severity": 1, "message": "2571", "line": 8, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 8, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2572", "line": 9, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2573", "line": 10, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 10, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "2574", "line": 11, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 11, "endColumn": 8}, {"ruleId": "1957", "severity": 1, "message": "2582", "line": 19, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 19, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2584", "line": 22, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 22, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2727", "line": 24, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 24, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2728", "line": 25, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 25, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2729", "line": 26, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 26, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2730", "line": 27, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 27, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2543", "line": 31, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 31, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2586", "line": 36, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 36, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2731", "line": 104, "column": 21, "nodeType": "1959", "messageId": "1960", "endLine": 104, "endColumn": 33}, {"ruleId": "1957", "severity": 1, "message": "2732", "line": 105, "column": 24, "nodeType": "1959", "messageId": "1960", "endLine": 105, "endColumn": 40}, {"ruleId": "1957", "severity": 1, "message": "2589", "line": 120, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 120, "endColumn": 31}, {"ruleId": "1957", "severity": 1, "message": "2590", "line": 154, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 154, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2733", "line": 157, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 157, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2734", "line": 1, "column": 17, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2476", "line": 4, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2634", "line": 8, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 8, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "2735", "line": 15, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 15, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2736", "line": 15, "column": 30, "nodeType": "1959", "messageId": "1960", "endLine": 15, "endColumn": 44}, {"ruleId": "1957", "severity": 1, "message": "2737", "line": 15, "column": 46, "nodeType": "1959", "messageId": "1960", "endLine": 15, "endColumn": 60}, {"ruleId": "1957", "severity": 1, "message": "2738", "line": 15, "column": 62, "nodeType": "1959", "messageId": "1960", "endLine": 15, "endColumn": 78}, {"ruleId": "1957", "severity": 1, "message": "2739", "line": 15, "column": 80, "nodeType": "1959", "messageId": "1960", "endLine": 15, "endColumn": 96}, {"ruleId": "1957", "severity": 1, "message": "2740", "line": 17, "column": 29, "nodeType": "1959", "messageId": "1960", "endLine": 17, "endColumn": 33}, {"ruleId": "1957", "severity": 1, "message": "2741", "line": 17, "column": 35, "nodeType": "1959", "messageId": "1960", "endLine": 17, "endColumn": 47}, {"ruleId": "1957", "severity": 1, "message": "2235", "line": 17, "column": 49, "nodeType": "1959", "messageId": "1960", "endLine": 17, "endColumn": 55}, {"ruleId": "1957", "severity": 1, "message": "2742", "line": 22, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 22, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "2743", "line": 58, "column": 13, "nodeType": "1959", "messageId": "1960", "endLine": 58, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2485", "line": 65, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 65, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "2744", "line": 66, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 66, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2488", "line": 72, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 72, "endColumn": 8}, {"ruleId": "1957", "severity": 1, "message": "2487", "line": 73, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 73, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2720", "line": 74, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 74, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2393", "line": 75, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 75, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2745", "line": 76, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 76, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2495", "line": 77, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 77, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2746", "line": 78, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 78, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2496", "line": 79, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 79, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2491", "line": 80, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 80, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2747", "line": 81, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 81, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2722", "line": 82, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 82, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2244", "line": 83, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 83, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2239", "line": 84, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 84, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2748", "line": 87, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 87, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2749", "line": 89, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 89, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2245", "line": 91, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 91, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2250", "line": 93, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 93, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2750", "line": 99, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 99, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2751", "line": 99, "column": 21, "nodeType": "1959", "messageId": "1960", "endLine": 99, "endColumn": 33}, {"ruleId": "1957", "severity": 1, "message": "2752", "line": 103, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 103, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2753", "line": 103, "column": 26, "nodeType": "1959", "messageId": "1960", "endLine": 103, "endColumn": 44}, {"ruleId": "1957", "severity": 1, "message": "2754", "line": 161, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 161, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2755", "line": 164, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 164, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2756", "line": 170, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 170, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2757", "line": 173, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 173, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2758", "line": 183, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 183, "endColumn": 21}, {"ruleId": "1968", "severity": 1, "message": "2759", "line": 339, "column": 5, "nodeType": "1970", "endLine": 339, "endColumn": 18, "suggestions": "2760"}, {"ruleId": "1957", "severity": 1, "message": "2761", "line": 341, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 341, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2498", "line": 593, "column": 33, "nodeType": "1959", "messageId": "1960", "endLine": 593, "endColumn": 47}, {"ruleId": "1957", "severity": 1, "message": "2357", "line": 593, "column": 49, "nodeType": "1959", "messageId": "1960", "endLine": 593, "endColumn": 60}, {"ruleId": "1957", "severity": 1, "message": "2284", "line": 593, "column": 62, "nodeType": "1959", "messageId": "1960", "endLine": 593, "endColumn": 67}, {"ruleId": "1957", "severity": 1, "message": "2244", "line": 593, "column": 69, "nodeType": "1959", "messageId": "1960", "endLine": 593, "endColumn": 85}, {"ruleId": "1957", "severity": 1, "message": "2762", "line": 783, "column": 7, "nodeType": "1959", "messageId": "1960", "endLine": 783, "endColumn": 17}, {"ruleId": "1968", "severity": 1, "message": "2763", "line": 924, "column": 3, "nodeType": "1970", "endLine": 924, "endColumn": 19, "suggestions": "2764"}, {"ruleId": "1957", "severity": 1, "message": "2765", "line": 1024, "column": 15, "nodeType": "1959", "messageId": "1960", "endLine": 1024, "endColumn": 33}, {"ruleId": "1957", "severity": 1, "message": "2766", "line": 1033, "column": 15, "nodeType": "1959", "messageId": "1960", "endLine": 1033, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2634", "line": 2, "column": 56, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 65}, {"ruleId": "1957", "severity": 1, "message": "2334", "line": 2, "column": 67, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 75}, {"ruleId": "1957", "severity": 1, "message": "2205", "line": 2, "column": 77, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 83}, {"ruleId": "1957", "severity": 1, "message": "2635", "line": 13, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 13, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2767", "line": 47, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 47, "endColumn": 48}, {"ruleId": "1957", "severity": 1, "message": "2452", "line": 59, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 59, "endColumn": 39}, {"ruleId": "1957", "severity": 1, "message": "2768", "line": 68, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 68, "endColumn": 41}, {"ruleId": "1957", "severity": 1, "message": "2769", "line": 74, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 74, "endColumn": 42}, {"ruleId": "1957", "severity": 1, "message": "2329", "line": 3, "column": 38, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 42}, {"ruleId": "1957", "severity": 1, "message": "2336", "line": 3, "column": 56, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 63}, {"ruleId": "1957", "severity": 1, "message": "2246", "line": 13, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 13, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2390", "line": 14, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 14, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "2391", "line": 15, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 15, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2058", "line": 16, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 16, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "2060", "line": 18, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 18, "endColumn": 9}, {"ruleId": "1957", "severity": 1, "message": "2061", "line": 19, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 19, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "2062", "line": 20, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 20, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2063", "line": 21, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 21, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2392", "line": 22, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 22, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2393", "line": 23, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 23, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2394", "line": 24, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 24, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2075", "line": 25, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 25, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2770", "line": 37, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 37, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2364", "line": 39, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 39, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2771", "line": 41, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 41, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2772", "line": 45, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 45, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2773", "line": 50, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 50, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2774", "line": 50, "column": 21, "nodeType": "1959", "messageId": "1960", "endLine": 50, "endColumn": 34}, {"ruleId": "1957", "severity": 1, "message": "2775", "line": 52, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 52, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2776", "line": 52, "column": 30, "nodeType": "1959", "messageId": "1960", "endLine": 52, "endColumn": 52}, {"ruleId": "1957", "severity": 1, "message": "2777", "line": 53, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 53, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2778", "line": 53, "column": 27, "nodeType": "1959", "messageId": "1960", "endLine": 53, "endColumn": 46}, {"ruleId": "1957", "severity": 1, "message": "2779", "line": 3, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 8}, {"ruleId": "1957", "severity": 1, "message": "2780", "line": 4, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2781", "line": 5, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2782", "line": 6, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 6, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "2530", "line": 9, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 9}, {"ruleId": "1957", "severity": 1, "message": "2227", "line": 17, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 17, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "2228", "line": 18, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 18, "endColumn": 7}, {"ruleId": "1957", "severity": 1, "message": "2229", "line": 19, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 19, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2230", "line": 20, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 20, "endColumn": 7}, {"ruleId": "1957", "severity": 1, "message": "2231", "line": 23, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 23, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2232", "line": 24, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 24, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2233", "line": 25, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 25, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2336", "line": 26, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 26, "endColumn": 9}, {"ruleId": "1957", "severity": 1, "message": "2429", "line": 43, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 43, "endColumn": 35}, {"ruleId": "1957", "severity": 1, "message": "2783", "line": 44, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 44, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2784", "line": 50, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 50, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2785", "line": 51, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 51, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2786", "line": 53, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 53, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2787", "line": 54, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 54, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2788", "line": 55, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 55, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2789", "line": 56, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 56, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2249", "line": 59, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 59, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2282", "line": 60, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 60, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2637", "line": 61, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 61, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2240", "line": 63, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 63, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2241", "line": 69, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 69, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2242", "line": 70, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 70, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2243", "line": 76, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 76, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "2790", "line": 78, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 78, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2085", "line": 81, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 81, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2086", "line": 82, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 82, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2084", "line": 83, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 83, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2791", "line": 84, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 84, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2792", "line": 85, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 85, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2793", "line": 86, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 86, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2256", "line": 88, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 88, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2254", "line": 90, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 90, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "2258", "line": 92, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 92, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "2794", "line": 93, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 93, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2251", "line": 95, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 95, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2393", "line": 102, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 102, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2392", "line": 102, "column": 22, "nodeType": "1959", "messageId": "1960", "endLine": 102, "endColumn": 36}, {"ruleId": "1957", "severity": 1, "message": "2075", "line": 103, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 103, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2394", "line": 103, "column": 26, "nodeType": "1959", "messageId": "1960", "endLine": 103, "endColumn": 44}, {"ruleId": "1957", "severity": 1, "message": "2795", "line": 104, "column": 17, "nodeType": "1959", "messageId": "1960", "endLine": 104, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2796", "line": 105, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 105, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2797", "line": 106, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 106, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "2798", "line": 106, "column": 14, "nodeType": "1959", "messageId": "1960", "endLine": 106, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2252", "line": 107, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 107, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2799", "line": 107, "column": 17, "nodeType": "1959", "messageId": "1960", "endLine": 107, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2456", "line": 108, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 108, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2800", "line": 108, "column": 23, "nodeType": "1959", "messageId": "1960", "endLine": 108, "endColumn": 38}, {"ruleId": "1957", "severity": 1, "message": "2701", "line": 119, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 119, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2801", "line": 119, "column": 17, "nodeType": "1959", "messageId": "1960", "endLine": 119, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2802", "line": 126, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 126, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2803", "line": 126, "column": 26, "nodeType": "1959", "messageId": "1960", "endLine": 126, "endColumn": 44}, {"ruleId": "1968", "severity": 1, "message": "2804", "line": 149, "column": 5, "nodeType": "1970", "endLine": 149, "endColumn": 60, "suggestions": "2805"}, {"ruleId": "1957", "severity": 1, "message": "2806", "line": 152, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 152, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2807", "line": 164, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 164, "endColumn": 24}, {"ruleId": "1968", "severity": 1, "message": "2808", "line": 168, "column": 5, "nodeType": "1970", "endLine": 168, "endColumn": 60, "suggestions": "2809"}, {"ruleId": "1957", "severity": 1, "message": "2810", "line": 170, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 170, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2259", "line": 204, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 204, "endColumn": 30}, {"ruleId": "1957", "severity": 1, "message": "2260", "line": 208, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 208, "endColumn": 29}, {"ruleId": "1957", "severity": 1, "message": "2428", "line": 4, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2085", "line": 11, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 11, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2086", "line": 12, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 12, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2084", "line": 13, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 13, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2434", "line": 21, "column": 21, "nodeType": "1959", "messageId": "1960", "endLine": 21, "endColumn": 34}, {"ruleId": "1957", "severity": 1, "message": "2435", "line": 22, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 22, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2436", "line": 22, "column": 33, "nodeType": "1959", "messageId": "1960", "endLine": 22, "endColumn": 44}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 84, "column": 22, "nodeType": "2121", "messageId": "2122", "endLine": 84, "endColumn": 24}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 84, "column": 53, "nodeType": "2121", "messageId": "2122", "endLine": 84, "endColumn": 55}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 87, "column": 36, "nodeType": "2121", "messageId": "2122", "endLine": 87, "endColumn": 38}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 87, "column": 63, "nodeType": "2121", "messageId": "2122", "endLine": 87, "endColumn": 65}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 90, "column": 36, "nodeType": "2121", "messageId": "2122", "endLine": 90, "endColumn": 38}, {"ruleId": "1957", "severity": 1, "message": "2438", "line": 97, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 97, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2439", "line": 98, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 98, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2440", "line": 101, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 101, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2441", "line": 102, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 102, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2442", "line": 111, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 111, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2443", "line": 131, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 131, "endColumn": 16}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 270, "column": 45, "nodeType": "2121", "messageId": "2122", "endLine": 270, "endColumn": 47}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 270, "column": 104, "nodeType": "2121", "messageId": "2122", "endLine": 270, "endColumn": 106}, {"ruleId": "1957", "severity": 1, "message": "2562", "line": 1, "column": 17, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2329", "line": 2, "column": 38, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 42}, {"ruleId": "1957", "severity": 1, "message": "1999", "line": 2, "column": 64, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 78}, {"ruleId": "1957", "severity": 1, "message": "2332", "line": 2, "column": 93, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 103}, {"ruleId": "1957", "severity": 1, "message": "2563", "line": 2, "column": 123, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 140}, {"ruleId": "1957", "severity": 1, "message": "2229", "line": 2, "column": 142, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 158}, {"ruleId": "1957", "severity": 1, "message": "2564", "line": 2, "column": 160, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 166}, {"ruleId": "1957", "severity": 1, "message": "2567", "line": 4, "column": 26, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 49}, {"ruleId": "1957", "severity": 1, "message": "2568", "line": 4, "column": 51, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 71}, {"ruleId": "1957", "severity": 1, "message": "2569", "line": 4, "column": 73, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 91}, {"ruleId": "1957", "severity": 1, "message": "2570", "line": 5, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 31}, {"ruleId": "1957", "severity": 1, "message": "2811", "line": 23, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 23, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2094", "line": 24, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 24, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2812", "line": 26, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 26, "endColumn": 29}, {"ruleId": "1957", "severity": 1, "message": "2090", "line": 27, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 27, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2813", "line": 28, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 28, "endColumn": 29}, {"ruleId": "1957", "severity": 1, "message": "2814", "line": 29, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 29, "endColumn": 31}, {"ruleId": "1957", "severity": 1, "message": "2105", "line": 31, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 31, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2416", "line": 119, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 119, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2478", "line": 11, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 11, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2428", "line": 14, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 14, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2815", "line": 67, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 67, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2816", "line": 180, "column": 86, "nodeType": "1959", "messageId": "1960", "endLine": 180, "endColumn": 101}, {"ruleId": "1957", "severity": 1, "message": "2496", "line": 184, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 184, "endColumn": 24}, {"ruleId": "1968", "severity": 1, "message": "2817", "line": 563, "column": 5, "nodeType": "1970", "endLine": 563, "endColumn": 40, "suggestions": "2818"}, {"ruleId": "1968", "severity": 1, "message": "2819", "line": 586, "column": 6, "nodeType": "1970", "endLine": 586, "endColumn": 42, "suggestions": "2820"}, {"ruleId": "1968", "severity": 1, "message": "2821", "line": 600, "column": 6, "nodeType": "1970", "endLine": 600, "endColumn": 50, "suggestions": "2822"}, {"ruleId": "1968", "severity": 1, "message": "2823", "line": 877, "column": 5, "nodeType": "1970", "endLine": 877, "endColumn": 160, "suggestions": "2824"}, {"ruleId": "1968", "severity": 1, "message": "2825", "line": 945, "column": 5, "nodeType": "1970", "endLine": 945, "endColumn": 110, "suggestions": "2826"}, {"ruleId": "1968", "severity": 1, "message": "2827", "line": 993, "column": 5, "nodeType": "1970", "endLine": 993, "endColumn": 34, "suggestions": "2828"}, {"ruleId": "1968", "severity": 1, "message": "2829", "line": 1011, "column": 5, "nodeType": "1970", "endLine": 1011, "endColumn": 34, "suggestions": "2830"}, {"ruleId": "1968", "severity": 1, "message": "2829", "line": 1025, "column": 5, "nodeType": "1970", "endLine": 1025, "endColumn": 34, "suggestions": "2831"}, {"ruleId": "1968", "severity": 1, "message": "2829", "line": 1028, "column": 5, "nodeType": "1970", "endLine": 1028, "endColumn": 40, "suggestions": "2832"}, {"ruleId": "1957", "severity": 1, "message": "2833", "line": 1238, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 1238, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2834", "line": 1241, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 1241, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2336", "line": 2, "column": 64, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 71}, {"ruleId": "1957", "severity": 1, "message": "2564", "line": 2, "column": 73, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 79}, {"ruleId": "1957", "severity": 1, "message": "2017", "line": 15, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 15, "endColumn": 9}, {"ruleId": "1957", "severity": 1, "message": "2835", "line": 18, "column": 48, "nodeType": "1959", "messageId": "1960", "endLine": 18, "endColumn": 76}, {"ruleId": "1957", "severity": 1, "message": "2836", "line": 18, "column": 78, "nodeType": "1959", "messageId": "1960", "endLine": 18, "endColumn": 85}, {"ruleId": "1957", "severity": 1, "message": "2383", "line": 20, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 20, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2837", "line": 52, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 52, "endColumn": 31}, {"ruleId": "1957", "severity": 1, "message": "2838", "line": 54, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 54, "endColumn": 31}, {"ruleId": "1957", "severity": 1, "message": "2839", "line": 59, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 59, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2749", "line": 59, "column": 18, "nodeType": "1959", "messageId": "1960", "endLine": 59, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2840", "line": 60, "column": 27, "nodeType": "1959", "messageId": "1960", "endLine": 60, "endColumn": 46}, {"ruleId": "1957", "severity": 1, "message": "2053", "line": 61, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 61, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2054", "line": 61, "column": 16, "nodeType": "1959", "messageId": "1960", "endLine": 61, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2810", "line": 85, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 85, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2592", "line": 92, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 92, "endColumn": 25}, {"ruleId": "1968", "severity": 1, "message": "2841", "line": 183, "column": 5, "nodeType": "1970", "endLine": 183, "endColumn": 52, "suggestions": "2842"}, {"ruleId": "1968", "severity": 1, "message": "2175", "line": 183, "column": 6, "nodeType": "2176", "endLine": 183, "endColumn": 51}, {"ruleId": "1957", "severity": 1, "message": "2270", "line": 2, "column": 92, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 102}, {"ruleId": "1957", "severity": 1, "message": "2843", "line": 76, "column": 19, "nodeType": "1959", "messageId": "1960", "endLine": 76, "endColumn": 30}, {"ruleId": "1957", "severity": 1, "message": "2562", "line": 1, "column": 17, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2329", "line": 2, "column": 38, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 42}, {"ruleId": "1957", "severity": 1, "message": "1999", "line": 2, "column": 64, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 78}, {"ruleId": "1957", "severity": 1, "message": "2332", "line": 2, "column": 93, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 103}, {"ruleId": "1957", "severity": 1, "message": "2563", "line": 2, "column": 123, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 140}, {"ruleId": "1957", "severity": 1, "message": "2229", "line": 2, "column": 142, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 158}, {"ruleId": "1957", "severity": 1, "message": "2564", "line": 2, "column": 160, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 166}, {"ruleId": "1957", "severity": 1, "message": "2232", "line": 2, "column": 177, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 193}, {"ruleId": "1957", "severity": 1, "message": "2567", "line": 4, "column": 26, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 49}, {"ruleId": "1957", "severity": 1, "message": "2568", "line": 4, "column": 51, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 71}, {"ruleId": "1957", "severity": 1, "message": "2569", "line": 4, "column": 73, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 91}, {"ruleId": "1957", "severity": 1, "message": "2570", "line": 5, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 31}, {"ruleId": "1957", "severity": 1, "message": "2571", "line": 7, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 7, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2572", "line": 8, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 8, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2573", "line": 9, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "2574", "line": 10, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 10, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "1997", "line": 11, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 11, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2576", "line": 27, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 27, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2577", "line": 28, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 28, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2578", "line": 29, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 29, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2579", "line": 30, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 30, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2582", "line": 38, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 38, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2583", "line": 39, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 39, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2584", "line": 41, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 41, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2611", "line": 42, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 42, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2612", "line": 43, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 43, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2844", "line": 44, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 44, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2613", "line": 45, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 45, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2615", "line": 47, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 47, "endColumn": 30}, {"ruleId": "1957", "severity": 1, "message": "2616", "line": 48, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 48, "endColumn": 33}, {"ruleId": "1957", "severity": 1, "message": "2617", "line": 49, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 49, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2619", "line": 51, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 51, "endColumn": 30}, {"ruleId": "1957", "severity": 1, "message": "2620", "line": 52, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 52, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2621", "line": 53, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 53, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2622", "line": 54, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 54, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2543", "line": 58, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 58, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2845", "line": 152, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 152, "endColumn": 40}, {"ruleId": "1957", "severity": 1, "message": "2846", "line": 153, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 153, "endColumn": 41}, {"ruleId": "1957", "severity": 1, "message": "2847", "line": 154, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 154, "endColumn": 47}, {"ruleId": "1957", "severity": 1, "message": "2053", "line": 156, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 156, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2848", "line": 185, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 185, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2590", "line": 220, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 220, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2591", "line": 223, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 223, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2592", "line": 228, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 228, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2159", "line": 245, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 245, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2624", "line": 249, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 249, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2630", "line": 254, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 254, "endColumn": 20}, {"ruleId": "1968", "severity": 1, "message": "2849", "line": 263, "column": 6, "nodeType": "1970", "endLine": 263, "endColumn": 8, "suggestions": "2850"}, {"ruleId": "1957", "severity": 1, "message": "2851", "line": 298, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 298, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2562", "line": 1, "column": 17, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2475", "line": 1, "column": 49, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 55}, {"ruleId": "1957", "severity": 1, "message": "2852", "line": 1, "column": 69, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 80}, {"ruleId": "1957", "severity": 1, "message": "2329", "line": 2, "column": 38, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 42}, {"ruleId": "1957", "severity": 1, "message": "2332", "line": 2, "column": 93, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 103}, {"ruleId": "1957", "severity": 1, "message": "2563", "line": 2, "column": 123, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 140}, {"ruleId": "1957", "severity": 1, "message": "2229", "line": 2, "column": 142, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 158}, {"ruleId": "1957", "severity": 1, "message": "2564", "line": 2, "column": 160, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 166}, {"ruleId": "1957", "severity": 1, "message": "2476", "line": 2, "column": 195, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 212}, {"ruleId": "1957", "severity": 1, "message": "2567", "line": 5, "column": 26, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 49}, {"ruleId": "1957", "severity": 1, "message": "2568", "line": 5, "column": 51, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 71}, {"ruleId": "1957", "severity": 1, "message": "2569", "line": 5, "column": 73, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 91}, {"ruleId": "1957", "severity": 1, "message": "2570", "line": 6, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 6, "endColumn": 31}, {"ruleId": "1957", "severity": 1, "message": "2575", "line": 8, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 8, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2576", "line": 9, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2577", "line": 10, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 10, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2578", "line": 11, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 11, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2579", "line": 12, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 12, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2580", "line": 13, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 13, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2571", "line": 15, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 15, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2572", "line": 16, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 16, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2573", "line": 17, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 17, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "2574", "line": 18, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 18, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "1997", "line": 19, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 19, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2853", "line": 34, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 34, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2854", "line": 44, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 44, "endColumn": 29}, {"ruleId": "1957", "severity": 1, "message": "2582", "line": 45, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 45, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2583", "line": 46, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 46, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2584", "line": 48, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 48, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2611", "line": 49, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 49, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2612", "line": 50, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 50, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2844", "line": 51, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 51, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2613", "line": 52, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 52, "endColumn": 29}, {"ruleId": "1957", "severity": 1, "message": "2615", "line": 54, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 54, "endColumn": 35}, {"ruleId": "1957", "severity": 1, "message": "2616", "line": 55, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 55, "endColumn": 38}, {"ruleId": "1957", "severity": 1, "message": "2617", "line": 56, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 56, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2619", "line": 58, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 58, "endColumn": 35}, {"ruleId": "1957", "severity": 1, "message": "2620", "line": 59, "column": 6, "nodeType": "1959", "messageId": "1960", "endLine": 59, "endColumn": 29}, {"ruleId": "1957", "severity": 1, "message": "2621", "line": 60, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 60, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2622", "line": 61, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 61, "endColumn": 29}, {"ruleId": "1957", "severity": 1, "message": "2855", "line": 63, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 63, "endColumn": 29}, {"ruleId": "1957", "severity": 1, "message": "2543", "line": 66, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 66, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2845", "line": 89, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 89, "endColumn": 42}, {"ruleId": "1957", "severity": 1, "message": "2846", "line": 90, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 90, "endColumn": 43}, {"ruleId": "1957", "severity": 1, "message": "2847", "line": 91, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 91, "endColumn": 46}, {"ruleId": "1968", "severity": 1, "message": "2856", "line": 112, "column": 5, "nodeType": "1970", "endLine": 112, "endColumn": 52, "suggestions": "2857"}, {"ruleId": "1957", "severity": 1, "message": "2590", "line": 117, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 117, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2591", "line": 120, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 120, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2592", "line": 125, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 125, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2858", "line": 135, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 135, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2159", "line": 175, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 175, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2859", "line": 183, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 183, "endColumn": 20}, {"ruleId": "1968", "severity": 1, "message": "2849", "line": 188, "column": 4, "nodeType": "1970", "endLine": 188, "endColumn": 6, "suggestions": "2860"}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 193, "column": 19, "nodeType": "2121", "messageId": "2122", "endLine": 193, "endColumn": 21}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 194, "column": 19, "nodeType": "2121", "messageId": "2122", "endLine": 194, "endColumn": 21}, {"ruleId": "2119", "severity": 1, "message": "2120", "line": 194, "column": 39, "nodeType": "2121", "messageId": "2122", "endLine": 194, "endColumn": 41}, {"ruleId": "2119", "severity": 1, "message": "2120", "line": 213, "column": 19, "nodeType": "2121", "messageId": "2122", "endLine": 213, "endColumn": 21}, {"ruleId": "2119", "severity": 1, "message": "2141", "line": 226, "column": 20, "nodeType": "2121", "messageId": "2122", "endLine": 226, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2861", "line": 279, "column": 7, "nodeType": "1959", "messageId": "1960", "endLine": 279, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2053", "line": 307, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 307, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2848", "line": 334, "column": 7, "nodeType": "1959", "messageId": "1960", "endLine": 334, "endColumn": 23}, {"ruleId": "1968", "severity": 1, "message": "2862", "line": 371, "column": 4, "nodeType": "1970", "endLine": 371, "endColumn": 6, "suggestions": "2863"}, {"ruleId": "1957", "severity": 1, "message": "2562", "line": 1, "column": 17, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2526", "line": 1, "column": 29, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 37}, {"ruleId": "1957", "severity": 1, "message": "2519", "line": 1, "column": 38, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 47}, {"ruleId": "1957", "severity": 1, "message": "2269", "line": 1, "column": 49, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 59}, {"ruleId": "1957", "severity": 1, "message": "2475", "line": 1, "column": 61, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 67}, {"ruleId": "1957", "severity": 1, "message": "2634", "line": 2, "column": 27, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 36}, {"ruleId": "1957", "severity": 1, "message": "2329", "line": 2, "column": 38, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 42}, {"ruleId": "1957", "severity": 1, "message": "2205", "line": 2, "column": 56, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 62}, {"ruleId": "1957", "severity": 1, "message": "1999", "line": 2, "column": 64, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 78}, {"ruleId": "1957", "severity": 1, "message": "2331", "line": 2, "column": 80, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 91}, {"ruleId": "1957", "severity": 1, "message": "2332", "line": 2, "column": 93, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 103}, {"ruleId": "1957", "severity": 1, "message": "2333", "line": 2, "column": 105, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 111}, {"ruleId": "1957", "severity": 1, "message": "2334", "line": 2, "column": 113, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 121}, {"ruleId": "1957", "severity": 1, "message": "2563", "line": 2, "column": 123, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 140}, {"ruleId": "1957", "severity": 1, "message": "2229", "line": 2, "column": 142, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 158}, {"ruleId": "1957", "severity": 1, "message": "2564", "line": 2, "column": 160, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 166}, {"ruleId": "1957", "severity": 1, "message": "2531", "line": 3, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2301", "line": 4, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2567", "line": 4, "column": 26, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 49}, {"ruleId": "1957", "severity": 1, "message": "2568", "line": 4, "column": 51, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 71}, {"ruleId": "1957", "severity": 1, "message": "2569", "line": 4, "column": 73, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 91}, {"ruleId": "1957", "severity": 1, "message": "2570", "line": 5, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 31}, {"ruleId": "1957", "severity": 1, "message": "2571", "line": 7, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 7, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2572", "line": 8, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 8, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2573", "line": 9, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2574", "line": 10, "column": 7, "nodeType": "1959", "messageId": "1960", "endLine": 10, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "1997", "line": 11, "column": 7, "nodeType": "1959", "messageId": "1960", "endLine": 11, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2606", "line": 12, "column": 7, "nodeType": "1959", "messageId": "1960", "endLine": 12, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2864", "line": 19, "column": 7, "nodeType": "1959", "messageId": "1960", "endLine": 19, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2530", "line": 2, "column": 27, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 34}, {"ruleId": "1957", "severity": 1, "message": "2335", "line": 2, "column": 36, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 46}, {"ruleId": "1957", "severity": 1, "message": "2334", "line": 2, "column": 59, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 67}, {"ruleId": "1957", "severity": 1, "message": "2336", "line": 2, "column": 77, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 84}, {"ruleId": "1957", "severity": 1, "message": "2865", "line": 3, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2853", "line": 4, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2866", "line": 10, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 10, "endColumn": 29}, {"ruleId": "1957", "severity": 1, "message": "2867", "line": 11, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 11, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2868", "line": 23, "column": 16, "nodeType": "1959", "messageId": "1960", "endLine": 23, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2869", "line": 25, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 25, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2870", "line": 25, "column": 18, "nodeType": "1959", "messageId": "1960", "endLine": 25, "endColumn": 28}, {"ruleId": "1968", "severity": 1, "message": "2871", "line": 58, "column": 5, "nodeType": "1970", "endLine": 58, "endColumn": 18, "suggestions": "2872"}, {"ruleId": "1957", "severity": 1, "message": "2873", "line": 3, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2575", "line": 6, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 6, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2278", "line": 2, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2634", "line": 2, "column": 27, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 36}, {"ruleId": "1957", "severity": 1, "message": "2329", "line": 2, "column": 38, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 42}, {"ruleId": "1957", "severity": 1, "message": "2393", "line": 9, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2392", "line": 9, "column": 22, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 36}, {"ruleId": "1957", "severity": 1, "message": "2075", "line": 10, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 10, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2394", "line": 10, "column": 26, "nodeType": "1959", "messageId": "1960", "endLine": 10, "endColumn": 44}, {"ruleId": "1957", "severity": 1, "message": "2796", "line": 12, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 12, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2840", "line": 12, "column": 27, "nodeType": "1959", "messageId": "1960", "endLine": 12, "endColumn": 46}, {"ruleId": "1957", "severity": 1, "message": "2797", "line": 13, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 13, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "2798", "line": 13, "column": 14, "nodeType": "1959", "messageId": "1960", "endLine": 13, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2456", "line": 15, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 15, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2701", "line": 16, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 16, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2874", "line": 28, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 28, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2270", "line": 2, "column": 32, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 42}, {"ruleId": "1957", "severity": 1, "message": "2634", "line": 2, "column": 44, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 53}, {"ruleId": "1957", "severity": 1, "message": "2635", "line": 4, "column": 46, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 65}, {"ruleId": "1957", "severity": 1, "message": "1997", "line": 4, "column": 67, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 75}, {"ruleId": "1957", "severity": 1, "message": "2875", "line": 8, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 8, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2743", "line": 16, "column": 13, "nodeType": "1959", "messageId": "1960", "endLine": 16, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2876", "line": 31, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 31, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2406", "line": 33, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 33, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2877", "line": 43, "column": 6, "nodeType": "1959", "messageId": "1960", "endLine": 43, "endColumn": 34}, {"ruleId": "1957", "severity": 1, "message": "2878", "line": 85, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 85, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2656", "line": 95, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 95, "endColumn": 26}, {"ruleId": "1957", "severity": 1, "message": "2866", "line": 5, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 29}, {"ruleId": "1957", "severity": 1, "message": "2867", "line": 6, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 6, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2879", "line": 7, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 7, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2880", "line": 28, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 28, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2881", "line": 35, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 35, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2669", "line": 57, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 57, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2670", "line": 59, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 59, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2882", "line": 81, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 81, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2883", "line": 83, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 83, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2884", "line": 83, "column": 23, "nodeType": "1959", "messageId": "1960", "endLine": 83, "endColumn": 38}, {"ruleId": "1957", "severity": 1, "message": "2885", "line": 136, "column": 7, "nodeType": "1959", "messageId": "1960", "endLine": 136, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2886", "line": 293, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 293, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2887", "line": 335, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 335, "endColumn": 25}, {"ruleId": "1968", "severity": 1, "message": "2888", "line": 355, "column": 5, "nodeType": "1970", "endLine": 355, "endColumn": 22, "suggestions": "2889"}, {"ruleId": "1957", "severity": 1, "message": "2734", "line": 1, "column": 58, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 67}, {"ruleId": "1957", "severity": 1, "message": "2530", "line": 2, "column": 15, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2270", "line": 2, "column": 24, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 34}, {"ruleId": "1957", "severity": 1, "message": "2336", "line": 2, "column": 48, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 55}, {"ruleId": "1957", "severity": 1, "message": "2742", "line": 5, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "2226", "line": 6, "column": 41, "nodeType": "1959", "messageId": "1960", "endLine": 6, "endColumn": 53}, {"ruleId": "1957", "severity": 1, "message": "2740", "line": 7, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 7, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2235", "line": 7, "column": 16, "nodeType": "1959", "messageId": "1960", "endLine": 7, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2890", "line": 7, "column": 24, "nodeType": "1959", "messageId": "1960", "endLine": 7, "endColumn": 29}, {"ruleId": "1957", "severity": 1, "message": "2891", "line": 7, "column": 31, "nodeType": "1959", "messageId": "1960", "endLine": 7, "endColumn": 35}, {"ruleId": "1957", "severity": 1, "message": "2361", "line": 7, "column": 37, "nodeType": "1959", "messageId": "1960", "endLine": 7, "endColumn": 47}, {"ruleId": "1957", "severity": 1, "message": "2741", "line": 7, "column": 49, "nodeType": "1959", "messageId": "1960", "endLine": 7, "endColumn": 61}, {"ruleId": "1957", "severity": 1, "message": "2531", "line": 8, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 8, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2853", "line": 9, "column": 8, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2606", "line": 10, "column": 20, "nodeType": "1959", "messageId": "1960", "endLine": 10, "endColumn": 30}, {"ruleId": "1957", "severity": 1, "message": "2892", "line": 46, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 46, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2893", "line": 47, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 47, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2082", "line": 48, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 48, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2894", "line": 49, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 49, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2367", "line": 50, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 50, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2895", "line": 51, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 51, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2366", "line": 52, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 52, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2365", "line": 53, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 53, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2896", "line": 54, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 54, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "2364", "line": 56, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 56, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2897", "line": 57, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 57, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2485", "line": 61, "column": 4, "nodeType": "1959", "messageId": "1960", "endLine": 61, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "2660", "line": 75, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 75, "endColumn": 22}, {"ruleId": "1968", "severity": 1, "message": "2898", "line": 171, "column": 7, "nodeType": "1970", "endLine": 171, "endColumn": 27, "suggestions": "2899"}, {"ruleId": "1968", "severity": 1, "message": "2900", "line": 193, "column": 9, "nodeType": "2342", "endLine": 198, "endColumn": 4, "suggestions": "2901"}, {"ruleId": "1968", "severity": 1, "message": "2902", "line": 276, "column": 6, "nodeType": "1970", "endLine": 276, "endColumn": 19, "suggestions": "2903"}, {"ruleId": "1968", "severity": 1, "message": "2904", "line": 350, "column": 6, "nodeType": "1970", "endLine": 350, "endColumn": 17, "suggestions": "2905"}, {"ruleId": "1957", "severity": 1, "message": "2231", "line": 2, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2906", "line": 20, "column": 24, "nodeType": "1959", "messageId": "1960", "endLine": 20, "endColumn": 35}, {"ruleId": "1957", "severity": 1, "message": "2403", "line": 42, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 42, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2480", "line": 43, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 43, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2247", "line": 44, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 44, "endColumn": 28}, {"ruleId": "1957", "severity": 1, "message": "2907", "line": 46, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 46, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2358", "line": 48, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 48, "endColumn": 17}, {"ruleId": "1957", "severity": 1, "message": "2406", "line": 49, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 49, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2388", "line": 61, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 61, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2389", "line": 61, "column": 45, "nodeType": "1959", "messageId": "1960", "endLine": 61, "endColumn": 62}, {"ruleId": "1957", "severity": 1, "message": "2908", "line": 101, "column": 11, "nodeType": "1959", "messageId": "1960", "endLine": 101, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2909", "line": 104, "column": 12, "nodeType": "1959", "messageId": "1960", "endLine": 104, "endColumn": 26}, {"ruleId": "1968", "severity": 1, "message": "2556", "line": 129, "column": 5, "nodeType": "1970", "endLine": 129, "endColumn": 155, "suggestions": "2910"}, {"ruleId": "1957", "severity": 1, "message": "2911", "line": 22, "column": 7, "nodeType": "1959", "messageId": "1960", "endLine": 22, "endColumn": 23}, {"ruleId": "1957", "severity": 1, "message": "2912", "line": 31, "column": 7, "nodeType": "1959", "messageId": "1960", "endLine": 31, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2913", "line": 40, "column": 7, "nodeType": "1959", "messageId": "1960", "endLine": 40, "endColumn": 25}, {"ruleId": "1957", "severity": 1, "message": "2914", "line": 5, "column": 3, "nodeType": "1959", "messageId": "1960", "endLine": 5, "endColumn": 24}, {"ruleId": "1968", "severity": 1, "message": "2915", "line": 121, "column": 6, "nodeType": "1970", "endLine": 121, "endColumn": 26, "suggestions": "2916"}, {"ruleId": "1957", "severity": 1, "message": "2917", "line": 39, "column": 9, "nodeType": "1959", "messageId": "1960", "endLine": 39, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2269", "line": 1, "column": 17, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 27}, {"ruleId": "1957", "severity": 1, "message": "2526", "line": 1, "column": 28, "nodeType": "1959", "messageId": "1960", "endLine": 1, "endColumn": 36}, {"ruleId": "1957", "severity": 1, "message": "1977", "line": 2, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 22}, {"ruleId": "1957", "severity": 1, "message": "2273", "line": 4, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 4, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2779", "line": 6, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 6, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "2780", "line": 7, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 7, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "1999", "line": 8, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 8, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2918", "line": 9, "column": 2, "nodeType": "1959", "messageId": "1960", "endLine": 9, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2278", "line": 11, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 11, "endColumn": 8}, {"ruleId": "1957", "severity": 1, "message": "2330", "line": 13, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 13, "endColumn": 14}, {"ruleId": "1957", "severity": 1, "message": "2233", "line": 15, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 15, "endColumn": 16}, {"ruleId": "1957", "severity": 1, "message": "2234", "line": 16, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 16, "endColumn": 18}, {"ruleId": "1957", "severity": 1, "message": "2332", "line": 18, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 18, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2333", "line": 19, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 19, "endColumn": 11}, {"ruleId": "1957", "severity": 1, "message": "2334", "line": 20, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 20, "endColumn": 13}, {"ruleId": "1957", "severity": 1, "message": "2335", "line": 21, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 21, "endColumn": 15}, {"ruleId": "1957", "severity": 1, "message": "2336", "line": 22, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 22, "endColumn": 12}, {"ruleId": "1957", "severity": 1, "message": "2337", "line": 23, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 23, "endColumn": 10}, {"ruleId": "1957", "severity": 1, "message": "2338", "line": 24, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 24, "endColumn": 9}, {"ruleId": "1957", "severity": 1, "message": "2919", "line": 25, "column": 5, "nodeType": "1959", "messageId": "1960", "endLine": 25, "endColumn": 21}, {"ruleId": "1957", "severity": 1, "message": "2920", "line": 2, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 2, "endColumn": 19}, {"ruleId": "1957", "severity": 1, "message": "2270", "line": 3, "column": 14, "nodeType": "1959", "messageId": "1960", "endLine": 3, "endColumn": 24}, {"ruleId": "1957", "severity": 1, "message": "2921", "line": 6, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 6, "endColumn": 20}, {"ruleId": "1957", "severity": 1, "message": "2922", "line": 7, "column": 10, "nodeType": "1959", "messageId": "1960", "endLine": 7, "endColumn": 19}, {"ruleId": "1968", "severity": 1, "message": "2923", "line": 124, "column": 6, "nodeType": "1970", "endLine": 124, "endColumn": 27, "suggestions": "2924"}, "@typescript-eslint/no-unused-vars", "'GuidePopup' is defined but never used.", "Identifier", "unusedVar", "'Rte' is defined but never used.", "'isAppReady' is assigned a value but never used.", "'LoginUserInfo' is defined but never used.", "'UserManager' is defined but never used.", "'useNavigate' is defined but never used.", "'useLocation' is defined but never used.", "'redirectPath' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loggedOut'. Either include it or remove the dependency array.", "ArrayExpression", ["2925"], "'signIn' is assigned a value but never used.", "'useMemo' is defined but never used.", "'Steps' is defined but never used.", "'PopupList' is defined but never used.", "'BUTTON_DEFAULT_VALUE' is defined but never used.", "'stopScraping' is defined but never used.", "'addicon' is defined but never used.", "'touricon' is defined but never used.", "'ProductToursicon' is defined but never used.", "'Tooltipsicon' is defined but never used.", "'announcementicon' is defined but never used.", "'Bannersicon' is defined but never used.", "'Checklisticon' is defined but never used.", "'Hotspoticon' is defined but never used.", "'Surveyicon' is defined but never used.", "'Announcementsicon' is defined but never used.", "'bannersicon' is defined but never used.", "'tooltipicon' is defined but never used.", "'checklisticon' is defined but never used.", "'hotspotsicon' is defined but never used.", "'surveysicon' is defined but never used.", "'settingsicon' is defined but never used.", "'undoicon' is defined but never used.", "'redoicon' is defined but never used.", "'shareicon' is defined but never used.", "'editicon' is defined but never used.", "'Outlet' is defined but never used.", "'InputAdornment' is defined but never used.", "'FormHelperText' is defined but never used.", "'List' is defined but never used.", "'Step' is defined but never used.", "'guideSetting' is defined but never used.", "'JSEncrypt' is defined but never used.", "'GetUserDetailsById' is defined but never used.", "'UserLogin' is defined but never used.", "'VisibilityOff' is defined but never used.", "'Visibility' is defined but never used.", "'initialsData' is defined but never used.", "'EditIcon' is defined but never used.", "'TooltipUserview' is defined but never used.", "'SubmitUpdateGuid' is defined but never used.", "'PageInteractions' is defined but never used.", "'ElementsSettings' is defined but never used.", "'DrawerState' is defined but never used.", "'Checklist' is defined but never used.", "'Padding' is defined but never used.", "'CheckIcon' is defined but never used.", "'TooltipPreview' is defined but never used.", "'defaultAnnouncementImages' is defined but never used.", "'DismissData' is defined but never used.", "'Canvas' is defined but never used.", "'Design' is defined but never used.", "'Advanced' is defined but never used.", "'Hotspot' is defined but never used.", "'stepId' is defined but never used.", "'userId' is defined but never used.", "'loginUserData' is defined but never used.", "'setIsGuidesListOpen' is assigned a value but never used.", "'setIsInHomeScreen' is assigned a value but never used.", "'setIsAnnouncementListOpen' is assigned a value but never used.", "'setIsBannerslistOpen' is assigned a value but never used.", "'selectedTemplated' is assigned a value but never used.", "'setSelectedTemplated' is assigned a value but never used.", "'errorInStepName' is assigned a value but never used.", "'showTextField' is assigned a value but never used.", "'signOut' is assigned a value but never used.", "'selectedElement' is assigned a value but never used.", "'setSelectedElement' is assigned a value but never used.", "'showPassword' is assigned a value but never used.", "'setShowPassword' is assigned a value but never used.", "'password' is assigned a value but never used.", "'setPassword' is assigned a value but never used.", "'loginUserInfo' is assigned a value but never used.", "'setLoginUserInfo' is assigned a value but never used.", "'setresponse' is assigned a value but never used.", "'isTooltipPopupOpen' is assigned a value but never used.", "'setIsTooltipPopupOpen' is assigned a value but never used.", "'email' is assigned a value but never used.", "'setEmail' is assigned a value but never used.", "'loginUserDetails' is assigned a value but never used.", "'setUserDetails' is assigned a value but never used.", "'error' is assigned a value but never used.", "'setError' is assigned a value but never used.", "'isSelectingElement' is assigned a value but never used.", "'selectedElementDetails' is assigned a value but never used.", "'setSelectedElementDetails' is assigned a value but never used.", "'position' is assigned a value but never used.", "'setPosition' is assigned a value but never used.", "'radius' is assigned a value but never used.", "'setRadius' is assigned a value but never used.", "'borderSize' is assigned a value but never used.", "'setBorderSize' is assigned a value but never used.", "'announcementData' is assigned a value but never used.", "'currentUrl' is assigned a value but never used.", "'isBannerPopupOpen' is assigned a value but never used.", "'i18nInitialized' is assigned a value but never used.", "'setI18nInitialized' is assigned a value but never used.", "'hashValue' is assigned a value but never used.", "'setHashValue' is assigned a value but never used.", "React Hook useEffect has an unnecessary dependency: 'window.location.href'. Either exclude it or remove the dependency array. Outer scope values like 'window.location.href' aren't valid dependencies because mutating them doesn't re-render the component.", ["2926"], "'fit' is assigned a value but never used.", "'fill' is assigned a value but never used.", "'backgroundColor' is assigned a value but never used.", "'sectionHeight' is assigned a value but never used.", "'setSectionHeight' is assigned a value but never used.", "'guidedatas' is assigned a value but never used.", "'setGuideDataS' is assigned a value but never used.", "'hotspotPopup' is assigned a value but never used.", "'setHotspotPopup' is assigned a value but never used.", "'textvaluess' is assigned a value but never used.", "'preview' is assigned a value but never used.", "'btnBorderColor' is assigned a value but never used.", "'btnBgColor' is assigned a value but never used.", "'btnTextColor' is assigned a value but never used.", "'isTooltipPopup' is assigned a value but never used.", "'setSteps' is assigned a value but never used.", "'newCurrentStep' is assigned a value but never used.", "'updateCanvasInTooltip' is assigned a value but never used.", "'hotspbgcolor' is assigned a value but never used.", "'setHotspBgColor' is assigned a value but never used.", "'setHotspotDataOnEdit' is assigned a value but never used.", "'openTooltip' is assigned a value but never used.", "'setXpathToTooltipMetaData' is assigned a value but never used.", "'setAxisData' is assigned a value but never used.", "'axisData' is assigned a value but never used.", "'setAutoPosition' is assigned a value but never used.", "'targetURL' is assigned a value but never used.", "'elementButtonName' is assigned a value but never used.", "'setElementButtonName' is assigned a value but never used.", "'isSaveClicked' is assigned a value but never used.", "'setbtnidss' is assigned a value but never used.", "'pulseAnimationsH' is assigned a value but never used.", "'setPulseAnimationsH' is assigned a value but never used.", "'tooltipCount' is assigned a value but never used.", "'HotspotGuideDetails' is assigned a value but never used.", "'TooltipGuideDetailsNew' is assigned a value but never used.", "'editClicked' is assigned a value but never used.", "'textArray' is assigned a value but never used.", "'setTextArray' is assigned a value but never used.", "'setDrawerActiveMenu' is assigned a value but never used.", "'setDrawerSearchText' is assigned a value but never used.", "'setInteractionData' is assigned a value but never used.", "'syncAIAnnouncementCanvasSettings' is assigned a value but never used.", "'ele4' is assigned a value but never used.", "'targetElement' is assigned a value but never used.", "'setHotspotClicked' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "React Hook useEffect has missing dependencies: 'fetchGuideDetails' and 'hotspot'. Either include them or remove the dependency array.", ["2927"], "React Hook useEffect has a missing dependency: 'setDesignPopup'. Either include it or remove the dependency array.", ["2928"], "'screenWidth' is assigned a value but never used.", "'dialogWidth' is assigned a value but never used.", "'handlechangeStep' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'determineCurrentScreen'. Either include it or remove the dependency array.", ["2929"], "React Hook useEffect has a missing dependency: 'errors'. Either include it or remove the dependency array. Outer scope values like 'selectedStepType' aren't valid dependencies because mutating them doesn't re-render the component.", ["2930"], "React Hook useEffect has a missing dependency: 'setIsAIGuidePersisted'. Either include it or remove the dependency array.", ["2931"], "React Hook useEffect has missing dependencies: 'clearGuideDetails' and 'resetTooltipMetaData'. Either include them or remove the dependency array.", ["2932"], "React Hook useEffect has missing dependencies: 'bannerPopup', 'clearBannerButtonDetials', 'currentGuideId', 'currentStep', 'selectedTemplate', 'selectedTemplateTour', 'setBannerButtonSelected', 'setBannerPopup', 'setIsAnnouncementCreationBuilderOpen', 'setIsBannerCreationBuilderOpen', 'setIsHotspotCreationBuilderOpen', 'setIsTooltipCreationBuilderOpen', 'updateButtonContainerOnReload', and 'updateRTEContainerOnReload'. Either include them or remove the dependency array.", ["2933"], "'setCount' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "'handleGuidesSettingsclick' is assigned a value but never used.", "'synchronizePreviewData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'elementSelected', 'resetALTKeywordForNewTooltip', 'setElementSelected', 'setIsALTKeywordEnabled', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2934"], "'handleElementSelectionToggle' is assigned a value but never used.", "'userInfoObj' is assigned a value but never used.", "'isAnnouncementOpen' is assigned a value but never used.", "'setAnnouncementOpen' is assigned a value but never used.", "'aiCreationComplete' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setSettingAnchorEl'. Either include it or remove the dependency array.", ["2935"], "'defaultButtonSection' is assigned a value but never used.", "'responseData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setIsUnSavedChanges' and 'stepCreation'. Either include them or remove the dependency array.", ["2936"], "'handleNewInteractionClick' is assigned a value but never used.", "Assignments to the 'accountId' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "'handleEditClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'userType'. Either include it or remove the dependency array.", ["2937"], "'editstepNameClicked' is assigned a value but never used.", "'setEditStepNameClicked' is assigned a value but never used.", "'handleNextClick' is assigned a value but never used.", "'isValid' is assigned a value but never used.", "'handleEventChange' is assigned a value but never used.", "'isGuideNameUnique' is assigned a value but never used.", "React Hook useEffect has an unnecessary dependency: 'updatedGuideData.GuideStep'. Either exclude it or remove the dependency array. Outer scope values like 'updatedGuideData.GuideStep' aren't valid dependencies because mutating them doesn't re-render the component.", ["2938"], "'getAlignment' is defined but never used.", "'popupVisible' is assigned a value but never used.", "'triggerType' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'currentGuide?.GuideStep'. Either include it or remove the dependency array.", ["2939"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "ChainExpression", "'customButton' is assigned a value but never used.", "'groupedButtons' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'cleanupDuplicateSteps', 'createWithAI', 'currentGuideId', 'interactionData', and 'resetHeightofBanner'. Either include them or remove the dependency array.", ["2940"], "'isDisabled' is assigned a value but never used.", "'guideType' is assigned a value but never used.", "'guideSteps' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'SetGuideName', 'cleanupDuplicateSteps', 'createWithAI', 'currentStep', 'getGuideTypeValue', 'handlecreatefromScratchclick', 'overlayEnabled', 'pageinteraction', 'progress', 'resetHeightofBanner', 'setBannerPopup', 'setBposition', 'setDismiss', 'setIsGuideInfoScreen', 'setOverlayEnabled', 'setPageInteraction', 'setProgress', 'setProgressColor', 'setSelectedOption', 'setSelectedTemplate', 'setSelectedTemplateTour', 'setTooltipCount', and 'setTourDataOnEdit'. Either include them or remove the dependency array.", ["2941"], "React Hook useEffect has missing dependencies: 'SetGuideName', 'currentGuideId', 'currentStep', 'getGuideTypeValue', 'handlecreatefromScratchclick', 'selectedTemplate', 'setIsGuideInfoScreen', 'setSelectedTemplate', and 'steps'. Either include them or remove the dependency array.", ["2942"], "React Hook useEffect has missing dependencies: 'setBannerPopup', 'setCreateWithAI', 'setCurrentGuideId', 'setIsAIGuidePersisted', 'setIsAnnouncementCreationBuilderOpen', 'setIsBannerCreationBuilderOpen', 'setIsGuideInfoScreen', 'setIsHomeScreen', 'setIsHotspotCreationBuilderOpen', 'setIsTemplateScreen', 'setIsTooltipCreationBuilderOpen', and 'setIsTooltipPopup'. Either include them or remove the dependency array.", ["2943"], "React Hook useEffect has an unnecessary dependency: 'updatedGuideData'. Either exclude it or remove the dependency array. Outer scope values like 'updatedGuideData' aren't valid dependencies because mutating them doesn't re-render the component.", ["2944"], "'getAccountIdForUpdate' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'deleteClicked', 'handleStepChange', 'playIconClicked', and 'updateStepClicked'. Either include them or remove the dependency array.", ["2945"], "'handleStartAgentTraining' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\/.", "Literal", "unnecessaryEscape", ["2946", "2947"], "'selectedStepTitle' is assigned a value but never used.", "'accountId' is assigned a value but never used.", "'CelebrationOutlinedIcon' is defined but never used.", "'ErrorOutlineOutlinedIcon' is defined but never used.", "'Button' is defined but never used.", "'Routes' is defined but never used.", "'RouteSharp' is defined but never used.", "'extractStateForHistory' is defined but never used.", "no-dupe-keys", "Duplicate key 'hotspotXaxis'.", "ObjectExpression", "Duplicate key 'setHotspotXaxis'.", "Duplicate key 'setSelectedTemplate'.", "no-self-assign", "'state.overlayEnabled' is assigned to itself.", "MemberExpression", "selfAssignment", "Duplicate key 'toolTipGuideMetaData'.", "'isTourBanner' is assigned a value but never used.", "'announcementGuideStep' is assigned a value but never used.", "Duplicate key 'announcementGuideMetaData'.", "'opt' is assigned a value but never used.", "'targetStep' is assigned a value but never used.", "'future' is assigned a value but never used.", "'FALSE' is defined but never used.", "'TSectionType' is defined but never used.", "'RadioGroup' is defined but never used.", "'Radio' is defined but never used.", "'FormControlLabel' is defined but never used.", "'Input' is defined but never used.", "'Autocomplete' is defined but never used.", "'CircularProgress' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogActions' is defined but never used.", "'GifBox' is defined but never used.", "'Opacity' is defined but never used.", "'WarningIcon' is defined but never used.", "'color' is defined but never used.", "'dismissData' is assigned a value but never used.", "'setSelectActions' is assigned a value but never used.", "'setSelectedInteraction' is assigned a value but never used.", "'openInteractionList' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'currentStepIndex' is assigned a value but never used.", "'setProgress' is assigned a value but never used.", "'selectedTemplate' is assigned a value but never used.", "'updateTooltipButtonAction' is assigned a value but never used.", "'updateTooltipButtonInteraction' is assigned a value but never used.", "'selectedTemplateTour' is assigned a value but never used.", "'setProgressColor' is assigned a value but never used.", "'createWithAI' is assigned a value but never used.", "'action' is assigned a value but never used.", "'designPopup' is assigned a value but never used.", "'buttonId' is assigned a value but never used.", "'setButtonId' is assigned a value but never used.", "'cuntainerId' is assigned a value but never used.", "'setCuntainerId' is assigned a value but never used.", "'btnname' is assigned a value but never used.", "'handleCloseInteraction' is assigned a value but never used.", "'handleOpenInteraction' is assigned a value but never used.", "'sideAddButtonStyle' is assigned a value but never used.", "React Hook useMemo has missing dependencies: 'getCurrentButtonInfo', 'setBtnName', and 'setCurrentButtonName'. Either include them or remove the dependency array.", ["2948"], "'selectedButton' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', 'defaultButtonColors.color', 'getCurrentButtonInfo', 'setTargetURL', and 'targetURL'. Either include them or remove the dependency array.", ["2949"], "React Hook useEffect has missing dependencies: 'selectedActions.value' and 'targetURL'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setSelectedActions' needs the current value of 'selectedActions.value'.", ["2950"], "'useContext' is defined but never used.", "'Typography' is defined but never used.", "'AuthProvider' is defined but never used.", "'useAuth' is defined but never used.", "'AccountContext' is defined but never used.", "'clearAccessToken' is assigned a value but never used.", "'userLocalData' is assigned a value but never used.", "'SAinitialsData' is assigned a value but never used.", "'userDetails' is defined but never used.", "'Box' is defined but never used.", "'ai' is defined but never used.", "'EnableAIButton' is defined but never used.", "'IsOpenAIKeyEnabledForAccount' is defined but never used.", "'setSelectedTemplate' is assigned a value but never used.", "'setSelectedTemplateTour' is assigned a value but never used.", "'steps' is assigned a value but never used.", "'setTooltipCount' is assigned a value but never used.", "'SetGuideName' is assigned a value but never used.", "'setIsTooltipPopup' is assigned a value but never used.", "'setBannerPopup' is assigned a value but never used.", "'setElementSelected' is assigned a value but never used.", "'TooltipGuideDetails' is assigned a value but never used.", "'HotspotGuideDetailsNew' is assigned a value but never used.", "'setSelectedStepTypeHotspot' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'isExtensionClosed' and 'setIsExtensionClosed'. Either include them or remove the dependency array.", ["2951"], "React Hook useEffect has missing dependencies: 'setDrawerActiveMenu', 'setDrawerSearchText', and 'setHasAnnouncementOpened'. Either include them or remove the dependency array.", ["2952"], "React Hook useEffect has missing dependencies: 'setDrawerActiveMenu', 'setDrawerSearchText', and 'setIsPopupOpen'. Either include them or remove the dependency array. If 'setIsPopupOpen' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2953"], "'handleEnableAI' is assigned a value but never used.", "'userApiService' is defined but never used.", "'useDrawerStore' is defined but never used.", "'constants' is defined but never used.", "'addPersistentHighlight' is assigned a value but never used.", "'showClickFeedback' is assigned a value but never used.", ["2954", "2955"], "Unnecessary escape character: \\..", ["2956", "2957"], ["2958", "2959"], ["2960", "2961"], ["2962", "2963"], ["2964", "2965"], ["2966", "2967"], ["2968", "2969"], "'response' is assigned a value but never used.", "'errorMessage' is assigned a value but never used.", "'axios' is defined but never used.", "'AnyMxRecord' is defined but never used.", "'UndoIcon' is defined but never used.", "'RedoIcon' is defined but never used.", "'canUndoValue' is assigned a value but never used.", "'canRedoValue' is assigned a value but never used.", "'ChecklistPopup' is defined but never used.", "'closeicon' is defined but never used.", "'closepluginicon' is defined but never used.", "'setShowLauncherSettings' is assigned a value but never used.", "'showLauncherSettings' is assigned a value but never used.", "'setIcons' is assigned a value but never used.", "'checklistColor' is assigned a value but never used.", "'Grid' is defined but never used.", "'Container' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'IconButton' is defined but never used.", "'Tooltip' is defined but never used.", "'Alert' is defined but never used.", "'Chip' is defined but never used.", "'GetGudeDetailsByGuideId' is defined but never used.", "'initialCompletedStatus' is assigned a value but never used.", "The 'checkpointslistData' logical expression could make the dependencies of useEffect Hook (at line 143) change on every render. To fix this, wrap the initialization of 'checkpointslistData' in its own useMemo() Hook.", "VariableDeclarator", "The 'checkpointslistData' logical expression could make the dependencies of useEffect Hook (at line 207) change on every render. To fix this, wrap the initialization of 'checkpointslistData' in its own useMemo() Hook.", "'checklistItems' is assigned a value but never used.", "'setChecklistItems' is assigned a value but never used.", "no-lone-blocks", "Nested block is redundant.", "BlockStatement", "redundantNestedBlock", "React Hook useEffect has a missing dependency: 'checkpointslistData'. Either include it or remove the dependency array.", ["2970"], "'iconColor' is assigned a value but never used.", "'base64IconFinal' is assigned a value but never used.", "'handleNavigate' is assigned a value but never used.", "'anchorEl' is assigned a value but never used.", "'setAnchorEl' is assigned a value but never used.", "'currentStep' is assigned a value but never used.", "'setCurrentStep' is assigned a value but never used.", "'scrollPercentage' is assigned a value but never used.", "'setScrollPercentage' is assigned a value but never used.", "'TextFormat' is defined but never used.", "'BUTTON_CONT_DEF_VALUE' is defined but never used.", "'saveGuide' is defined but never used.", "'setSectionColor' is assigned a value but never used.", "'BborderSize' is assigned a value but never used.", "'Bbordercolor' is assigned a value but never used.", "'backgroundC' is assigned a value but never used.", "'setPreview' is assigned a value but never used.", "'clearGuideDetails' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'bannerButtonSelected', 'buttonColor', 'rtesContainer', 'setButtonColor', 'textAreas', and 'textBoxRef'. Either include them or remove the dependency array.", ["2971"], "React Hook useEffect has missing dependencies: 'buttonColor', 'removeTextArea', 'setButtonColor', and 'textAreas'. Either include them or remove the dependency array.", ["2972"], "React Hook useEffect has a missing dependency: 'setTextArray'. Either include it or remove the dependency array.", ["2973"], "React Hook useEffect has a missing dependency: 'textAreas'. Either include it or remove the dependency array.", ["2974"], "'overlayEnabled' is assigned a value but never used.", "'setShowEmojiPicker' is assigned a value but never used.", "'enableProgress' is assigned a value but never used.", "'ViewModuleIcon' is defined but never used.", "'CodeIcon' is defined but never used.", "'TouchAppSharp' is defined but never used.", "'reselectElement' is assigned a value but never used.", "'setReselectElement' is assigned a value but never used.", "'goToNextElement' is assigned a value but never used.", "'setGoToNextElement' is assigned a value but never used.", "'setCurrentGuideId' is assigned a value but never used.", "'getCurrentGuideId' is assigned a value but never used.", "'padding' is assigned a value but never used.", "'setPadding' is assigned a value but never used.", "'setBorderColor' is assigned a value but never used.", "'borderColor' is assigned a value but never used.", "'setBackgroundColor' is assigned a value but never used.", "'setZiindex' is assigned a value but never used.", "'setguidesSettingspopup' is assigned a value but never used.", "'setTooltipBackgroundcolor' is assigned a value but never used.", "'setTooltipBordercolor' is assigned a value but never used.", "'setTooltipBordersize' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE' is assigned a value but never used.", "'savedGuideData' is assigned a value but never used.", "'ButtonsDropdown' is assigned a value but never used.", "'setButtonsDropdown' is assigned a value but never used.", "'elementSelected' is assigned a value but never used.", "'elementbuttonClick' is assigned a value but never used.", "'highlightedButton' is assigned a value but never used.", "'mapButtonSection' is assigned a value but never used.", "'progress' is assigned a value but never used.", "'setSelectedOption' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setHotspotPopup', 'setShowLauncherSettings', 'setShowTooltipCanvasSettings', and 'setTitlePopup'. Either include them or remove the dependency array.", ["2975"], "'toggleReselectElement' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'SetElementButtonClick', 'createWithAI', 'currentGuideId', 'interactionData', 'setButtonClick', 'setDropdownValue', 'setElementButtonName', 'setElementClick', and 'setbtnidss'. Either include them or remove the dependency array.", ["2976"], "'existingHotspot' is assigned a value but never used.", "'existingTooltip' is assigned a value but never used.", "'toggleCustomCSS' is assigned a value but never used.", "'toggleAnimation' is assigned a value but never used.", "'handleDismissDataChange' is assigned a value but never used.", "'setTooltipXaxis' is defined but never used.", "'setTooltipYaxis' is defined but never used.", "'setTooltipPosition' is defined but never used.", "'setTooltipBorderradius' is defined but never used.", "'setTooltipPadding' is defined but never used.", "'setTooltipWidth' is defined but never used.", "'updateCanvasInTooltip' is defined but never used.", "'setElementSelected' is defined but never used.", "'CustomIconButton' is defined but never used.", "'ArrowBackIosNewOutlinedIcon' is defined but never used.", "'parse' is defined but never used.", "'domToReact' is defined but never used.", "'Element' is defined but never used.", "'IconButtonSX' is defined but never used.", "'setShowBanner' is assigned a value but never used.", "'setImageSrc' is assigned a value but never used.", "'htmlContent' is assigned a value but never used.", "'Teext' is assigned a value but never used.", "'IconColor' is assigned a value but never used.", "'IconOpacity' is assigned a value but never used.", "'Width' is assigned a value but never used.", "'Radius' is assigned a value but never used.", "'Design' is assigned a value but never used.", "'brCount' is assigned a value but never used.", "'BannerEndUser' is defined but never used.", "'BannerStepPreview' is defined but never used.", "'setBannerPreview' is assigned a value but never used.", "'bannerPreview' is assigned a value but never used.", "'announcementPreview' is assigned a value but never used.", "'setAnnouncementPreview' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'OverlayValue'. Either include it or remove the dependency array. If 'setOverlayValue' needs the current value of 'OverlayValue', you can also switch to useReducer instead of useState and read 'OverlayValue' in the reducer.", ["2977"], "'imageStyle' is assigned a value but never used.", "'dissmissIconColor' is assigned a value but never used.", "'ActionButtonBackgroundcolor' is assigned a value but never used.", "'overlay' is assigned a value but never used.", "'openInNewTab' is assigned a value but never used.", "'HotspotGuideProps' is defined but never used.", "'hotspotGuideMetaData' is assigned a value but never used.", "valid-typeof", "Invalid typeof comparison value.", "invalidV<PERSON>ue", ["2978"], "React Hook useEffect has a missing dependency: 'getElementPosition'. Either include it or remove the dependency array.", ["2979"], "React Hook useEffect has a missing dependency: 'xpath'. Either include it or remove the dependency array.", ["2980"], "React Hook useEffect has a missing dependency: 'calculateOptimalWidth'. Either include it or remove the dependency array.", ["2981"], "React Hook useEffect has a missing dependency: 'guideStep'. Either include it or remove the dependency array.", ["2982"], "React Hook useEffect has missing dependencies: 'currentStep', 'selectedTemplateTour', and 'setOpenTooltip'. Either include them or remove the dependency array.", ["2983"], ["2984"], "Assignments to the 'hotspot' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "'useRef' is defined but never used.", "'ClickAwayListener' is defined but never used.", "'LinearProgress' is defined but never used.", "'Breadcrumbs' is defined but never used.", "'updateCacheWithNewRows' is defined but never used.", "'toolTipGuideMetaData' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", "'tooltip' is assigned a value but never used.", "'guideName' is assigned a value but never used.", "'borderRadius' is assigned a value but never used.", "'width' is assigned a value but never used.", "'tooltipXaxis' is assigned a value but never used.", "'tooltipYaxis' is assigned a value but never used.", "'tooltipWidth' is assigned a value but never used.", "'setTooltipWidth' is assigned a value but never used.", "'setTooltipPadding' is assigned a value but never used.", "'setTooltipBorderradius' is assigned a value but never used.", "'tooltipbordersize' is assigned a value but never used.", "'tooltipPosition' is assigned a value but never used.", "'setTooltipPosition' is assigned a value but never used.", "'selectedOption' is assigned a value but never used.", "'setCurrentStepIndex' is assigned a value but never used.", "'HotspotSettings' is assigned a value but never used.", "'currentGuideId' is assigned a value but never used.", "'setIsALTKeywordEnabled' is assigned a value but never used.", "'hoveredElement' is assigned a value but never used.", "'setHoveredElement' is assigned a value but never used.", "'overlayPosition' is assigned a value but never used.", "'setOverlayPosition' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentElement'.", "ArrowFunctionExpression", "unsafeRefs", "'removeAppliedStyleOfEle' is assigned a value but never used.", "'isElementHover' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'applyCustomCursor', 'applyHotspotProperties', 'createWithAI', 'isCollapsed', 'isGuideInfoScreen', 'isTooltipNameScreenOpen', 'rectData', 'selectedTemplate', 'selectedTemplateTour', 'setAxisData', 'setCurrentHoveredElement', 'setElementSelected', 'setOpenTooltip', 'setTooltip', 'setXpathToTooltipMetaData', and 'syncAITooltipContainerData'. Either include them or remove the dependency array.", ["2985"], "React Hook useEffect has a missing dependency: 'applyCustomCursor'. Either include it or remove the dependency array.", ["2986"], "'normalizePx' is assigned a value but never used.", "'DotsStepper' is assigned a value but never used.", "'useEffect' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'count' is assigned a value but never used.", "'selectedStepStyle' is assigned a value but never used.", "'isSelected' is assigned a value but never used.", "'isHovered' is assigned a value but never used.", "'useState' is defined but never used.", "'ForkLeft' is defined but never used.", "'editType' is assigned a value but never used.", "'handleStepTypeChange' is assigned a value but never used.", "'Popover' is defined but never used.", "'CloseIcon' is defined but never used.", "'PopoverOrigin' is defined but never used.", "React Hook useEffect has missing dependencies: 'initializeTourHotspotMetadata', 'savedGuideData?.GuideStep', 'setAnnouncementPreview', 'setBannerPreview', 'setHotspotPreview', 'setOpenTooltip', and 'setTooltipPreview'. Either include them or remove the dependency array.", ["2987"], "'setCurrentUrl' is assigned a value but never used.", "Assignments to the 'savedGuideData' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "CallExpression", "'AnySoaRecord' is defined but never used.", "'userUrl' is assigned a value but never used.", "'adminApiService' is defined but never used.", "'idsApiService' is defined but never used.", "'ArrowBackIosIcon' is defined but never used.", "'isUnSavedChanges' is assigned a value but never used.", "'openWarning' is assigned a value but never used.", "'setActiveMenu' is assigned a value but never used.", "'setSearchText' is assigned a value but never used.", "'setName' is assigned a value but never used.", "'handleKeyDown' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAnnouncements'. Either include it or remove the dependency array.", ["2988"], ["2989"], "'AxiosResponse' is defined but never used.", "'LauncherSettings' is defined but never used.", "React Hook useEffect has missing dependencies: 'checkpointslistData' and 'completedStatus'. Either include them or remove the dependency array.", ["2990"], "React Hook useEffect has missing dependencies: 'createWithAI' and 'interactionData'. Either include them or remove the dependency array.", ["2991"], ["2992"], "React Hook useEffect has a missing dependency: 'checklistGuideMetaData'. Either include it or remove the dependency array.", ["2993"], "'toggleItemCompletion' is assigned a value but never used.", "'useReducer' is defined but never used.", "'SelectChangeEvent' is defined but never used.", "'Switch' is defined but never used.", "'ToggleButton' is defined but never used.", "'ToggleButtonGroup' is defined but never used.", "'BUTTON_CONT_DEF_VALUE_1' is defined but never used.", "'CANVAS_DEFAULT_VALUE' is defined but never used.", "'IMG_CONT_DEF_VALUE' is defined but never used.", "'HOTSPOT_DEFAULT_VALUE' is defined but never used.", "'InfoFilled' is defined but never used.", "'QuestionFill' is defined but never used.", "'Reselect' is defined but never used.", "'Solid' is defined but never used.", "'AddCircleOutlineIcon' is defined but never used.", "'InsertPhotoIcon' is defined but never used.", "'PersonIcon' is defined but never used.", "'FavoriteIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'ErrorOutlineIcon' is defined but never used.", "'position' is defined but never used.", "'titlePopup' is assigned a value but never used.", "'setTitlePopup' is assigned a value but never used.", "'titleColor' is assigned a value but never used.", "'launcherColor' is assigned a value but never used.", "'hasChanges' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistGuideMetaData', 'checklistLauncherProperties', and 'icons'. Either include them or remove the dependency array.", ["2994"], "'handleTitleColorChange' is assigned a value but never used.", "'handledesignclose' is assigned a value but never used.", "'handleSizeChange' is assigned a value but never used.", "'onReselectElement' is assigned a value but never used.", "'handleIconColorChange' is assigned a value but never used.", "'handleLauncherColorChange' is assigned a value but never used.", "'type' is assigned a value but never used.", "'text' is assigned a value but never used.", "'setText' is assigned a value but never used.", "'textColor' is assigned a value but never used.", "'setTextColor' is assigned a value but never used.", "'icon' is assigned a value but never used.", "'appliedIconColorBase64Icon' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistLauncherProperties', 'icons', and 'updateChecklistLauncher'. Either include them or remove the dependency array.", ["2995"], "'setPositionLeft' is assigned a value but never used.", "'setSetPositionLeft' is assigned a value but never used.", "'deleteicon' is defined but never used.", "'deletestep' is defined but never used.", "'editpricol' is defined but never used.", "'getAllGuides' is defined but never used.", "'ShowLauncherSettings' is assigned a value but never used.", "'setTitleColor' is assigned a value but never used.", "'checkpointsPopup' is assigned a value but never used.", "'checkpointTitleColor' is assigned a value but never used.", "'setCheckpointTitleColor' is assigned a value but never used.", "'checkpointTitleDescription' is assigned a value but never used.", "'setCheckpointTitleDescription' is assigned a value but never used.", "'checkpointIconColor' is assigned a value but never used.", "'setCheckpointIconColor' is assigned a value but never used.", "'setUnlockCheckPointInOrder' is assigned a value but never used.", "'unlockCheckPointInOrder' is assigned a value but never used.", "'checkPointMessage' is assigned a value but never used.", "'setCheckPointMessage' is assigned a value but never used.", ["2996"], "'interactions' is assigned a value but never used.", "'setInteractions' is assigned a value but never used.", "'skip' is assigned a value but never used.", "'setSkip' is assigned a value but never used.", "'top' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'dropdownRef' is assigned a value but never used.", "'RadioButtonUncheckedIcon' is defined but never used.", "'RadioButtonCheckedIcon' is defined but never used.", "'topCenter' is defined but never used.", "'TextField' is defined but never used.", "'backgroundcoloricon' is defined but never used.", "'ButtonSettings' is defined but never used.", "'buttonProperty' is assigned a value but never used.", "'isEditingPrevious' is assigned a value but never used.", "'isEditingContinue' is assigned a value but never used.", "'previousButtonText' is assigned a value but never used.", "'continueButtonText' is assigned a value but never used.", "'buttonText' is assigned a value but never used.", "'setButtonText' is assigned a value but never used.", "'buttonToEdit' is assigned a value but never used.", "'isDeleteIcon' is assigned a value but never used.", "'isEditingButton' is assigned a value but never used.", "'isEditing' is assigned a value but never used.", "'setIsEditing' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setButtonProperty'. Either include it or remove the dependency array.", ["2997"], "'handlePreviousTextChange' is assigned a value but never used.", "'handleContinueTextChange' is assigned a value but never used.", "'toggleEdit' is assigned a value but never used.", "'handlePreviousBlur' is assigned a value but never used.", "'handleContinueBlur' is assigned a value but never used.", "'handleChangeButton' is assigned a value but never used.", "'handleEditButtonText' is assigned a value but never used.", "'selectedtemp' is defined but never used.", "'editingRTEId' is assigned a value but never used.", "'contentState' is assigned a value but never used.", "React Hook React.useCallback has a missing dependency: 'isContentScrollable'. Either include it or remove the dependency array.", ["2998"], "React Hook useEffect has a missing dependency: 'setToolbarVisibleRTEId'. Either include it or remove the dependency array.", ["2999"], ["3000"], "The 'handlePaste' function makes the dependencies of useCallback Hook (at line 511) change on every render. Move it inside the useCallback callback. Alternatively, wrap the definition of 'handlePaste' in its own useCallback() Hook.", "The 'containersToRender' array makes the dependencies of useEffect Hook (at line 565) change on every render. To fix this, wrap the initialization of 'containersToRender' in its own useMemo() Hook.", "'CachedIcon' is defined but never used.", "'snackbarKey' is assigned a value but never used.", "'openSnackbar' is assigned a value but never used.", "'setImageLink' is assigned a value but never used.", "'guidePopupRef' is assigned a value but never used.", "'handleLinkSubmit' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'updatePopoverPositions'. Either include it or remove the dependency array.", ["3001"], "'beta' is defined but never used.", "'setIsCollapsed' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "'micicon' is defined but never used.", "'micicon_hover' is defined but never used.", "'PerfectScrollbar' is defined but never used.", "'isChatOpen' is assigned a value but never used.", "'setIsChatOpen' is assigned a value but never used.", "'isMicHovered' is assigned a value but never used.", "'setIsMicHovered' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'accountId' and 'openSnackbar'. Either include them or remove the dependency array.", ["3002"], "'handleSpeechRecognition' is assigned a value but never used.", "'isTourCreationPrompt' is assigned a value but never used.", "'parseTourSteps' is assigned a value but never used.", "'dataNew' is assigned a value but never used.", "'stepDataNew' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setElementSelected'. Either include it or remove the dependency array.", ["3003"], "'OverlaySettingsProps' is defined but never used.", "'ElementsSettingsProps' is defined but never used.", "'setTooltipElementOptions' is assigned a value but never used.", "'updateprogressclick' is assigned a value but never used.", "'displayType' is assigned a value but never used.", "'dontShowAgain' is assigned a value but never used.", "'colors' is assigned a value but never used.", "'handleDisplayTypeChange' is assigned a value but never used.", "'handleBorderColorChange' is assigned a value but never used.", "'handleDontShowAgainChange' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dismissData.Color' and 'setDismiss'. Either include them or remove the dependency array.", ["3004"], "'defaultDots' is defined but never used.", "'topLeft' is defined but never used.", "'topRight' is defined but never used.", "'middleLeft' is defined but never used.", "'middleCenter' is defined but never used.", "'middleRight' is defined but never used.", "'bottomLeft' is defined but never used.", "'bottomMiddle' is defined but never used.", "'bottomRight' is defined but never used.", "'topcenter' is defined but never used.", "'setCanvasSetting' is assigned a value but never used.", "'setWidth' is assigned a value but never used.", "'setBorderRadius' is assigned a value but never used.", "'Annpadding' is assigned a value but never used.", "'setAnnPadding' is assigned a value but never used.", "'AnnborderSize' is assigned a value but never used.", "'setAnnBorderSize' is assigned a value but never used.", "'Bposition' is assigned a value but never used.", "'setBposition' is assigned a value but never used.", "'handleBackgroundColorChange' is assigned a value but never used.", "'checklistTitle' is assigned a value but never used.", "'setChecklistTitle' is assigned a value but never used.", "'checklistSubTitle' is assigned a value but never used.", "'setChecklistSubTitle' is assigned a value but never used.", "'setTempTitle' is assigned a value but never used.", "'settempTempTitle' is assigned a value but never used.", "'handleBlur' is assigned a value but never used.", "'RefObject' is defined but never used.", "'CustomWidthTooltip' is defined but never used.", "'EXTENSION_PART' is defined but never used.", "'TOOLTIP_HEIGHT' is defined but never used.", "'TOOLTIP_MN_WIDTH' is defined but never used.", "'TOOLTIP_MX_WIDTH' is defined but never used.", "'Code' is defined but never used.", "'VideoLibrary' is defined but never used.", "'RTE' is defined but never used.", "'translate' is assigned a value but never used.", "'tooltipBackgroundcolor' is assigned a value but never used.", "'tooltipborderradius' is assigned a value but never used.", "'tooltipBordercolor' is assigned a value but never used.", "'tooltippadding' is assigned a value but never used.", "'elementClick' is assigned a value but never used.", "'setDismiss' is assigned a value but never used.", "'savedRange' is assigned a value but never used.", "'setSaveRange' is assigned a value but never used.", "'isEditorFocused' is assigned a value but never used.", "'setIsEditorFocused' is assigned a value but never used.", "'handleDeleteSection' is assigned a value but never used.", "'handleCloneContainer' is assigned a value but never used.", "'handleDragStart' is assigned a value but never used.", "'handleDragEnter' is assigned a value but never used.", "'handleDragEnd' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'popupPosition', 'setCurrentHoveredElement', 'setTooltipPositionByXpath', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["3005"], "'canvasProperties' is assigned a value but never used.", "'RTEToolbar' is assigned a value but never used.", "React Hook useMemo has a missing dependency: 'handleFocus'. Either include it or remove the dependency array.", ["3006"], "'isInsideJoditPopup' is assigned a value but never used.", "'isPasteEvent' is assigned a value but never used.", "'imageContainerStyle' is assigned a value but never used.", "'iconRowStyle' is assigned a value but never used.", "'iconTextStyle' is assigned a value but never used.", "'setZindeex' is assigned a value but never used.", "'setOverlayEnabled' is assigned a value but never used.", "'handlePositionChange' is assigned a value but never used.", "'tempZIndex' is assigned a value but never used.", "'setTempZIndex' is assigned a value but never used.", "'tempBackgroundColor' is assigned a value but never used.", "'setTempBackgroundColor' is assigned a value but never used.", "'tempSectionColor' is assigned a value but never used.", "'setTempSectionColor' is assigned a value but never used.", "'Dialog' is defined but never used.", "'DialogContent' is defined but never used.", "'useMediaQuery' is defined but never used.", "'useTheme' is defined but never used.", "'zIndex' is defined but never used.", "'buttonsContainer' is assigned a value but never used.", "'cloneButtonContainer' is assigned a value but never used.", "'addNewButton' is assigned a value but never used.", "'deleteButton' is assigned a value but never used.", "'deleteButtonContainer' is assigned a value but never used.", "'updateContainer' is assigned a value but never used.", "'updateButtonInteraction' is assigned a value but never used.", "'setBtnBgColor' is assigned a value but never used.", "'setBtnTextColor' is assigned a value but never used.", "'setBtnBorderColor' is assigned a value but never used.", "'setBtnName' is assigned a value but never used.", "'setIsOpen' is assigned a value but never used.", "'selectedPosition' is assigned a value but never used.", "'url' is assigned a value but never used.", "'setUrl' is assigned a value but never used.", "'setAction' is assigned a value but never used.", "'setOpenInNewTab' is assigned a value but never used.", "'setColors' is assigned a value but never used.", "'buttonNameError' is assigned a value but never used.", "'setButtonNameError' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', 'defaultButtonColors.color', 'getCurrentButtonInfo', 'setCurrentButtonName', 'setSelectedTab', and 'setTargetURL'. Either include them or remove the dependency array.", ["3007"], "'positions' is assigned a value but never used.", "'curronButtonInfo' is assigned a value but never used.", "React Hook useMemo has missing dependencies: 'getCurrentButtonInfo' and 'setCurrentButtonName'. Either include them or remove the dependency array.", ["3008"], "'handlePositionClick' is assigned a value but never used.", "'setOpenTooltip' is assigned a value but never used.", "'setTooltipPositionByXpath' is assigned a value but never used.", "'updateTooltipBtnContainer' is assigned a value but never used.", "'updateTooltipImageContainer' is assigned a value but never used.", "'CustomImage' is defined but never used.", "'pageinteraction' is assigned a value but never used.", "React Hook useCallback has an unnecessary dependency: 'smoothScrollTo'. Either exclude it or remove the dependency array.", ["3009"], "React Hook useEffect has missing dependencies: 'currentStep' and 'selectedTemplate'. Either include them or remove the dependency array.", ["3010"], "React Hook useEffect has missing dependencies: 'currentStep' and 'currentStepIndex'. Either include them or remove the dependency array.", ["3011"], "React Hook useCallback has unnecessary dependencies: 'calculateBestPosition' and 'scrollToTargetElement'. Either exclude them or remove the dependency array.", ["3012"], "React Hook useCallback has a missing dependency: 'steps'. Either include it or remove the dependency array.", ["3013"], "React Hook useEffect has a missing dependency: 'currentStepIndex'. Either include it or remove the dependency array.", ["3014"], "React Hook useEffect has a missing dependency: 'updateTargetAndPosition'. Either include it or remove the dependency array.", ["3015"], ["3016"], ["3017"], "'hasOnlyTextContent' is assigned a value but never used.", "'hasOnlyButton' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE_HOTSPOT' is defined but never used.", "'TCanvas' is defined but never used.", "'updateDesignelementInTooltip' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE_HOTSPOT' is assigned a value but never used.", "'dismiss' is assigned a value but never used.", "'setSelectedPosition' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'selectedTemplate', 'selectedTemplateTour', 'setTooltipBackgroundcolor', 'setTooltipBordercolor', 'setTooltipBorderradius', 'setTooltipBordersize', 'setTooltipPadding', 'setTooltipPosition', 'setTooltipWidth', 'setTooltipXaxis', 'setTooltipYaxis', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["3018"], "'guideStatus' is assigned a value but never used.", "'setCheckPointsPopup' is assigned a value but never used.", "'handleCheckPointIconColorChange' is assigned a value but never used.", "'handleCheckPointTitleColorChange' is assigned a value but never used.", "'handleCheckPointDescriptionColorChange' is assigned a value but never used.", "'handleFileUpload' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["3019"], "'handleMenuScroll' is assigned a value but never used.", "'useCallback' is defined but never used.", "'AddIcon' is defined but never used.", "'checkpointsEditPopup' is assigned a value but never used.", "'updateChecklistCheckPoints' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'filteredInteractions'. Either include it or remove the dependency array.", ["3020"], "'applyclicked' is assigned a value but never used.", "'isSearching' is assigned a value but never used.", ["3021"], "'handleSearch' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistCheckpointListProperties' and 'icons'. Either include them or remove the dependency array.", ["3022"], "'modifySVGColor' is assigned a value but never used.", "'RemoveIcon' is defined but never used.", "'DriveFolderUploadIcon' is defined but never used.", "'BackupIcon' is defined but never used.", "'setLimit' is assigned a value but never used.", "'filters' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getAllFilesData'. Either include it or remove the dependency array.", ["3023"], "'FolderIcon' is defined but never used.", "'handleColorChange' is assigned a value but never used.", "'useAsyncError' is defined but never used.", "'getCurrentButtonInfo' is assigned a value but never used.", "'clickTimeout' is defined but never used.", "'handleEditButtonName' is assigned a value but never used.", "'Modal' is defined but never used.", "'IMG_EXPONENT' is defined but never used.", "'getAllFiles' is defined but never used.", "'selectedColor' is assigned a value but never used.", "'formOfUpload' is assigned a value but never used.", "'setFormOfUpload' is assigned a value but never used.", "'urll' is defined but never used.", "'handleHyperlinkClick' is assigned a value but never used.", "'handleOpenGallery' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setImageAnchorEl'. Either include it or remove the dependency array.", ["3024"], "'Image' is defined but never used.", "'Link' is defined but never used.", "'setIsUnSavedChanges' is assigned a value but never used.", "'setHtmlContent' is assigned a value but never used.", "'setTextvaluess' is assigned a value but never used.", "'setBackgroundC' is assigned a value but never used.", "'bpadding' is assigned a value but never used.", "'handleTooltipRTEBlur' is assigned a value but never used.", "React Hook useMemo has an unnecessary dependency: 'id'. Either exclude it or remove the dependency array.", ["3025"], "The 'updateContentState' function makes the dependencies of useCallback Hook (at line 271) change on every render. To fix this, wrap the definition of 'updateContentState' in its own useCallback() Hook.", ["3026"], "React Hook useEffect has a missing dependency: 'updateContentState'. Either include it or remove the dependency array.", ["3027"], "React Hook useEffect has a missing dependency: 'setToolbarVisibleRTEId'. Either include it or remove the dependency array. If 'setToolbarVisibleRTEId' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["3028"], "'ColorResult' is defined but never used.", "'setBtnIdss' is assigned a value but never used.", "'gotoNextButtonId' is assigned a value but never used.", "'matchingButton' is assigned a value but never used.", ["3029"], "'getSavedLanguage' is assigned a value but never used.", "'saveLanguage' is assigned a value but never used.", "'clearSavedLanguage' is assigned a value but never used.", "'getAvailableLanguages' is defined but never used.", "React Hook useEffect has a missing dependency: 'userInfoObj'. Either include it or remove the dependency array.", ["3030"], "'orgId' is assigned a value but never used.", "'DialogContentText' is defined but never used.", "'TextareaAutosize' is defined but never used.", "'closeIcon' is defined but never used.", "'FileUpload' is defined but never used.", "'timeStamp' is defined but never used.", "React Hook useEffect has a missing dependency: 'calculatePosition'. Either include it or remove the dependency array.", ["3031"], {"desc": "3032", "fix": "3033"}, {"desc": "3034", "fix": "3035"}, {"desc": "3036", "fix": "3037"}, {"desc": "3038", "fix": "3039"}, {"desc": "3040", "fix": "3041"}, {"desc": "3042", "fix": "3043"}, {"desc": "3044", "fix": "3045"}, {"desc": "3046", "fix": "3047"}, {"desc": "3048", "fix": "3049"}, {"desc": "3050", "fix": "3051"}, {"desc": "3052", "fix": "3053"}, {"desc": "3054", "fix": "3055"}, {"desc": "3056", "fix": "3057"}, {"desc": "3058", "fix": "3059"}, {"desc": "3060", "fix": "3061"}, {"desc": "3062", "fix": "3063"}, {"desc": "3064", "fix": "3065"}, {"desc": "3066", "fix": "3067"}, {"desc": "3068", "fix": "3069"}, {"desc": "3070", "fix": "3071"}, {"desc": "3072", "fix": "3073"}, {"messageId": "3074", "fix": "3075", "desc": "3076"}, {"messageId": "3077", "fix": "3078", "desc": "3079"}, {"desc": "3080", "fix": "3081"}, {"desc": "3082", "fix": "3083"}, {"desc": "3084", "fix": "3085"}, {"desc": "3086", "fix": "3087"}, {"desc": "3088", "fix": "3089"}, {"desc": "3090", "fix": "3091"}, {"messageId": "3074", "fix": "3092", "desc": "3076"}, {"messageId": "3077", "fix": "3093", "desc": "3079"}, {"messageId": "3074", "fix": "3094", "desc": "3076"}, {"messageId": "3077", "fix": "3095", "desc": "3079"}, {"messageId": "3074", "fix": "3096", "desc": "3076"}, {"messageId": "3077", "fix": "3097", "desc": "3079"}, {"messageId": "3074", "fix": "3098", "desc": "3076"}, {"messageId": "3077", "fix": "3099", "desc": "3079"}, {"messageId": "3074", "fix": "3100", "desc": "3076"}, {"messageId": "3077", "fix": "3101", "desc": "3079"}, {"messageId": "3074", "fix": "3102", "desc": "3076"}, {"messageId": "3077", "fix": "3103", "desc": "3079"}, {"messageId": "3074", "fix": "3104", "desc": "3076"}, {"messageId": "3077", "fix": "3105", "desc": "3079"}, {"messageId": "3074", "fix": "3106", "desc": "3076"}, {"messageId": "3077", "fix": "3107", "desc": "3079"}, {"desc": "3108", "fix": "3109"}, {"desc": "3110", "fix": "3111"}, {"desc": "3112", "fix": "3113"}, {"desc": "3114", "fix": "3115"}, {"desc": "3116", "fix": "3117"}, {"desc": "3118", "fix": "3119"}, {"desc": "3120", "fix": "3121"}, {"desc": "3122", "fix": "3123"}, {"messageId": "3124", "data": "3125", "fix": "3126", "desc": "3127"}, {"desc": "3128", "fix": "3129"}, {"desc": "3130", "fix": "3131"}, {"desc": "3132", "fix": "3133"}, {"desc": "3134", "fix": "3135"}, {"desc": "3136", "fix": "3137"}, {"desc": "3138", "fix": "3139"}, {"desc": "3140", "fix": "3141"}, {"desc": "3142", "fix": "3143"}, {"desc": "3144", "fix": "3145"}, {"desc": "3146", "fix": "3147"}, {"desc": "3148", "fix": "3149"}, {"desc": "3150", "fix": "3151"}, {"desc": "3152", "fix": "3153"}, {"desc": "3108", "fix": "3154"}, {"desc": "3155", "fix": "3156"}, {"desc": "3157", "fix": "3158"}, {"desc": "3159", "fix": "3160"}, {"desc": "3155", "fix": "3161"}, {"desc": "3162", "fix": "3163"}, {"desc": "3164", "fix": "3165"}, {"desc": "3166", "fix": "3167"}, {"desc": "3166", "fix": "3168"}, {"desc": "3169", "fix": "3170"}, {"desc": "3171", "fix": "3172"}, {"desc": "3173", "fix": "3174"}, {"desc": "3175", "fix": "3176"}, {"desc": "3177", "fix": "3178"}, {"desc": "3179", "fix": "3180"}, {"desc": "3181", "fix": "3182"}, {"desc": "3183", "fix": "3184"}, {"desc": "3185", "fix": "3186"}, {"desc": "3187", "fix": "3188"}, {"desc": "3189", "fix": "3190"}, {"desc": "3191", "fix": "3192"}, {"desc": "3193", "fix": "3194"}, {"desc": "3195", "fix": "3196"}, {"desc": "3197", "fix": "3198"}, {"desc": "3197", "fix": "3199"}, {"desc": "3200", "fix": "3201"}, {"desc": "3202", "fix": "3203"}, {"desc": "3204", "fix": "3205"}, {"desc": "3206", "fix": "3207"}, {"desc": "3204", "fix": "3208"}, {"desc": "3209", "fix": "3210"}, {"desc": "3211", "fix": "3212"}, {"desc": "3213", "fix": "3214"}, {"desc": "3215", "fix": "3216"}, {"desc": "3217", "fix": "3218"}, {"desc": "3219", "fix": "3220"}, {"desc": "3221", "fix": "3222"}, {"desc": "3223", "fix": "3224"}, {"desc": "3225", "fix": "3226"}, {"desc": "3227", "fix": "3228"}, "Update the dependencies array to be: [loggedOut]", {"range": "3229", "text": "3230"}, "Update the dependencies array to be: []", {"range": "3231", "text": "3232"}, "Update the dependencies array to be: [toolTipGuideMetaData, currentStep, hotspotClicked, fetchGuideDetails, hotspot]", {"range": "3233", "text": "3234"}, "Update the dependencies array to be: [designPopup, setDesignPopup]", {"range": "3235", "text": "3236"}, "Update the dependencies array to be: [isTemplateScreen, isPopupOpen, bannerPopup, currentGuideId, selectedTemplate, selectedTemplateTour, determineCurrentScreen]", {"range": "3237", "text": "3238"}, "Update the dependencies array to be: [isCollapsed, isGuideInfoScreen, isTemplateScreen, isPopupOpen, bannerPopup, currentScreen, selectedTemplate, selectedTemplateTour, guideName, currentGuideId, guideStep, htmlContent, imageSrc, buttonColor, imageName, errors]", {"range": "3239", "text": "3240"}, "Update the dependencies array to be: [initialState, setIsAIGuidePersisted]", {"range": "3241", "text": "3242"}, "Update the dependencies array to be: [isGuideInfoScreen, currentGuideId, clearGuideDetails, resetTooltipMetaData]", {"range": "3243", "text": "3244"}, "Update the dependencies array to be: [savedGuideData, isSaveInProgress, currentGuideId, selectedTemplate, selectedTemplateTour, bannerPopup, currentStep, setBannerPopup, setIsBannerCreationBuilderOpen, setIsAnnouncementCreationBuilderOpen, setIsTooltipCreationBuilderOpen, setIsHotspotCreationBuilderOpen, clearBannerButtonDetials, updateRTEContainerOnReload, setBannerButtonSelected, updateButtonContainerOnReload]", {"range": "3245", "text": "3246"}, "Update the dependencies array to be: [handleClose, selectedTemplate, selectedTemplateTour, isShowIcon, toolTipGuideMetaData, currentStep, elementSelected, resetALTKeywordForNewTooltip, setElementSelected, setIsALTKeywordEnabled]", {"range": "3247", "text": "3248"}, "Update the dependencies array to be: [openStepDropdown, plusIconclick, setSettingAnchorEl]", {"range": "3249", "text": "3250"}, "Update the dependencies array to be: [createWithAI, setIsUnSavedChanges, stepCreation]", {"range": "3251", "text": "3252"}, "Update the dependencies array to be: [isLoggedIn, organizationId, userType]", {"range": "3253", "text": "3254"}, "Update the dependencies array to be: [isAnnouncementPopupOpen, showBannerenduser, showTooltipenduser, showHotspotenduser, isTourTemplate]", {"range": "3255", "text": "3256"}, "Update the dependencies array to be: [currentGuide?.GuideStep, currentStep]", {"range": "3257", "text": "3258"}, "Update the dependencies array to be: [cleanupDuplicateSteps, createWithAI, currentGuideId, elementSelected, interactionData, resetHeightofBanner]", {"range": "3259", "text": "3260"}, "Update the dependencies array to be: [SetGuideName, cleanupDuplicateSteps, createWithAI, currentGuideId, currentStep, getGuideTypeValue, handlecreatefromScratchclick, interactionData, overlayEnabled, pageinteraction, progress, resetHeightofBanner, setBannerPopup, setBposition, setDismiss, setIsGuideInfoScreen, setOverlayEnabled, setPageInteraction, setProgress, setProgressColor, setSelectedOption, setSelectedTemplate, setSelectedTemplateTour, setTooltipCount, setTourDataOnEdit]", {"range": "3261", "text": "3262"}, "Update the dependencies array to be: [isAnnouncementPopupOpen, showHotspotenduser, isHotspotPopupOpen, isTourTemplate, isChecklistPreview, currentGuideId, setIsGuideInfoScreen, setSelectedTemplate, handlecreatefromScratchclick, SetGuideName, steps, selectedTemplate, getGuideTypeValue, currentStep]", {"range": "3263", "text": "3264"}, "Update the dependencies array to be: [isLoggedIn, setBannerPopup, setCreateWithAI, setCurrentGuideId, setIsAIGuidePersisted, setIsAnnouncementCreationBuilderOpen, setIsBannerCreationBuilderOpen, setIsGuideInfoScreen, setIsHomeScreen, setIsHotspotCreationBuilderOpen, setIsTemplateScreen, setIsTooltipCreationBuilderOpen, setIsTooltipPopup]", {"range": "3265", "text": "3266"}, "Update the dependencies array to be: [pendingWebTourModal, isTourPopupOpen, tourModalSource]", {"range": "3267", "text": "3268"}, "Update the dependencies array to be: [currentStep, deleteClicked, handleStepChange, playIconClicked, steps, updateStepClicked]", {"range": "3269", "text": "3270"}, "removeEscape", {"range": "3271", "text": "3272"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "3273", "text": "3274"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName, setBtnName]", {"range": "3275", "text": "3276"}, "Update the dependencies array to be: [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, targetURL]", {"range": "3277", "text": "3278"}, "Update the dependencies array to be: [selectedActions.value, targetURL]", {"range": "3279", "text": "3280"}, "Update the dependencies array to be: [isExtensionClosed, setIsExtensionClosed]", {"range": "3281", "text": "3282"}, "Update the dependencies array to be: [hasAnnouncementOpened, setDrawerActiveMenu, setDrawerSearchText, setHasAnnouncementOpened]", {"range": "3283", "text": "3284"}, "Update the dependencies array to be: [isExtensionClosed, activeMenu, searchText, setDrawerActiveMenu, setDrawerSearchText, setIsPopupOpen]", {"range": "3285", "text": "3286"}, {"range": "3287", "text": "3272"}, {"range": "3288", "text": "3274"}, {"range": "3289", "text": "3272"}, {"range": "3290", "text": "3274"}, {"range": "3291", "text": "3272"}, {"range": "3292", "text": "3274"}, {"range": "3293", "text": "3272"}, {"range": "3294", "text": "3274"}, {"range": "3295", "text": "3272"}, {"range": "3296", "text": "3274"}, {"range": "3297", "text": "3272"}, {"range": "3298", "text": "3274"}, {"range": "3299", "text": "3272"}, {"range": "3300", "text": "3274"}, {"range": "3301", "text": "3272"}, {"range": "3302", "text": "3274"}, "Update the dependencies array to be: [checkpointslistData]", {"range": "3303", "text": "3304"}, "Update the dependencies array to be: [bannerButtonSelected, buttonColor, rtesContainer, setButtonColor, textAreas, textBoxRef]", {"range": "3305", "text": "3306"}, "Update the dependencies array to be: [bannerButtonSelected, buttonColor, removeTextArea, setButtonColor, textAreas]", {"range": "3307", "text": "3308"}, "Update the dependencies array to be: [setTextArray, textAreas]", {"range": "3309", "text": "3310"}, "Update the dependencies array to be: [createWithAI, bannerButtonSelected, buttonColor, setButtonColor, textAreas]", {"range": "3311", "text": "3312"}, "Update the dependencies array to be: [selectedTemplate, selectedTemplateTour, setHotspotPopup, setShowLauncherSettings, setShowTooltipCanvasSettings, setTitlePopup]", {"range": "3313", "text": "3314"}, "Update the dependencies array to be: [SetElementButtonClick, createWithAI, currentGuideId, currentStep, interactionData, setButtonClick, setDropdownValue, setElementButtonName, setElementClick, setbtnidss, toolTipGuideMetaData]", {"range": "3315", "text": "3316"}, "Update the dependencies array to be: [OverlayValue]", {"range": "3317", "text": "3318"}, "suggestString", {"type": "3319"}, {"range": "3320", "text": "3321"}, "Use `\"undefined\"` instead of `undefined`.", "Update the dependencies array to be: [getElementPosition, xpath]", {"range": "3322", "text": "3323"}, "Update the dependencies array to be: [savedGuideData, xpath]", {"range": "3324", "text": "3325"}, "Update the dependencies array to be: [textFieldProperties, imageProperties, customButton, currentStep, calculateOptimalWidth]", {"range": "3326", "text": "3327"}, "Update the dependencies array to be: [currentStep, guideStep, setOpenTooltip]", {"range": "3328", "text": "3329"}, "Update the dependencies array to be: [currentStep, isHotspotPopupOpen, selectedTemplateTour, setOpenTooltip, toolTipGuideMetaData]", {"range": "3330", "text": "3331"}, "Update the dependencies array to be: [currentStep, selectedTemplateTour, setOpenTooltip, showHotspotenduser, toolTipGuideMetaData]", {"range": "3332", "text": "3333"}, "Update the dependencies array to be: [toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, createWithAI, syncAITooltipContainerData, applyHotspotProperties, applyCustomCursor]", {"range": "3334", "text": "3335"}, "Update the dependencies array to be: [isALTKeywordEnabled, elementSelected, selectedTemplate, selectedTemplateTour, applyCustomCursor]", {"range": "3336", "text": "3337"}, "Update the dependencies array to be: [stepType, currentStep, setAnnouncementPreview, setBannerPreview, setTooltipPreview, setHotspotPreview, savedGuideData?.GuideStep, setOpenTooltip, initializeTourHotspotMetadata]", {"range": "3338", "text": "3339"}, "Update the dependencies array to be: [paginationModel, activeTab, Open, accountId, fetchAnnouncements]", {"range": "3340", "text": "3341"}, "Update the dependencies array to be: [fetchAnnouncements, searchQuery]", {"range": "3342", "text": "3343"}, "Update the dependencies array to be: [checkpointslistData, completedStatus]", {"range": "3344", "text": "3345"}, "Update the dependencies array to be: [selectedItem, activeItem, createWithAI, interactionData]", {"range": "3346", "text": "3347"}, {"range": "3348", "text": "3304"}, "Update the dependencies array to be: [checklistGuideMetaData]", {"range": "3349", "text": "3350"}, "Update the dependencies array to be: [checklistGuideMetaData, checklistLauncherProperties, icons]", {"range": "3351", "text": "3352"}, "Update the dependencies array to be: [checklistLauncherProperties, icons, updateChecklistLauncher]", {"range": "3353", "text": "3354"}, {"range": "3355", "text": "3350"}, "Update the dependencies array to be: [setButtonProperty]", {"range": "3356", "text": "3357"}, "Update the dependencies array to be: [isContentScrollable]", {"range": "3358", "text": "3359"}, "Update the dependencies array to be: [setToolbarVisibleRTEId, toolbarVisibleRTEId]", {"range": "3360", "text": "3361"}, {"range": "3362", "text": "3361"}, "Update the dependencies array to be: [open, openSettingsPopover, updatePopoverPositions]", {"range": "3363", "text": "3364"}, "Update the dependencies array to be: [accountId, openSnackbar]", {"range": "3365", "text": "3366"}, "Update the dependencies array to be: [setElementSelected]", {"range": "3367", "text": "3368"}, "Update the dependencies array to be: [dismissData.Color, dismissData?.dismisssel, setDismiss]", {"range": "3369", "text": "3370"}, "Update the dependencies array to be: [currentStep, openTooltip, popupPosition, setCurrentHoveredElement, setTooltipPositionByXpath, toolTipGuideMetaData]", {"range": "3371", "text": "3372"}, "Update the dependencies array to be: [handleFocus, isRtlDirection]", {"range": "3373", "text": "3374"}, "Update the dependencies array to be: [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setCurrentButtonName, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, setSelectedTab]", {"range": "3375", "text": "3376"}, "Update the dependencies array to be: [getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName]", {"range": "3377", "text": "3378"}, "Update the dependencies array to be: [universalScrollTo]", {"range": "3379", "text": "3380"}, "Update the dependencies array to be: [currentStep, currentStepIndex, interactWithPage, selectedTemplate]", {"range": "3381", "text": "3382"}, "Update the dependencies array to be: [currentStep, currentStepData?.overlay, currentStepIndex, interactWithPage]", {"range": "3383", "text": "3384"}, "Update the dependencies array to be: [currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, setCurrentStep, setCurrentStepIndex]", {"range": "3385", "text": "3386"}, "Update the dependencies array to be: [selectedTemplate, currentStepIndex, setCurrentStep, currentStep, steps]", {"range": "3387", "text": "3388"}, "Update the dependencies array to be: [currentStepData, currentStepIndex, handleNext]", {"range": "3389", "text": "3390"}, "Update the dependencies array to be: [currentStepData, currentUrl, updateTargetAndPosition]", {"range": "3391", "text": "3392"}, {"range": "3393", "text": "3392"}, "Update the dependencies array to be: [currentStepData, currentUrl, rect, updateTargetAndPosition]", {"range": "3394", "text": "3395"}, "Update the dependencies array to be: [currentStep, selectedTemplate, selectedTemplateTour, setTooltipBackgroundcolor, setTooltipBordercolor, setTooltipBorderradius, setTooltipBordersize, setTooltipPadding, setTooltipPosition, setTooltipWidth, setTooltipXaxis, setTooltipYaxis, toolTipGuideMetaData]", {"range": "3396", "text": "3397"}, "Update the dependencies array to be: [fetchData]", {"range": "3398", "text": "3399"}, "Update the dependencies array to be: [selectedInteraction, interactions, searchTerm, filteredInteractions]", {"range": "3400", "text": "3401"}, {"range": "3402", "text": "3399"}, "Update the dependencies array to be: [checklistCheckpointListProperties, icons]", {"range": "3403", "text": "3404"}, "Update the dependencies array to be: [skip, limit, getAllFilesData]", {"range": "3405", "text": "3406"}, "Update the dependencies array to be: [setImageAnchorEl, tooltip.visible]", {"range": "3407", "text": "3408"}, "Update the dependencies array to be: [toolbarVisible]", {"range": "3409", "text": "3410"}, "Wrap the definition of 'updateContentState' in its own useCallback() Hook.", {"range": "3411", "text": "3412"}, "Update the dependencies array to be: [rteBoxValue, updateContentState]", {"range": "3413", "text": "3414"}, "Update the dependencies array to be: [isEditing, setToolbarVisibleRTEId]", {"range": "3415", "text": "3416"}, "Update the dependencies array to be: [settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep, createWithAI, interactionData]", {"range": "3417", "text": "3418"}, "Update the dependencies array to be: [orgId, accessToken, userInfoObj]", {"range": "3419", "text": "3420"}, "Update the dependencies array to be: [calculatePosition, openGenAiImagePopup]", {"range": "3421", "text": "3422"}, [4501, 4503], "[loggedOut]", [18364, 18386], "[]", [27185, 27236], "[toolTipGuideMetaData, currentStep, hotspotClicked, fetchGuideDetails, hotspot]", [27718, 27731], "[designPopup, setDesignPopup]", [30455, 30602], "[isTemplateScreen, isPopupOpen, bannerPopup, currentGuideId, selectedTemplate, selectedTemplateTour, determineCurrentScreen]", [31080, 31440], "[isCollapsed, isGuideInfoScreen, isTemplateScreen, isPopupOpen, bannerPopup, currentScreen, selectedTemplate, selectedTemplateTour, guideName, currentGuideId, guideStep, htmlContent, imageSrc, buttonColor, imageName, errors]", [31808, 31822], "[initialState, setIsAIGuidePersisted]", [31961, 31996], "[isGuideInfoScreen, currentGuideId, clearGuideDetails, resetTooltipMetaData]", [36390, 36424], "[savedGuideData, isSaveInProgress, currentGuideId, selectedTemplate, selectedTemplateTour, bannerPopup, currentStep, setBannerPopup, setIsBannerCreationBuilderOpen, setIsAnnouncementCreationBuilderOpen, setIsTooltipCreationBuilderOpen, setIsHotspotCreationBuilderOpen, clearBannerButtonDetials, updateRTEContainerOnReload, setBannerButtonSelected, updateButtonContainerOnReload]", [72553, 72617], "[handleClose, selectedTemplate, selectedTemplateTour, isShowIcon, toolTipGuideMetaData, currentStep, elementSelected, resetALTKeywordForNewTooltip, setElementSelected, setIsALTKeywordEnabled]", [88764, 88797], "[openStepDropdown, plusIconclick, setSettingAnchorEl]", [104136, 104150], "[createWithAI, setIsUnSavedChanges, stepCreation]", [128006, 128034], "[isLoggedIn, organizationId, userType]", [134566, 134718], "[isAnnounce<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, showB<PERSON><PERSON><PERSON>er, showTooltipenduser, showHotspotenduser, isTourTemplate]", [164736, 164793], "[currentGuide?.GuideStep, currentStep]", [170049, 170066], "[cleanupDuplicateSteps, createWithAI, currentGuideId, elementSelected, interactionData, resetHeightofBanner]", [175999, 176032], "[SetGuideName, cleanupDuplicateSteps, createWithAI, currentGuideId, currentStep, getGuideTypeValue, handlecreatefromScratchclick, interactionData, overlayEnabled, pageinteraction, progress, resetHeightofBanner, setBannerPopup, setBposition, setDismiss, setIsGuideInfoScreen, setOverlayEnabled, setPageInteraction, setProgress, setProgressColor, setSelectedOption, setSelectedTemplate, setSelectedTemplateTour, setTooltipCount, setTourDataOnEdit]", [176742, 176843], "[isAnnouncementPopupOpen, showHotspotenduser, isHotspotPopupOpen, isTourTemplate, isChecklistPreview, currentGuideId, setIsGuideInfoScreen, setSelectedTemplate, handlecreatefromScratchclick, SetGuideName, steps, selectedTemplate, getGuideTypeValue, currentStep]", [182273, 182285], "[isLoggedIn, setBannerPopup, setCreateWithAI, setCurrentGuideId, setIsAIGuidePersisted, setIsAnnouncementCreationBuilderOpen, setIsBannerCreationBuilderOpen, setIsGuideInfoScreen, setIsHomeScreen, setIsHotspotCreationBuilderOpen, setIsTemplateScreen, setIsTooltipCreationBuilderOpen, setIsTooltipPopup]", [182705, 182778], "[pendingWebTourModal, isTourPopupOpen, tourModalSource]", [183397, 183404], "[currentStep, deleteClicked, handleStepChange, playIconClicked, steps, updateStepClicked]", [205575, 205576], "", [205575, 205575], "\\", [16287, 16342], "[getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName, setBtnName]", [17716, 17771], "[settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, targetURL]", [18216, 18218], "[selectedActions.value, targetURL]", [2476, 2478], "[isExtensionClosed, setIsExtensionClosed]", [3143, 3166], "[hasAnnouncementOpened, setDrawerActiveMenu, setDrawerSearchText, setHasAnnouncementOpened]", [3510, 3553], "[isExtensionClosed, activeMenu, searchText, setDrawerActiveMenu, setDrawerSearchText, setIsPopupOpen]", [22521, 22522], [22521, 22521], [22525, 22526], [22525, 22525], [22536, 22537], [22536, 22536], [22540, 22541], [22540, 22540], [22569, 22570], [22569, 22569], [22573, 22574], [22573, 22573], [22584, 22585], [22584, 22584], [22588, 22589], [22588, 22588], [5944, 5977], "[checkpointslistData]", [6153, 6155], "[bannerButtonSelected, buttonColor, rtesContainer, setButtonColor, textAreas, textBoxRef]", [7218, 7240], "[bannerButtonSelected, buttonColor, removeTextArea, setButtonColor, textAreas]", [7401, 7412], "[setTex<PERSON><PERSON><PERSON><PERSON>, text<PERSON><PERSON>s]", [8963, 9028], "[createWithAI, bannerButtonSelected, buttonColor, setButtonColor, textAreas]", [5310, 5350], "[selectedTemplate, selectedTemplateTour, setHotspotPopup, setShowLauncherSettings, setShowTooltipCanvasSettings, setTitlePopup]", [10234, 10269], "[SetElementButtonClick, createWithAI, currentGuideId, currentStep, interactionData, setButtonClick, setDropdownValue, setElementButtonName, setElementClick, setbtnidss, toolTipGuideMetaData]", [4728, 4730], "[OverlayValue]", "undefined", [6611, 6620], "\"undefined\"", [6747, 6754], "[getElementPosition, xpath]", [6883, 6899], "[savedGuideData, xpath]", [16346, 16411], "[textFieldProperties, imageProperties, customButton, currentStep, calculateOptimalWidth]", [20560, 20619], "[currentStep, guideStep, setOpenTooltip]", [21305, 21347], "[currentStep, isHotspotPopupOpen, selectedTemplateTour, setOpenTooltip, toolTipGuideMetaData]", [21971, 22013], "[currentStep, selectedTemplateTour, setOpenTooltip, showHotspotenduser, toolTipGuideMetaData]", [25153, 25248], "[toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, createWithAI, syncAITooltipContainerData, applyHotspotProperties, applyCustomCursor]", [25832, 25910], "[isALTKeywordEnabled, elementSelected, selectedTemplate, selectedTemplateTour, applyCustomCursor]", [6220, 6243], "[stepType, currentStep, setAnnouncementPreview, setBannerPreview, setTooltipPreview, setHotspotPreview, savedGuideData?.GuideStep, setOpenTooltip, initializeTourHotspotMetadata]", [9051, 9096], "[paginationModel, activeTab, Open, accountId, fetchAnnouncements]", [9402, 9415], "[fetchAnnouncements, searchQuery]", [3543, 3545], "[checkpointslistData, completedStatus]", [4387, 4413], "[selectedItem, activeItem, createWithAI, interactionData]", [4517, 4551], [5507, 5534], "[checklistGuideMetaData]", [4872, 4874], "[checklistGuideMetaData, checklistLauncherProperties, icons]", [9650, 9652], "[checklistLauncherProperties, icons, updateChecklistLauncher]", [3211, 3251], [2683, 2685], "[setButtonProperty]", [4673, 4675], "[isContentScrollable]", [7509, 7530], "[setToolbarVisibleRTEId, toolbarVisibleRTEId]", [11107, 11128], [17228, 17255], "[open, openSettingsPopover, updatePopoverPositions]", [4604, 4606], "[accountId, openSnackbar]", [17421, 17423], "[setElementSelected]", [6878, 6903], "[dismissData.Color, dismissData?.dismisssel, setDismiss]", [10303, 10316], "[currentStep, openTooltip, popupPosition, setCurrentHoveredElement, setTooltipPositionByXpath, toolTipGuideMetaData]", [26651, 26667], "[handleFocus, isRtlDirection]", [4656, 4711], "[settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setCurrentButtonName, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, setSelectedTab]", [6189, 6244], "[getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName]", [19204, 19239], "[universalScrollTo]", [20357, 20393], "[currentStep, currentStepIndex, interactWithPage, selectedTemplate]", [20804, 20848], "[currentStep, currentStepData?.overlay, currentStepIndex, interactWithPage]", [29926, 30081], "[currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, setCurrentStep, setCurrentStepIndex]", [32166, 32271], "[selectedTemplate, currentStepIndex, setCurrentStep, currentStep, steps]", [34487, 34516], "[currentStepData, currentStepIndex, handleNext]", [34993, 35022], "[currentStepData, currentUrl, updateTargetAndPosition]", [35434, 35463], [35522, 35557], "[currentStepData, currentUrl, rect, updateTargetAndPosition]", [7898, 7945], "[currentStep, selectedTemplate, selectedTemplateTour, setTooltipBackgroundcolor, setTooltipBordercolor, setTooltipBorderradius, setTooltipBordersize, setTooltipPadding, setTooltipPosition, setTooltipWidth, setTooltipXaxis, setTooltipYaxis, toolTipGuideMetaData]", [7680, 7682], "[fetchData]", [4285, 4332], "[selectedInteraction, interactions, searchTerm, filteredInteractions]", [6555, 6557], [12538, 12540], "[checklistCheckpointListProperties, icons]", [2046, 2059], "[skip, limit, getAllFilesData]", [10496, 10513], "[setImageAnchorEl, tooltip.visible]", [5518, 5538], "[toolbarVisible]", [6321, 6491], "useCallback((content: string) => {\r\n\t\t\tconst isEmpty = isContentEmpty(content);\r\n\t\t\tconst isScrollable = isContentScrollable();\r\n\r\n\t\t\tsetContentState({ isEmpty, isScrollable });\r\n\t\t})", [9239, 9252], "[rteBoxValue, updateContentState]", [12542, 12553], "[isEditing, setToolbarVisibleRTEId]", [4267, 4417], "[settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep, createWithAI, interactionData]", [4596, 4616], "[orgId, accessToken, userInfoObj]", [4820, 4841], "[calculatePosition, openGenAiImagePopup]"]