.rtl {
	.qadpt-overlay {
		.leftDrawer {
			right: 0 !important;
			left: auto !important;
			&.collapsed{
				border-top-left-radius: 12px !important;
    border-bottom-left-radius: 12px !important;
			}
			.qadpt-toggleIcon svg {
				transform: rotate(180deg);
			}
		}
	}
	.qadptDrawerContent {
		.qadptFormLabel {
			float: right;
		}
		.qadptWelcomeMessage {
			text-align: right !important;
		}
	}
	.qadpt-drawerContent {
		.qadpt-subhead .qadpt-backbtn {
			left: auto !important;
			right: 20px;
			transform: rotate(180deg);
		}
																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																.qadpt-guide-form {
																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																	.qadpt-errmsg span {
																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																		margin-left: 4px !important;
																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																	}
																																																																																																																																																																																																																																																																
																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																	.qadpt-guide-label {
			text-align: right !important;
		}
																																																																																																																																																																																																																																																																}
		.side-menu {
			.menu-list {
  .menu-item {
    .icons {
      margin-left: 10px;
      margin-right: 0 !important;
    }

    &[data-id="banners"] .icons svg,
    &[data-id="tooltips"] .icons svg,
    &[data-id="survey"] .icons svg {
     transform: scaleX(-1);
    }
  }
}

			.qadpt-ai-container {
				.qadpt-ai-title {
					text-align: right;
				}
				.beta {
					left: 23px !important;
					right: auto;
					border-radius: 0px 6px 6px 0px;
				}
				.qadpt-icon {
					margin-right: 0px !important;
					margin-left: 10px !important;
				}
			}
		}
	}
	.qadpt-threedot-popup .qadpt-popup-icon {
		margin-right: 0px;
		margin-left: 10px;
	}

	.qadpt-gud-menupopup {
		margin-right: 300px !important;
		margin-left: auto !important;
		.qadpt-webgird {
			.MuiDataGrid-cell:nth-child(2),
			.MuiDataGrid-cell:nth-child(3),
			.MuiDataGrid-cell:nth-child(4) {
				text-align: right !important;
			}
		}
  }
		.qadpt-titsection .qadpt-memberButton svg {
			margin-left: 8px;
			margin-right: 0 !important;
		}
    
	.qadpt-ext-banner {
		.qadpt-left-banner {
			padding-left: auto;
			padding-right: 10px;
			.qadpt-edtxt {
				margin-right: 8px;
				margin-left: 0 !important;
				padding-right: 10px;
				padding-left: 0 !important;
				border-right: 2px solid rgb(204, 204, 204);
				border-left: 0 !important;
			}
			.guidename-btn {
				margin-left: auto !important;
				margin-right: 6px;
				.back-icon {
					transform: rotate(180deg);
				}
			}
		}
		.qadpt-center-banner .qadpt-banner-button:not(.qadpt-icon) svg {
			margin-right: 0px !important;
			margin-left: -5px;
		}
		.qadpt-right-banner {
			justify-content: left !important;
			padding-left: 10px;
			padding-right: auto;
		}
	}
	.qadpt-designpopup {
		right: 10px;
		left: auto !important;
		&.qadpt-btnprop {
			left: 165px !important;
			right: auto !important;
		}
		&.qadpt-banbtnprop {
			right: auto !important;
			left: 300px !important;
		}
		&.qadpt-tltbtnprop {
			left: 260px !important;
			right: auto !important;
		}

		.qadpt-controls {
    .qadpt-design-btn {
        .qadpt-hotsicon,.MuiButton-startIcon{
				margin-left: 8px;
				margin-right: -4px;
			}
}
.qadpt-gtnext span{
  margin-right: 0px !important;
  margin-left: 8px;

}
.qadpt-chos-btn p{
  text-align: right !important;
}
			.qadpt-position-grid .qadpt-ctrl-title {
				text-align: right;
			}
			.qadpt-control-box {
        &:not(.qadpt-chkcontrol-box) {
            padding-left: 8px;
            padding-right: 0 !important;
          }
				.qadpt-control-label {
					margin-left: auto;
					margin-right: 0px;
				}
				.qadpt-control-input {
					margin-left: 0;
					margin-right: auto;
					.MuiInputBase-root {
						padding-left: 14px;
						padding-right: 0;
					}
					
				}
				.qadpt-chkoffset{
					padding-left: 8px;
					padding-right: 0 !important;
				}
   
			}
		}
		.qadpt-content .qadpt-design-header button svg {
			transform: rotate(180deg);
		}
	}
	.qadpt-container.creation {
		.qadpt-options-menu {
			left: 25px;
			right: auto !important;
		}
	}
	.qadpt-modal .qadpt-tours-container {
		.qadpt-step-label {
			margin-right: 8px;
			margin-left: 0 !important;
		}
		.qadpt-subtitle {
			text-align: right;
		}
	}
	.step-dropdown .qadpt-stpname {
		text-align: right !important;
	}
	.qadpt-chklayout {
		right: auto;
		left: 20px;
	}
	// .qadpt-chkpopup {
	// 	margin-left: 40px !important;
	// 	margin-right: auto !important;
	// 	.qadpt-chkrgt {
	// 		border-left: 1px solid rgb(229, 231, 235);
	// 		border-right: 0 !important;
	// 	}
	// 	.qadpt-chkstp .qadpt-chkstpctn {
	// 		padding-right: 20px !important;
	// 		padding-left: 10px !important;
	// 	}
	// }
	.qadpt-editor {
		right: 0;
		left: auto !important;
		button {
			.qadpt-sep {
				border-left: 1px solid #ccc;
				border-right: 0 !important;
				svg {
					transform: rotate(180deg);
				}
			}
			.qadpt-bansep svg{					
				transform: rotate(180deg);
}
			.edt-txt {
				margin-left: 10px;
				margin-right: 0 !important;
			}
		}
		.qadpt-curstep {
			margin-right: 11px;
			margin-left: 0 !important;
		}
	}
	.qadpt-dismiss {
		left: -10px !important;
		right: auto !important;
	}
	.toggle-switch {
		input:checked + .slider:before {
			transform: translateX(0px);
		}
		.slider:before {
			transform: translateX(20px);
		}
	}
	.del-icon {
		left: -10px;
		right: auto;
	}
	.undo-redobtn {
		display: inline-flex;
		direction: ltr;
	}
	.MuiTablePagination-toolbar {
		.MuiTablePagination-input {
			margin-right: 8px !important;
			margin-left: 32px !important;
		}
		.MuiTablePagination-displayedRows,
		.MuiTablePagination-actions {
			direction: ltr;
		}
		.MuiTablePagination-actions {
			margin-left: 0px;
			margin-right: 20px;
		}
	}
	.pwdicon-blk {
		margin-left: 0px !important;
	}

.MuiDataGrid-scrollbar.MuiDataGrid-scrollbar--vertical {
    right: auto;
    left: 0;
}
// .qadpr-chkprvlayout { 
//   &.right {
//     left: var(--x-offset) !important;
//     right: auto;
//   }
// &.left {
//     right: var(--x-offset) !important;
//     left: auto;
//   }
// }
// .qadpt-prvchkpopup{

// &.right-position {
//     margin-left: var(--x-offset);
//     margin-right: auto;
//     .qadpt-chkrgt{
//         border-left: 1px solid rgb(229, 231, 235);
//         border-right: 0 !important;
//     }
//   }
  
//   &.left-position {
//     margin-left: auto;
//     margin-right: var(--x-offset);
//     .qadpt-chkrgt{
//         border-left: 1px solid rgb(229, 231, 235);
//         border-right: 0 !important;
//     }
//   }
// }
 .qadpt-memberButton svg{
    margin-left: 8px;
    margin-right: 0 !important;

}
.qadpt-elmslc{
     margin-left: 8px;
	 margin-right: 0 !important;
  }
  .qadpt-acnt-drpdwn .MuiPaper-root.MuiPaper-elevation{
	right: 170px !important;
	}
	
	// .jodit_theme_default.jodit-popup_strategy_leftbottom {
	// 	right: 630px !important;
	// 	left: auto !important;
	// }
	// 			.jodit_theme_default.jodit-popup_strategy_lefttop {
	// 				bottom: 6px;
	// 				top: auto !important;
	// 				left: auto !important;
	// 				right: 630px;
	// 			}

	.jodit-ui-checkbox .jodit-ui-checkbox__wrapper .jodit-ui-checkbox__input {
		margin: 3px 4px 3px 3px !important;
	}
	.jodit-ui-input__wrapper .jodit-ui-input__input{
		text-align: right !important;
	}
	
	.MuiInputBase-root {
		.MuiSelect-select {
			padding-left: 32px !important;
			padding-right: 8px !important;
			text-align: right !important;
		}
	
		.MuiSelect-icon {
			right: auto;
			left: 7px;
		}
}
 .jodit-popup,.jodit-toolbar__box, .jodit-ui-group {
    direction: rtl !important;
    text-align: right !important;
}
.qadpt-chat-window{
	right: 0;
	left : auto !important;
}
.qadpt-tours-container .qadpt-tours-content .qadpt-feature-icon svg{
	transform: scaleX(-1);
}
.qadpt-toaster .MuiAlert-message{
	text-align: right !important;
}
 .qadpt-webclonepopup .qadpt-close {  
    left: 20px !important;
	right : auto !important;    
 }
}