{"ast": null, "code": "var _jsxFileName = \"E:\\\\Code\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\Button.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Box, Button, Popover, Typography, IconButton, Tooltip } from \"@mui/material\";\nimport { ChromePicker } from \"react-color\";\nimport { deleteicon, copyicon, settingsicon } from \"../../../assets/icons/icons\";\nimport useDrawerStore from \"../../../store/drawerStore\";\nimport AddIcon from \"@mui/icons-material/Add\";\nimport { useTranslation } from 'react-i18next';\n\n// Function to get GuidePopUp position and dimensions\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst getGuidePopupPosition = () => {\n  const element = document.querySelector('.qadpt-guide-popup .MuiDialog-paper') || document.getElementById('guide-popup');\n  if (element) {\n    const rect = element.getBoundingClientRect();\n    return {\n      top: rect.top,\n      left: rect.left,\n      width: rect.width,\n      height: rect.height\n    };\n  }\n  return null;\n};\n\n// Function to calculate button properties popup position\nconst getButtonPropertiesPopupPosition = () => {\n  const guidePopupPos = getGuidePopupPosition();\n  if (!guidePopupPos) {\n    return {};\n  }\n  const viewportWidth = window.innerWidth;\n  const viewportHeight = window.innerHeight;\n  const popupWidth = 300; // Estimated width of button properties popup\n  const popupHeight = 200; // Estimated height of button properties popup\n  const gap = 15; // Required 15px gap\n  const headerHeight = 56;\n  const bottomPadding = 20;\n  const topPadding = 20;\n\n  // Calculate optimal vertical position (vertically aligned to screen)\n  const idealTop = (viewportHeight - popupHeight) / 2;\n  const minTop = headerHeight + topPadding;\n  const maxTop = viewportHeight - popupHeight - bottomPadding;\n  const viewportCenteredTop = Math.max(minTop, Math.min(idealTop, maxTop));\n\n  // Position to the right of the GuidePopUp with 15px gap\n  const rightPosition = guidePopupPos.left + guidePopupPos.width + gap;\n\n  // Check if there's enough space on the right\n  if (rightPosition + popupWidth <= viewportWidth - 10) {\n    return {\n      position: 'fixed',\n      top: `${viewportCenteredTop}px`,\n      left: `${rightPosition}px`\n    };\n  } else {\n    // If not enough space on right, position to the left\n    const leftPosition = guidePopupPos.left - popupWidth - gap;\n    return {\n      position: 'fixed',\n      top: `${viewportCenteredTop}px`,\n      left: `${Math.max(10, leftPosition)}px`\n    };\n  }\n};\nconst ButtonSection = ({\n  buttonColor,\n  setButtonColor,\n  isBanner,\n  isCloneDisabled,\n  onDelete,\n  onClone\n}) => {\n  _s();\n  var _buttonsContainer$fin, _buttonsContainer$fin2;\n  const {\n    buttonsContainer,\n    cloneButtonContainer,\n    updateButton,\n    addNewButton,\n    deleteButton,\n    deleteButtonContainer,\n    updateContainer,\n    setSettingAnchorEl,\n    selectedTemplate,\n    selectedTemplateTour,\n    setSelectedTemplate,\n    buttonProperty,\n    setButtonProperty,\n    btnBgColor,\n    btnBorderColor,\n    btnTextColor,\n    setButtonId,\n    setCuntainerId,\n    createWithAI,\n    currentStep,\n    ensureAnnouncementButtonContainer,\n    closeAllButtonPopups,\n    triggerCloseAllButtonPopups\n  } = useDrawerStore(state => state);\n  const {\n    t: translate\n  } = useTranslation();\n  const [isEditingPrevious, setIsEditingPrevious] = useState(false);\n  const [isEditingContinue, setIsEditingContinue] = useState(false);\n  const [previousButtonText, setPreviousButtonText] = useState(\"Previous\");\n  const [continueButtonText, setContinueButtonText] = useState(\"Continue\");\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [selectedColor, setSelectedColor] = useState(\"#313030\");\n  const [colorPickerAnchorEl, setColorPickerAnchorEl] = useState(null);\n  const [buttonText, setButtonText] = useState(\"Continue\");\n  const [buttonToEdit, setButtonToEdit] = useState(null);\n  const [isDeleteIcon, setIsDeleteIcon] = useState(\"\");\n  const [currentContainerId, setCurrentContainerId] = useState(\"\");\n  const [currentButtonId, setCurrentButtonId] = useState(\"\");\n  const [isEditingButton, setIsEditingButton] = useState(false);\n  const [isEditing, setIsEditing] = useState(null);\n  const [popupPosition, setPopupPosition] = useState({});\n\n  // Update popup position when popup opens or window resizes\n  useEffect(() => {\n    if (anchorEl) {\n      const updatePosition = () => {\n        const newPosition = getButtonPropertiesPopupPosition();\n        setPopupPosition(newPosition);\n      };\n      updatePosition();\n      window.addEventListener('resize', updatePosition);\n      return () => window.removeEventListener('resize', updatePosition);\n    }\n  }, [anchorEl]);\n\n  // Default button color\n  let clickTimeout;\n  useEffect(() => {\n    setAnchorEl(null);\n    setButtonProperty(false);\n  }, []); // Empty dependency array ensures it runs only once\n\n  // Listen for global close all button popups trigger\n  useEffect(() => {\n    if (closeAllButtonPopups > 0) {\n      setAnchorEl(null);\n      setColorPickerAnchorEl(null);\n      setButtonToEdit(null);\n    }\n  }, [closeAllButtonPopups]);\n  const handleClick = (event, buttonId) => {\n    const target = event.currentTarget;\n    clickTimeout = setTimeout(() => {\n      setAnchorEl(target);\n      setCurrentButtonId(buttonId);\n      setIsEditingButton(false);\n      handleEditButtonName(currentContainerId, buttonId, \"isEditing\", false);\n    }, 200);\n  };\n  const handleClose = () => {\n    setAnchorEl(null);\n    setSettingAnchorEl({\n      containerId: \"\",\n      buttonId: \"\",\n      value: null\n    });\n    setButtonToEdit(null);\n  };\n  const handlePreviousTextChange = event => {\n    setPreviousButtonText(event.target.value);\n  };\n  const handleContinueTextChange = event => {\n    setContinueButtonText(event.target.value);\n  };\n  const handleBackgroundColorClick = event => {\n    setColorPickerAnchorEl(event.currentTarget);\n    // Close the main popup when opening color picker\n    handleClose();\n  };\n  const handleColorChange = color => {\n    setSelectedColor(color.hex);\n    // Update the backgroundColor in the container's style\n    updateContainer(currentContainerId, \"style\", {\n      backgroundColor: color.hex\n    });\n\n    // Also update the BackgroundColor property at the ButtonSection level\n    updateContainer(currentContainerId, \"BackgroundColor\", color.hex);\n  };\n  const handleCloseColorPicker = () => {\n    setColorPickerAnchorEl(null);\n  };\n  const open = Boolean(anchorEl);\n  // const open = Boolean(anchorEl && !isEditingButton);\n  const id = open ? \"button-popover\" : undefined;\n  const colorPickerOpen = Boolean(colorPickerAnchorEl);\n  const toggleEdit = button => {\n    if (button === \"Previous\") {\n      setIsEditingPrevious(true);\n    } else if (button === \"Continue\") {\n      setIsEditingContinue(true);\n    }\n  };\n  const handlePreviousBlur = () => {\n    setIsEditingPrevious(false);\n  };\n  const handleContinueBlur = () => {\n    setIsEditingContinue(false);\n  };\n  const handleEditButtonName = (containerId, buttonId, isEditing, value) => {\n    clearTimeout(clickTimeout);\n    setIsEditingButton(true);\n    updateButton(containerId, buttonId, isEditing, value);\n  };\n  const handleChangeButton = (containerId, buttonId, value) => {\n    updateButton(containerId, buttonId, \"type\", value);\n  };\n  const handleEditButtonText = (containerId, buttonId, newText) => {\n    updateButton(containerId, buttonId, \"name\", newText);\n    setButtonToEdit(null); // Exit edit mode after saving\n  };\n  //   const [buttonCount, setButtonCount] = useState(0);\n  const handleAddIconClick = containerId => {\n    //  const buttonName = buttonCount === 0 ? \"Got It\" : `Button${buttonCount + 1}`;\n    addNewButton({\n      id: crypto.randomUUID(),\n      name: \"Button 1\",\n      position: \"center\",\n      type: \"primary\",\n      isEditing: false,\n      index: 0,\n      style: {\n        backgroundColor: \"#5F9EA0\"\n      }\n    }, containerId);\n    // setButtonCount(buttonCount + 1);\n  };\n\n  // shouldShowAddBtn will be calculated per section inside the map\n\n  const currentContainerColor = ((_buttonsContainer$fin = buttonsContainer.find(item => item.id === currentContainerId)) === null || _buttonsContainer$fin === void 0 ? void 0 : (_buttonsContainer$fin2 = _buttonsContainer$fin.style) === null || _buttonsContainer$fin2 === void 0 ? void 0 : _buttonsContainer$fin2.backgroundColor) || \"#5f9ea0\";\n  const handleDelteContainer = () => {\n    // Store the container ID before clearing state\n    const containerToDelete = currentContainerId;\n\n    // Close popup immediately before deleting to prevent stale anchor references\n    handleClose();\n\n    // Clear all local state to prevent stale references\n    setCurrentContainerId(\"\");\n    setCurrentButtonId(\"\");\n\n    // Trigger global popup close to ensure all popups are closed\n    triggerCloseAllButtonPopups();\n\n    // Use setTimeout to ensure state updates are processed before deletion\n    setTimeout(() => {\n      // Delete the container\n      deleteButtonContainer(containerToDelete);\n\n      // Call the onDelete callback if provided\n      if (onDelete) {\n        onDelete();\n      }\n    }, 0);\n  };\n  setButtonId(currentButtonId);\n  setCuntainerId(currentButtonId);\n  const handleSettingIconClick = event => {\n    if (selectedTemplate === \"Banner\" || selectedTemplateTour === \"Banner\") {\n      setSettingAnchorEl({\n        containerId: currentContainerId,\n        buttonId: currentButtonId,\n        // @ts-ignore\n        value: event.currentTarget\n      });\n      setButtonProperty(true);\n    } else {\n      setSettingAnchorEl({\n        containerId: currentContainerId,\n        buttonId: currentButtonId,\n        // @ts-ignore\n        value: event.currentTarget\n      });\n    }\n    setAnchorEl(null);\n    setButtonToEdit(null);\n\n    //setAnchorEl(null);\n  };\n\n  // Determine which containers to use based on whether this is an AI announcement\n  const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n  const isTourAnnouncement = createWithAI && selectedTemplate === \"Tour\" && selectedTemplateTour === \"Announcement\";\n  const currentStepIndex = currentStep - 1;\n  let containersToRender = [];\n  if (isAIAnnouncement) {\n    // Use the store function to ensure Button containers exist\n    containersToRender = ensureAnnouncementButtonContainer(currentStepIndex, isTourAnnouncement);\n  } else {\n    // For banners and non-AI content, use buttonsContainer\n    containersToRender = buttonsContainer;\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [containersToRender.map(buttonItem => {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        component: \"div\",\n        id: buttonItem.id\n        // className={isBanner ? \"qadpt-banner-btn\" : \"\"}\n        ,\n        sx: {\n          height: isBanner ? \"40px !important\" : \"60px !important\",\n          width: \"100%\",\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: \"8px\",\n          padding: \"0 16px\",\n          boxSizing: \"border-box\",\n          backgroundColor: buttonItem.style.backgroundColor ? buttonItem.style.backgroundColor : btnBgColor,\n          justifyContent: \"center\",\n          // \"&.qadpt-banner-btn\": { height: \"40px\" },\n          \"&:hover .add-button-icon\": {\n            display: \"flex\"\n          }\n        },\n        onMouseEnter: e => setCurrentContainerId(e.currentTarget.id),\n        children: [buttonItem.buttons.map(item => /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: \"relative\",\n            display: \"flex\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            \"&:hover .delete-icon\": {\n              // Add this hover effect to display the delete icon when the button is hovered\n              opacity: 1\n            }\n          },\n          onMouseEnter: () => setCurrentContainerId(buttonItem.id),\n          onMouseLeave: () => setIsDeleteIcon(\"\"),\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            id: item.id,\n            variant: \"contained\",\n            sx: {\n              lineHeight: \"var(--button-lineheight)\",\n              padding: \"var(--button-padding)\",\n              borderRadius: \"8px\",\n              color: item.style.color || \"#fff\",\n              border: item.style.borderColor ? `2px solid ${item.style.borderColor}` : \"none\",\n              textTransform: \"none\",\n              backgroundColor: item.style.backgroundColor,\n              boxShadow: \"none\",\n              \"&:hover\": {\n                boxShadow: \"none !important\"\n              }\n            },\n            onClick: e => handleClick(e, item.id),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              style: {\n                color: btnTextColor\n              },\n              children: item.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 10\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 9\n          }, this), buttonItem.buttons.length > 1 && /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            className: \"delete-icon\",\n            sx: {\n              position: \"absolute\",\n              right: \"-10px\",\n              top: \"0\",\n              transform: \"translateY(-50%)\",\n              backgroundColor: \"#fff\",\n              boxShadow: \"rgba(0, 0, 0, 0.4) 0px 2px 6px\",\n              opacity: 0,\n              // Initially hidden\n              transition: \"opacity 0.3s ease\",\n              // Smooth transition\n              zIndex: 1,\n              padding: \"3px !important\",\n              \"&:hover\": {\n                backgroundColor: \"#fff\",\n                boxShadow: \"none !important\"\n              },\n              span: {\n                height: \"16px\"\n              },\n              \"& svg\": {\n                width: \"14px\",\n                // Set the width of the SVG\n                height: \"14px\",\n                // Set the height of the SVG\n                path: {\n                  fill: \"#ff0000\"\n                }\n              }\n            },\n            onClick: () => deleteButton(buttonItem.id, item.id),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: deleteicon\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 11\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 10\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 8\n        }, this)), (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\") && buttonItem.buttons.length < 4 ? /*#__PURE__*/_jsxDEV(IconButton, {\n          className: \"add-button-icon\",\n          sx: {\n            backgroundColor: \"#5F9EA0\",\n            cursor: \"pointer\",\n            zIndex: 1000,\n            padding: \"6px !important\",\n            display: \"none\",\n            \"&:hover\": {\n              backgroundColor: \"#70afaf\"\n            }\n          }\n          // sx={sideAddButtonStyle}\n          ,\n          onClick: () => {\n            handleAddIconClick(buttonItem.id);\n            if (onClone) {\n              onClone();\n            }\n            handleClose();\n          },\n          children: /*#__PURE__*/_jsxDEV(AddIcon, {\n            fontSize: \"small\",\n            sx: {\n              color: \"#fff\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 8\n        }, this) : null]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 6\n      }, this);\n    }), /*#__PURE__*/_jsxDEV(Popover, {\n      className: \"qadpt-bunprop\",\n      id: id,\n      open: open,\n      anchorEl: anchorEl,\n      onClose: handleClose,\n      anchorOrigin: {\n        vertical: \"top\",\n        horizontal: \"left\"\n      },\n      transformOrigin: {\n        vertical: \"bottom\",\n        horizontal: \"left\"\n      },\n      slotProps: {\n        root: {\n          sx: {\n            zIndex: theme => theme.zIndex.tooltip + 1000,\n            ...popupPosition\n          }\n        }\n      }\n      // className={isBanner ? \"qadpt-banner-btn\" : \"\"}\n      ,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: \"8px\",\n          padding: \"8px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Settings\"),\n          arrow: true,\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: handleSettingIconClick,\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: settingsicon\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 6\n        }, this), (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\") && /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Background Color\"),\n          arrow: true,\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: handleBackgroundColorClick,\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                backgroundColor: selectedColor,\n                borderRadius: \"100%\",\n                width: \"20px\",\n                height: \"20px\",\n                display: \"inline-block\",\n                marginTop: \"-3px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 7\n        }, this), (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\") && /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: isCloneDisabled ? translate(\"Maximum limit of 3 Button sections reached\") : translate(\"Clone Button\"),\n          arrow: true,\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: () => {\n              cloneButtonContainer(currentContainerId);\n              if (onClone) {\n                onClone();\n              }\n              handleClose();\n            },\n            disabled: isCloneDisabled,\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: copyicon\n              },\n              style: {\n                opacity: isCloneDisabled ? 0.5 : 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 7\n        }, this), (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\") && /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Delete Button\"),\n          arrow: true,\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: handleDelteContainer,\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: deleteicon\n              },\n              style: {\n                marginTop: \"-3px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 586,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 485,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 461,\n      columnNumber: 4\n    }, this), /*#__PURE__*/_jsxDEV(Popover, {\n      open: colorPickerOpen,\n      anchorEl: colorPickerAnchorEl,\n      onClose: handleCloseColorPicker,\n      anchorOrigin: {\n        vertical: \"bottom\",\n        horizontal: \"center\"\n      },\n      transformOrigin: {\n        vertical: \"top\",\n        horizontal: \"center\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(ChromePicker, {\n          color: currentContainerColor,\n          onChange: handleColorChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n          children: `\n      .chrome-picker input {\n        padding: 0 !important;\n      }\n    `\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 620,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 615,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 602,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true);\n};\n_s(ButtonSection, \"m8Xt94Ajsu5rgZ4CBX8S/cfNFKw=\", false, function () {\n  return [useDrawerStore, useTranslation];\n});\n_c = ButtonSection;\nexport default ButtonSection;\nvar _c;\n$RefreshReg$(_c, \"ButtonSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "<PERSON><PERSON>", "Popover", "Typography", "IconButton", "<PERSON><PERSON><PERSON>", "ChromePicker", "deleteicon", "copyicon", "settingsicon", "useDrawerStore", "AddIcon", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "getGuidePopupPosition", "element", "document", "querySelector", "getElementById", "rect", "getBoundingClientRect", "top", "left", "width", "height", "getButtonPropertiesPopupPosition", "guidePopupPos", "viewportWidth", "window", "innerWidth", "viewportHeight", "innerHeight", "popup<PERSON><PERSON><PERSON>", "popupHeight", "gap", "headerHeight", "bottomPadding", "topPadding", "idealTop", "minTop", "maxTop", "viewportCenteredTop", "Math", "max", "min", "rightPosition", "position", "leftPosition", "ButtonSection", "buttonColor", "setButtonColor", "isBanner", "isCloneDisabled", "onDelete", "onClone", "_s", "_buttonsContainer$fin", "_buttonsContainer$fin2", "buttonsContainer", "cloneButtonContainer", "updateButton", "addNewButton", "deleteButton", "deleteButtonContainer", "updateContainer", "setSettingAnchorEl", "selectedTemplate", "selectedTemplateTour", "setSelectedTemplate", "buttonProperty", "setButtonProperty", "btnBgColor", "btnBorderColor", "btnTextColor", "setButtonId", "setCuntainerId", "createWithAI", "currentStep", "ensureAnnouncementButtonContainer", "closeAllButtonPopups", "triggerCloseAllButtonPopups", "state", "t", "translate", "isEditingPrevious", "setIsEditingPrevious", "isEditingContinue", "setIsEditingContinue", "previousButtonText", "setPreviousButtonText", "continueButtonText", "setContinueButtonText", "anchorEl", "setAnchorEl", "selectedColor", "setSelectedColor", "colorPickerAnchorEl", "setColorPickerAnchorEl", "buttonText", "setButtonText", "buttonToEdit", "setButtonToEdit", "isDeleteIcon", "setIsDeleteIcon", "currentContainerId", "setCurrentContainerId", "currentButtonId", "setCurrentButtonId", "isEditingButton", "setIsEditingButton", "isEditing", "setIsEditing", "popupPosition", "setPopupPosition", "updatePosition", "newPosition", "addEventListener", "removeEventListener", "clickTimeout", "handleClick", "event", "buttonId", "target", "currentTarget", "setTimeout", "handleEditButtonName", "handleClose", "containerId", "value", "handlePreviousTextChange", "handleContinueTextChange", "handleBackgroundColorClick", "handleColorChange", "color", "hex", "backgroundColor", "handleCloseColorPicker", "open", "Boolean", "id", "undefined", "colorPickerOpen", "toggleEdit", "button", "handlePreviousBlur", "handleContinueBlur", "clearTimeout", "handleChangeButton", "handleEditButtonText", "newText", "handleAddIconClick", "crypto", "randomUUID", "name", "type", "index", "style", "currentContainerColor", "find", "item", "handleDelteContainer", "containerToDelete", "handleSettingIconClick", "isAIAnnouncement", "isTourAnnouncement", "currentStepIndex", "containersToRender", "children", "map", "buttonItem", "component", "sx", "display", "alignItems", "padding", "boxSizing", "justifyContent", "onMouseEnter", "e", "buttons", "opacity", "onMouseLeave", "variant", "lineHeight", "borderRadius", "border", "borderColor", "textTransform", "boxShadow", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "size", "className", "right", "transform", "transition", "zIndex", "span", "path", "fill", "dangerouslySetInnerHTML", "__html", "cursor", "fontSize", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "slotProps", "root", "theme", "tooltip", "title", "arrow", "marginTop", "disabled", "onChange", "_c", "$RefreshReg$"], "sources": ["E:/Code/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/Button.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { Box, Button, Popover, Typography, TextField, IconButton, Tooltip } from \"@mui/material\";\r\nimport { ChromePicker, ColorResult } from \"react-color\";\r\nimport { deleteicon, copyicon, settingsicon, backgroundcoloricon, editicon } from \"../../../assets/icons/icons\";\r\nimport useDrawerStore, { TButton } from \"../../../store/drawerStore\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport ButtonSettings from \"../../guideBanners/selectedpopupfields/ImageProperties\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\n// Function to get GuidePopUp position and dimensions\r\nconst getGuidePopupPosition = () => {\r\n\tconst element = document.querySelector('.qadpt-guide-popup .MuiDialog-paper') ||\r\n\t\t\t\t\tdocument.getElementById('guide-popup');\r\n\tif (element) {\r\n\t\tconst rect = element.getBoundingClientRect();\r\n\t\treturn {\r\n\t\t\ttop: rect.top,\r\n\t\t\tleft: rect.left,\r\n\t\t\twidth: rect.width,\r\n\t\t\theight: rect.height\r\n\t\t};\r\n\t}\r\n\treturn null;\r\n};\r\n\r\n// Function to calculate button properties popup position\r\nconst getButtonPropertiesPopupPosition = () => {\r\n\tconst guidePopupPos = getGuidePopupPosition();\r\n\r\n\tif (!guidePopupPos) {\r\n\t\treturn {};\r\n\t}\r\n\r\n\tconst viewportWidth = window.innerWidth;\r\n\tconst viewportHeight = window.innerHeight;\r\n\tconst popupWidth = 300; // Estimated width of button properties popup\r\n\tconst popupHeight = 200; // Estimated height of button properties popup\r\n\tconst gap = 15; // Required 15px gap\r\n\tconst headerHeight = 56;\r\n\tconst bottomPadding = 20;\r\n\tconst topPadding = 20;\r\n\r\n\t// Calculate optimal vertical position (vertically aligned to screen)\r\n\tconst idealTop = (viewportHeight - popupHeight) / 2;\r\n\tconst minTop = headerHeight + topPadding;\r\n\tconst maxTop = viewportHeight - popupHeight - bottomPadding;\r\n\tconst viewportCenteredTop = Math.max(minTop, Math.min(idealTop, maxTop));\r\n\r\n\t// Position to the right of the GuidePopUp with 15px gap\r\n\tconst rightPosition = guidePopupPos.left + guidePopupPos.width + gap;\r\n\r\n\t// Check if there's enough space on the right\r\n\tif (rightPosition + popupWidth <= viewportWidth - 10) {\r\n\t\treturn {\r\n\t\t\tposition: 'fixed',\r\n\t\t\ttop: `${viewportCenteredTop}px`,\r\n\t\t\tleft: `${rightPosition}px`,\r\n\t\t};\r\n\t} else {\r\n\t\t// If not enough space on right, position to the left\r\n\t\tconst leftPosition = guidePopupPos.left - popupWidth - gap;\r\n\t\treturn {\r\n\t\t\tposition: 'fixed',\r\n\t\t\ttop: `${viewportCenteredTop}px`,\r\n\t\t\tleft: `${Math.max(10, leftPosition)}px`,\r\n\t\t};\r\n\t}\r\n};\r\n\r\nconst ButtonSection: React.FC<{ buttonColor: string; setButtonColor: (str: string) => void; isBanner: boolean; index?: number; isCloneDisabled?: boolean; onDelete?: () => void; onClone?: () => void;}> = ({\r\n\tbuttonColor,\r\n\tsetButtonColor,\r\n\tisBanner,\r\n\tisCloneDisabled,\r\n\tonDelete,\r\n\tonClone\r\n}) => {\r\n\tconst {\r\n\t\tbuttonsContainer,\r\n\t\tcloneButtonContainer,\r\n\t\tupdateButton,\r\n\t\taddNewButton,\r\n\t\tdeleteButton,\r\n\t\tdeleteButtonContainer,\r\n\t\tupdateContainer,\r\n\t\tsetSettingAnchorEl,\r\n\t\tselectedTemplate,\r\n\t\tselectedTemplateTour,\r\n\t\tsetSelectedTemplate,\r\n\t\tbuttonProperty,\r\n\t\tsetButtonProperty,\r\n\t\tbtnBgColor,\r\n\t\tbtnBorderColor,\r\n\t\tbtnTextColor,\r\n\t\tsetButtonId,\r\n\t\tsetCuntainerId,\r\n\t\tcreateWithAI,\r\n\t\tcurrentStep,\r\n\t\tensureAnnouncementButtonContainer,\r\n\t\tcloseAllButtonPopups,\r\n\t\ttriggerCloseAllButtonPopups,\r\n\t} = useDrawerStore((state: any) => state);\r\n\tconst { t: translate } = useTranslation();\r\n\tconst [isEditingPrevious, setIsEditingPrevious] = useState<boolean>(false);\r\n\tconst [isEditingContinue, setIsEditingContinue] = useState<boolean>(false);\r\n\tconst [previousButtonText, setPreviousButtonText] = useState<string>(\"Previous\");\r\n\tconst [continueButtonText, setContinueButtonText] = useState<string>(\"Continue\");\r\n\tconst [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [selectedColor, setSelectedColor] = useState<string>(\"#313030\");\r\n\tconst [colorPickerAnchorEl, setColorPickerAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [buttonText, setButtonText] = useState<string>(\"Continue\");\r\n\tconst [buttonToEdit, setButtonToEdit] = useState<string | null>(null);\r\n\tconst [isDeleteIcon, setIsDeleteIcon] = useState(\"\");\r\n\tconst [currentContainerId, setCurrentContainerId] = useState(\"\");\r\n\tconst [currentButtonId, setCurrentButtonId] = useState(\"\");\r\n\tconst [isEditingButton, setIsEditingButton] = useState(false);\r\n\tconst [isEditing, setIsEditing] = useState<string | null>(null);\r\n\tconst [popupPosition, setPopupPosition] = useState({});\r\n\r\n\t// Update popup position when popup opens or window resizes\r\n\tuseEffect(() => {\r\n\t\tif (anchorEl) {\r\n\t\t\tconst updatePosition = () => {\r\n\t\t\t\tconst newPosition = getButtonPropertiesPopupPosition();\r\n\t\t\t\tsetPopupPosition(newPosition);\r\n\t\t\t};\r\n\r\n\t\t\tupdatePosition();\r\n\t\t\twindow.addEventListener('resize', updatePosition);\r\n\t\t\treturn () => window.removeEventListener('resize', updatePosition);\r\n\t\t}\r\n\t}, [anchorEl]);\r\n\r\n\t// Default button color\r\n\tlet clickTimeout: NodeJS.Timeout;\r\n\r\n\tuseEffect(() => {\r\n\t\tsetAnchorEl(null);\r\n\t\tsetButtonProperty(false);\r\n\t}, []); // Empty dependency array ensures it runs only once\r\n\r\n\t// Listen for global close all button popups trigger\r\n\tuseEffect(() => {\r\n\t\tif (closeAllButtonPopups > 0) {\r\n\t\t\tsetAnchorEl(null);\r\n\t\t\tsetColorPickerAnchorEl(null);\r\n\t\t\tsetButtonToEdit(null);\r\n\t\t}\r\n\t}, [closeAllButtonPopups]);\r\n\r\n\tconst handleClick = (event: React.MouseEvent<HTMLElement>, buttonId: string) => {\r\n\t\tconst target = event.currentTarget;\r\n\r\n\t\tclickTimeout = setTimeout(() => {\r\n\t\t\tsetAnchorEl(target);\r\n\t\t\tsetCurrentButtonId(buttonId);\r\n\t\t\tsetIsEditingButton(false);\r\n\t\t\thandleEditButtonName(currentContainerId, buttonId, \"isEditing\", false);\r\n\t\t}, 200);\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetAnchorEl(null);\r\n\t\tsetSettingAnchorEl({ containerId: \"\", buttonId: \"\", value: null });\r\n\t\tsetButtonToEdit(null);\r\n\t};\r\n\r\n\tconst handlePreviousTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tsetPreviousButtonText(event.target.value);\r\n\t};\r\n\r\n\tconst handleContinueTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tsetContinueButtonText(event.target.value);\r\n\t};\r\n\r\n\tconst handleBackgroundColorClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetColorPickerAnchorEl(event.currentTarget);\r\n\t\t// Close the main popup when opening color picker\r\n\t\thandleClose();\r\n\t};\r\n\r\n\tconst handleColorChange = (color: ColorResult) => {\r\n\t\tsetSelectedColor(color.hex);\r\n\t\t// Update the backgroundColor in the container's style\r\n\t\tupdateContainer(currentContainerId, \"style\", {\r\n\t\t\tbackgroundColor: color.hex,\r\n\t\t});\r\n\r\n\t\t// Also update the BackgroundColor property at the ButtonSection level\r\n\t\tupdateContainer(currentContainerId, \"BackgroundColor\", color.hex);\r\n\t};\r\n\r\n\tconst handleCloseColorPicker = () => {\r\n\t\tsetColorPickerAnchorEl(null);\r\n\t};\r\n\r\n\tconst open = Boolean(anchorEl);\r\n\t// const open = Boolean(anchorEl && !isEditingButton);\r\n\tconst id = open ? \"button-popover\" : undefined;\r\n\tconst colorPickerOpen = Boolean(colorPickerAnchorEl);\r\n\tconst toggleEdit = (button: \"Previous\" | \"Continue\") => {\r\n\t\tif (button === \"Previous\") {\r\n\t\t\tsetIsEditingPrevious(true);\r\n\t\t} else if (button === \"Continue\") {\r\n\t\t\tsetIsEditingContinue(true);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handlePreviousBlur = () => {\r\n\t\tsetIsEditingPrevious(false);\r\n\t};\r\n\r\n\tconst handleContinueBlur = () => {\r\n\t\tsetIsEditingContinue(false);\r\n\t};\r\n\tconst handleEditButtonName = (\r\n\t\tcontainerId: string,\r\n\t\tbuttonId: string,\r\n\t\tisEditing: keyof TButton,\r\n\t\tvalue: TButton[keyof TButton]\r\n\t) => {\r\n\t\tclearTimeout(clickTimeout);\r\n\t\tsetIsEditingButton(true);\r\n\t\tupdateButton(containerId, buttonId, isEditing, value);\r\n\t};\r\n\r\n\tconst handleChangeButton = (containerId: string, buttonId: string, value: TButton[keyof TButton]) => {\r\n\t\tupdateButton(containerId, buttonId, \"type\", value);\r\n\t};\r\n\tconst handleEditButtonText = (containerId: string, buttonId: string, newText: string) => {\r\n\t\tupdateButton(containerId, buttonId, \"name\", newText);\r\n\t\tsetButtonToEdit(null); // Exit edit mode after saving\r\n\t};\r\n\t//   const [buttonCount, setButtonCount] = useState(0);\r\n\tconst handleAddIconClick = (containerId: string) => {\r\n\t\t//  const buttonName = buttonCount === 0 ? \"Got It\" : `Button${buttonCount + 1}`;\r\n\t\taddNewButton(\r\n\t\t\t{\r\n\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\tname: \"Button 1\",\r\n\t\t\t\tposition: \"center\",\r\n\t\t\t\ttype: \"primary\",\r\n\t\t\t\tisEditing: false,\r\n\t\t\t\tindex: 0,\r\n\t\t\t\tstyle: {\r\n\t\t\t\t\tbackgroundColor: \"#5F9EA0\",\r\n\t\t\t\t},\r\n\t\t\t},\r\n\t\t\tcontainerId\r\n\t\t);\r\n\t\t// setButtonCount(buttonCount + 1);\r\n\t};\r\n\r\n\t// shouldShowAddBtn will be calculated per section inside the map\r\n\r\n\tconst currentContainerColor =\r\n\t\tbuttonsContainer.find((item: any) => item.id === currentContainerId)?.style?.backgroundColor || \"#5f9ea0\";\r\n\r\n\tconst handleDelteContainer = () => {\r\n\t\t// Store the container ID before clearing state\r\n\t\tconst containerToDelete = currentContainerId;\r\n\r\n\t\t// Close popup immediately before deleting to prevent stale anchor references\r\n\t\thandleClose();\r\n\r\n\t\t// Clear all local state to prevent stale references\r\n\t\tsetCurrentContainerId(\"\");\r\n\t\tsetCurrentButtonId(\"\");\r\n\r\n\t\t// Trigger global popup close to ensure all popups are closed\r\n\t\ttriggerCloseAllButtonPopups();\r\n\r\n\t\t// Use setTimeout to ensure state updates are processed before deletion\r\n\t\tsetTimeout(() => {\r\n\t\t\t// Delete the container\r\n\t\t\tdeleteButtonContainer(containerToDelete);\r\n\r\n\t\t\t// Call the onDelete callback if provided\r\n\t\t\tif (onDelete) {\r\n\t\t\t\tonDelete();\r\n\t\t\t}\r\n\t\t}, 0);\r\n\t};\r\n\tsetButtonId(currentButtonId);\r\n\tsetCuntainerId(currentButtonId);\r\n\r\n\tconst handleSettingIconClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tif (selectedTemplate === \"Banner\" || selectedTemplateTour === \"Banner\") {\r\n\t\t\tsetSettingAnchorEl({\r\n\t\t\t\tcontainerId: currentContainerId,\r\n\t\t\t\tbuttonId: currentButtonId,\r\n\t\t\t\t// @ts-ignore\r\n\t\t\t\tvalue: event.currentTarget,\r\n\t\t\t});\r\n\t\t\tsetButtonProperty(true);\r\n\t\t} else {\r\n\t\t\tsetSettingAnchorEl({\r\n\t\t\t\tcontainerId: currentContainerId,\r\n\t\t\t\tbuttonId: currentButtonId,\r\n\t\t\t\t// @ts-ignore\r\n\t\t\t\tvalue: event.currentTarget,\r\n\t\t\t});\r\n\t\t}\r\n\t\tsetAnchorEl(null);\r\n\t\tsetButtonToEdit(null);\r\n\r\n\t\t//setAnchorEl(null);\r\n\t};\r\n\r\n\t// Determine which containers to use based on whether this is an AI announcement\r\n\tconst isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n\tconst isTourAnnouncement = createWithAI && selectedTemplate === \"Tour\" && selectedTemplateTour === \"Announcement\";\r\n\tconst currentStepIndex = currentStep - 1;\r\n\r\n\tlet containersToRender: any[] = [];\r\n\r\n\tif (isAIAnnouncement) {\r\n\t\t// Use the store function to ensure Button containers exist\r\n\t\tcontainersToRender = ensureAnnouncementButtonContainer(currentStepIndex, isTourAnnouncement);\r\n\t} else {\r\n\t\t// For banners and non-AI content, use buttonsContainer\r\n\t\tcontainersToRender = buttonsContainer;\r\n\t}\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{containersToRender.map((buttonItem: any) => {\r\n\t\t\t\treturn (\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tcomponent=\"div\"\r\n\t\t\t\t\t\tid={buttonItem.id}\r\n\t\t\t\t\t\t// className={isBanner ? \"qadpt-banner-btn\" : \"\"}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\theight: isBanner ? \"40px !important\" : \"60px !important\",\r\n\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\tgap: \"8px\",\r\n\t\t\t\t\t\t\tpadding: \"0 16px\",\r\n\t\t\t\t\t\t\tboxSizing: \"border-box\",\r\n\t\t\t\t\t\t\tbackgroundColor: buttonItem.style.backgroundColor ? buttonItem.style.backgroundColor : btnBgColor,\r\n\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t// \"&.qadpt-banner-btn\": { height: \"40px\" },\r\n\t\t\t\t\t\t\t\"&:hover .add-button-icon\": {\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tonMouseEnter={(e) => setCurrentContainerId(e.currentTarget.id)}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{buttonItem.buttons.map((item: any) => (\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tkey={item.id}\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\"&:hover .delete-icon\": {\r\n\t\t\t\t\t\t\t\t\t\t// Add this hover effect to display the delete icon when the button is hovered\r\n\t\t\t\t\t\t\t\t\t\topacity: 1,\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonMouseEnter={() => setCurrentContainerId(buttonItem.id)}\r\n\t\t\t\t\t\t\t\tonMouseLeave={() => setIsDeleteIcon(\"\")}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tid={item.id}\r\n\t\t\t\t\t\t\t\t\tvariant={\"contained\"}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tlineHeight: \"var(--button-lineheight)\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"var(--button-padding)\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\tcolor: item.style.color || \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\tborder: item.style.borderColor\r\n\t\t\t\t\t\t\t\t\t\t\t? `2px solid ${item.style.borderColor}`\r\n\t\t\t\t\t\t\t\t\t\t\t: \"none\",\r\n\t\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: item.style.backgroundColor,\r\n\t\t\t\t\t\t\t\t\t\tboxShadow: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\",\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\tonClick={(e) => handleClick(e, item.id)}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Typography style={{ color: btnTextColor }}>{item.name}</Typography>\r\n\t\t\t\t\t\t\t\t</Button>\r\n\r\n\t\t\t\t\t\t\t\t{/* Delete Icon - Right Side, only visible on hover */}\r\n\t\t\t\t\t\t\t\t{buttonItem.buttons.length > 1 && (\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"delete-icon\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\t\t\t\t\tright: \"-10px\",\r\n\t\t\t\t\t\t\t\t\t\t\ttop: \"0\",\r\n\t\t\t\t\t\t\t\t\t\t\ttransform: \"translateY(-50%)\",\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.4) 0px 2px 6px\",\r\n\t\t\t\t\t\t\t\t\t\t\topacity: 0, // Initially hidden\r\n\t\t\t\t\t\t\t\t\t\t\ttransition: \"opacity 0.3s ease\", // Smooth transition\r\n\t\t\t\t\t\t\t\t\t\t\tzIndex: 1,\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"3px !important\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\",\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\tspan: {\r\n\t\t\t\t\t\t\t\t\t\t\t\theight: \"16px\"\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\"& svg\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: \"14px\", // Set the width of the SVG\r\n\t\t\t\t\t\t\t\t\t\t\t\theight: \"14px\", // Set the height of the SVG\r\n\t\t\t\t\t\t\t\t\t\t\t\tpath: {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tfill:\"#ff0000\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => deleteButton(buttonItem.id, item.id)}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: deleteicon }} />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t))}\r\n\r\n\t\t\t\t\t\t{(selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\") &&\r\n\t\t\t\t\t\tbuttonItem.buttons.length < 4  ? (\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tclassName=\"add-button-icon\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"#5F9EA0\",\r\n\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\tzIndex: 1000,\r\n\t\t\t\t\t\t\t\t\tpadding: \"6px !important\",\r\n\t\t\t\t\t\t\t\t\tdisplay: \"none\",\r\n\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#70afaf\",\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t// sx={sideAddButtonStyle}\r\n\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\thandleAddIconClick(buttonItem.id);\r\n\t\t\t\t\t\t\t\t\tif (onClone) {\r\n\t\t\t\t\t\t\t\t\t\tonClone();\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\thandleClose();\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<AddIcon\r\n\t\t\t\t\t\t\t\t\tfontSize=\"small\"\r\n\t\t\t\t\t\t\t\t\tsx={{ color: \"#fff\" }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t) : null}\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t);\r\n\t\t\t})}\r\n\t\t\t<Popover\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-bunprop\"\r\n\t\t\t\tid={id}\r\n\t\t\t\topen={open}\r\n\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\tonClose={handleClose}\r\n\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\thorizontal: \"left\",\r\n\t\t\t\t}}\r\n\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\thorizontal: \"left\",\r\n\t\t\t\t}}\r\n\t\t\t\tslotProps={{\r\n\t\t\t\t\troot: {\r\n\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\tzIndex: (theme) => theme.zIndex.tooltip + 1000,\r\n\t\t\t\t\t\t\t...popupPosition,\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t\t// className={isBanner ? \"qadpt-banner-btn\" : \"\"}\r\n\t\t\t>\r\n\t\t\t\t<Box\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\tgap: \"8px\",\r\n\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t{/* <Tooltip\r\n\t\t\t\ttitle=\"Coming Soon\"\r\n\t\t\t\tPopperProps={{\r\n\t\t\t\t\tsx: {\r\n\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<span>  */}\r\n\t\t\t\t\t{/* <Typography\r\n\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\tsx={{ cursor: \"pointer\", fontWeight: \"bold\", opacity: 0.5 }}\r\n\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\tid=\"primary\"\r\n\t\t\t\t\t\tonClick={(e) => handleChangeButton(currentContainerId, currentButtonId, e.currentTarget.id)}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\tPrimary\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t\t{/* </span>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\ttitle=\"Coming Soon\"\r\n\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\tsx={{ cursor: \"pointer\", color: \"gray\", opacity: 0.5 }}\r\n\t\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\t\tid=\"secondary\"\r\n\t\t\t\t\t\t\t\t//onClick={(e) => handleChangeButton(currentContainerId, currentButtonId, e.currentTarget.id)}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tSecondary\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t</span>\r\n\t\t\t\t\t</Tooltip>\r\n\r\n\t\t\t\t\t<Box sx={{ borderLeft: \"1px solid #ccc\", height: \"24px\", marginLeft: \"8px\" }}></Box>\r\n\t\t\t\t\t*/}\r\n\r\n\t\t\t\t\t{/* Icons for additional options */}\r\n\t\t\t\t\t<Tooltip title={translate(\"Settings\")} arrow>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tonClick={handleSettingIconClick}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: settingsicon }} />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t{(selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\") && (\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Background Color\")} arrow>\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tonClick={handleBackgroundColorClick}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: selectedColor,\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"inline-block\",\r\n\t\t\t\t\t\t\t\t\t\tmarginTop: \"-3px\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t{(selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\") && (\r\n\t\t\t\t\t\t<Tooltip title={isCloneDisabled ? translate(\"Maximum limit of 3 Button sections reached\") : translate(\"Clone Button\")} arrow>\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\tcloneButtonContainer(currentContainerId);\r\n\t\t\t\t\t\t\t\t\tif (onClone) {\r\n\t\t\t\t\t\t\t\t\t\tonClone();\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\thandleClose();\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tdisabled={isCloneDisabled}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: copyicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ opacity: isCloneDisabled ? 0.5 : 1 }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t{(selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\") && (\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Delete Button\")} arrow>\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tonClick={handleDelteContainer}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: deleteicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ marginTop: \"-3px\" }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</Box>\r\n\t\t\t</Popover>\r\n\r\n\t\t\t{/* Color Picker Popover */}\r\n\t\t\t<Popover\r\n\t\t\t\topen={colorPickerOpen}\r\n\t\t\t\tanchorEl={colorPickerAnchorEl}\r\n\t\t\t\tonClose={handleCloseColorPicker}\r\n\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<ChromePicker\r\n\t\t\t\t\t\tcolor={currentContainerColor}\r\n\t\t\t\t\t\tonChange={handleColorChange}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t<style>\r\n\t\t\t\t\t\t{`\r\n      .chrome-picker input {\r\n        padding: 0 !important;\r\n      }\r\n    `}\r\n\t\t\t\t\t</style>\r\n\t\t\t\t</Box>\r\n\t\t\t</Popover>\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default ButtonSection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAEC,UAAU,EAAaC,UAAU,EAAEC,OAAO,QAAQ,eAAe;AAChG,SAASC,YAAY,QAAqB,aAAa;AACvD,SAASC,UAAU,EAAEC,QAAQ,EAAEC,YAAY,QAAuC,6BAA6B;AAC/G,OAAOC,cAAc,MAAmB,4BAA4B;AACpE,OAAOC,OAAO,MAAM,yBAAyB;AAG7C,SAASC,cAAc,QAAQ,eAAe;;AAE9C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EACnC,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,qCAAqC,CAAC,IACzED,QAAQ,CAACE,cAAc,CAAC,aAAa,CAAC;EAC1C,IAAIH,OAAO,EAAE;IACZ,MAAMI,IAAI,GAAGJ,OAAO,CAACK,qBAAqB,CAAC,CAAC;IAC5C,OAAO;MACNC,GAAG,EAAEF,IAAI,CAACE,GAAG;MACbC,IAAI,EAAEH,IAAI,CAACG,IAAI;MACfC,KAAK,EAAEJ,IAAI,CAACI,KAAK;MACjBC,MAAM,EAAEL,IAAI,CAACK;IACd,CAAC;EACF;EACA,OAAO,IAAI;AACZ,CAAC;;AAED;AACA,MAAMC,gCAAgC,GAAGA,CAAA,KAAM;EAC9C,MAAMC,aAAa,GAAGZ,qBAAqB,CAAC,CAAC;EAE7C,IAAI,CAACY,aAAa,EAAE;IACnB,OAAO,CAAC,CAAC;EACV;EAEA,MAAMC,aAAa,GAAGC,MAAM,CAACC,UAAU;EACvC,MAAMC,cAAc,GAAGF,MAAM,CAACG,WAAW;EACzC,MAAMC,UAAU,GAAG,GAAG,CAAC,CAAC;EACxB,MAAMC,WAAW,GAAG,GAAG,CAAC,CAAC;EACzB,MAAMC,GAAG,GAAG,EAAE,CAAC,CAAC;EAChB,MAAMC,YAAY,GAAG,EAAE;EACvB,MAAMC,aAAa,GAAG,EAAE;EACxB,MAAMC,UAAU,GAAG,EAAE;;EAErB;EACA,MAAMC,QAAQ,GAAG,CAACR,cAAc,GAAGG,WAAW,IAAI,CAAC;EACnD,MAAMM,MAAM,GAAGJ,YAAY,GAAGE,UAAU;EACxC,MAAMG,MAAM,GAAGV,cAAc,GAAGG,WAAW,GAAGG,aAAa;EAC3D,MAAMK,mBAAmB,GAAGC,IAAI,CAACC,GAAG,CAACJ,MAAM,EAAEG,IAAI,CAACE,GAAG,CAACN,QAAQ,EAAEE,MAAM,CAAC,CAAC;;EAExE;EACA,MAAMK,aAAa,GAAGnB,aAAa,CAACJ,IAAI,GAAGI,aAAa,CAACH,KAAK,GAAGW,GAAG;;EAEpE;EACA,IAAIW,aAAa,GAAGb,UAAU,IAAIL,aAAa,GAAG,EAAE,EAAE;IACrD,OAAO;MACNmB,QAAQ,EAAE,OAAO;MACjBzB,GAAG,EAAE,GAAGoB,mBAAmB,IAAI;MAC/BnB,IAAI,EAAE,GAAGuB,aAAa;IACvB,CAAC;EACF,CAAC,MAAM;IACN;IACA,MAAME,YAAY,GAAGrB,aAAa,CAACJ,IAAI,GAAGU,UAAU,GAAGE,GAAG;IAC1D,OAAO;MACNY,QAAQ,EAAE,OAAO;MACjBzB,GAAG,EAAE,GAAGoB,mBAAmB,IAAI;MAC/BnB,IAAI,EAAE,GAAGoB,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEI,YAAY,CAAC;IACpC,CAAC;EACF;AACD,CAAC;AAED,MAAMC,aAAkM,GAAGA,CAAC;EAC3MC,WAAW;EACXC,cAAc;EACdC,QAAQ;EACRC,eAAe;EACfC,QAAQ;EACRC;AACD,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EACL,MAAM;IACLC,gBAAgB;IAChBC,oBAAoB;IACpBC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,qBAAqB;IACrBC,eAAe;IACfC,kBAAkB;IAClBC,gBAAgB;IAChBC,oBAAoB;IACpBC,mBAAmB;IACnBC,cAAc;IACdC,iBAAiB;IACjBC,UAAU;IACVC,cAAc;IACdC,YAAY;IACZC,WAAW;IACXC,cAAc;IACdC,YAAY;IACZC,WAAW;IACXC,iCAAiC;IACjCC,oBAAoB;IACpBC;EACD,CAAC,GAAGzE,cAAc,CAAE0E,KAAU,IAAKA,KAAK,CAAC;EACzC,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAG1E,cAAc,CAAC,CAAC;EACzC,MAAM,CAAC2E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1F,QAAQ,CAAU,KAAK,CAAC;EAC1E,MAAM,CAAC2F,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5F,QAAQ,CAAU,KAAK,CAAC;EAC1E,MAAM,CAAC6F,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9F,QAAQ,CAAS,UAAU,CAAC;EAChF,MAAM,CAAC+F,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhG,QAAQ,CAAS,UAAU,CAAC;EAChF,MAAM,CAACiG,QAAQ,EAAEC,WAAW,CAAC,GAAGlG,QAAQ,CAAqB,IAAI,CAAC;EAClE,MAAM,CAACmG,aAAa,EAAEC,gBAAgB,CAAC,GAAGpG,QAAQ,CAAS,SAAS,CAAC;EACrE,MAAM,CAACqG,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGtG,QAAQ,CAAqB,IAAI,CAAC;EACxF,MAAM,CAACuG,UAAU,EAAEC,aAAa,CAAC,GAAGxG,QAAQ,CAAS,UAAU,CAAC;EAChE,MAAM,CAACyG,YAAY,EAAEC,eAAe,CAAC,GAAG1G,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAAC2G,YAAY,EAAEC,eAAe,CAAC,GAAG5G,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6G,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAAC+G,eAAe,EAAEC,kBAAkB,CAAC,GAAGhH,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACiH,eAAe,EAAEC,kBAAkB,CAAC,GAAGlH,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACmH,SAAS,EAAEC,YAAY,CAAC,GAAGpH,QAAQ,CAAgB,IAAI,CAAC;EAC/D,MAAM,CAACqH,aAAa,EAAEC,gBAAgB,CAAC,GAAGtH,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEtD;EACAC,SAAS,CAAC,MAAM;IACf,IAAIgG,QAAQ,EAAE;MACb,MAAMsB,cAAc,GAAGA,CAAA,KAAM;QAC5B,MAAMC,WAAW,GAAG1F,gCAAgC,CAAC,CAAC;QACtDwF,gBAAgB,CAACE,WAAW,CAAC;MAC9B,CAAC;MAEDD,cAAc,CAAC,CAAC;MAChBtF,MAAM,CAACwF,gBAAgB,CAAC,QAAQ,EAAEF,cAAc,CAAC;MACjD,OAAO,MAAMtF,MAAM,CAACyF,mBAAmB,CAAC,QAAQ,EAAEH,cAAc,CAAC;IAClE;EACD,CAAC,EAAE,CAACtB,QAAQ,CAAC,CAAC;;EAEd;EACA,IAAI0B,YAA4B;EAEhC1H,SAAS,CAAC,MAAM;IACfiG,WAAW,CAAC,IAAI,CAAC;IACjBvB,iBAAiB,CAAC,KAAK,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACA1E,SAAS,CAAC,MAAM;IACf,IAAImF,oBAAoB,GAAG,CAAC,EAAE;MAC7Bc,WAAW,CAAC,IAAI,CAAC;MACjBI,sBAAsB,CAAC,IAAI,CAAC;MAC5BI,eAAe,CAAC,IAAI,CAAC;IACtB;EACD,CAAC,EAAE,CAACtB,oBAAoB,CAAC,CAAC;EAE1B,MAAMwC,WAAW,GAAGA,CAACC,KAAoC,EAAEC,QAAgB,KAAK;IAC/E,MAAMC,MAAM,GAAGF,KAAK,CAACG,aAAa;IAElCL,YAAY,GAAGM,UAAU,CAAC,MAAM;MAC/B/B,WAAW,CAAC6B,MAAM,CAAC;MACnBf,kBAAkB,CAACc,QAAQ,CAAC;MAC5BZ,kBAAkB,CAAC,KAAK,CAAC;MACzBgB,oBAAoB,CAACrB,kBAAkB,EAAEiB,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC;IACvE,CAAC,EAAE,GAAG,CAAC;EACR,CAAC;EAED,MAAMK,WAAW,GAAGA,CAAA,KAAM;IACzBjC,WAAW,CAAC,IAAI,CAAC;IACjB5B,kBAAkB,CAAC;MAAE8D,WAAW,EAAE,EAAE;MAAEN,QAAQ,EAAE,EAAE;MAAEO,KAAK,EAAE;IAAK,CAAC,CAAC;IAClE3B,eAAe,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAM4B,wBAAwB,GAAIT,KAA0C,IAAK;IAChF/B,qBAAqB,CAAC+B,KAAK,CAACE,MAAM,CAACM,KAAK,CAAC;EAC1C,CAAC;EAED,MAAME,wBAAwB,GAAIV,KAA0C,IAAK;IAChF7B,qBAAqB,CAAC6B,KAAK,CAACE,MAAM,CAACM,KAAK,CAAC;EAC1C,CAAC;EAED,MAAMG,0BAA0B,GAAIX,KAAoC,IAAK;IAC5EvB,sBAAsB,CAACuB,KAAK,CAACG,aAAa,CAAC;IAC3C;IACAG,WAAW,CAAC,CAAC;EACd,CAAC;EAED,MAAMM,iBAAiB,GAAIC,KAAkB,IAAK;IACjDtC,gBAAgB,CAACsC,KAAK,CAACC,GAAG,CAAC;IAC3B;IACAtE,eAAe,CAACwC,kBAAkB,EAAE,OAAO,EAAE;MAC5C+B,eAAe,EAAEF,KAAK,CAACC;IACxB,CAAC,CAAC;;IAEF;IACAtE,eAAe,CAACwC,kBAAkB,EAAE,iBAAiB,EAAE6B,KAAK,CAACC,GAAG,CAAC;EAClE,CAAC;EAED,MAAME,sBAAsB,GAAGA,CAAA,KAAM;IACpCvC,sBAAsB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMwC,IAAI,GAAGC,OAAO,CAAC9C,QAAQ,CAAC;EAC9B;EACA,MAAM+C,EAAE,GAAGF,IAAI,GAAG,gBAAgB,GAAGG,SAAS;EAC9C,MAAMC,eAAe,GAAGH,OAAO,CAAC1C,mBAAmB,CAAC;EACpD,MAAM8C,UAAU,GAAIC,MAA+B,IAAK;IACvD,IAAIA,MAAM,KAAK,UAAU,EAAE;MAC1B1D,oBAAoB,CAAC,IAAI,CAAC;IAC3B,CAAC,MAAM,IAAI0D,MAAM,KAAK,UAAU,EAAE;MACjCxD,oBAAoB,CAAC,IAAI,CAAC;IAC3B;EACD,CAAC;EAED,MAAMyD,kBAAkB,GAAGA,CAAA,KAAM;IAChC3D,oBAAoB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAM4D,kBAAkB,GAAGA,CAAA,KAAM;IAChC1D,oBAAoB,CAAC,KAAK,CAAC;EAC5B,CAAC;EACD,MAAMsC,oBAAoB,GAAGA,CAC5BE,WAAmB,EACnBN,QAAgB,EAChBX,SAAwB,EACxBkB,KAA6B,KACzB;IACJkB,YAAY,CAAC5B,YAAY,CAAC;IAC1BT,kBAAkB,CAAC,IAAI,CAAC;IACxBjD,YAAY,CAACmE,WAAW,EAAEN,QAAQ,EAAEX,SAAS,EAAEkB,KAAK,CAAC;EACtD,CAAC;EAED,MAAMmB,kBAAkB,GAAGA,CAACpB,WAAmB,EAAEN,QAAgB,EAAEO,KAA6B,KAAK;IACpGpE,YAAY,CAACmE,WAAW,EAAEN,QAAQ,EAAE,MAAM,EAAEO,KAAK,CAAC;EACnD,CAAC;EACD,MAAMoB,oBAAoB,GAAGA,CAACrB,WAAmB,EAAEN,QAAgB,EAAE4B,OAAe,KAAK;IACxFzF,YAAY,CAACmE,WAAW,EAAEN,QAAQ,EAAE,MAAM,EAAE4B,OAAO,CAAC;IACpDhD,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;EACxB,CAAC;EACD;EACA,MAAMiD,kBAAkB,GAAIvB,WAAmB,IAAK;IACnD;IACAlE,YAAY,CACX;MACC8E,EAAE,EAAEY,MAAM,CAACC,UAAU,CAAC,CAAC;MACvBC,IAAI,EAAE,UAAU;MAChB3G,QAAQ,EAAE,QAAQ;MAClB4G,IAAI,EAAE,SAAS;MACf5C,SAAS,EAAE,KAAK;MAChB6C,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE;QACNrB,eAAe,EAAE;MAClB;IACD,CAAC,EACDR,WACD,CAAC;IACD;EACD,CAAC;;EAED;;EAEA,MAAM8B,qBAAqB,GAC1B,EAAArG,qBAAA,GAAAE,gBAAgB,CAACoG,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACpB,EAAE,KAAKnC,kBAAkB,CAAC,cAAAhD,qBAAA,wBAAAC,sBAAA,GAApED,qBAAA,CAAsEoG,KAAK,cAAAnG,sBAAA,uBAA3EA,sBAAA,CAA6E8E,eAAe,KAAI,SAAS;EAE1G,MAAMyB,oBAAoB,GAAGA,CAAA,KAAM;IAClC;IACA,MAAMC,iBAAiB,GAAGzD,kBAAkB;;IAE5C;IACAsB,WAAW,CAAC,CAAC;;IAEb;IACArB,qBAAqB,CAAC,EAAE,CAAC;IACzBE,kBAAkB,CAAC,EAAE,CAAC;;IAEtB;IACA3B,2BAA2B,CAAC,CAAC;;IAE7B;IACA4C,UAAU,CAAC,MAAM;MAChB;MACA7D,qBAAqB,CAACkG,iBAAiB,CAAC;;MAExC;MACA,IAAI5G,QAAQ,EAAE;QACbA,QAAQ,CAAC,CAAC;MACX;IACD,CAAC,EAAE,CAAC,CAAC;EACN,CAAC;EACDqB,WAAW,CAACgC,eAAe,CAAC;EAC5B/B,cAAc,CAAC+B,eAAe,CAAC;EAE/B,MAAMwD,sBAAsB,GAAI1C,KAAoC,IAAK;IACxE,IAAItD,gBAAgB,KAAK,QAAQ,IAAIC,oBAAoB,KAAK,QAAQ,EAAE;MACvEF,kBAAkB,CAAC;QAClB8D,WAAW,EAAEvB,kBAAkB;QAC/BiB,QAAQ,EAAEf,eAAe;QACzB;QACAsB,KAAK,EAAER,KAAK,CAACG;MACd,CAAC,CAAC;MACFrD,iBAAiB,CAAC,IAAI,CAAC;IACxB,CAAC,MAAM;MACNL,kBAAkB,CAAC;QAClB8D,WAAW,EAAEvB,kBAAkB;QAC/BiB,QAAQ,EAAEf,eAAe;QACzB;QACAsB,KAAK,EAAER,KAAK,CAACG;MACd,CAAC,CAAC;IACH;IACA9B,WAAW,CAAC,IAAI,CAAC;IACjBQ,eAAe,CAAC,IAAI,CAAC;;IAErB;EACD,CAAC;;EAED;EACA,MAAM8D,gBAAgB,GAAGvF,YAAY,KAAKV,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;EACzH,MAAMiG,kBAAkB,GAAGxF,YAAY,IAAIV,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,cAAc;EACjH,MAAMkG,gBAAgB,GAAGxF,WAAW,GAAG,CAAC;EAExC,IAAIyF,kBAAyB,GAAG,EAAE;EAElC,IAAIH,gBAAgB,EAAE;IACrB;IACAG,kBAAkB,GAAGxF,iCAAiC,CAACuF,gBAAgB,EAAED,kBAAkB,CAAC;EAC7F,CAAC,MAAM;IACN;IACAE,kBAAkB,GAAG5G,gBAAgB;EACtC;EAEA,oBACC/C,OAAA,CAAAE,SAAA;IAAA0J,QAAA,GACED,kBAAkB,CAACE,GAAG,CAAEC,UAAe,IAAK;MAC5C,oBACC9J,OAAA,CAACd,GAAG;QACH6K,SAAS,EAAC,KAAK;QACf/B,EAAE,EAAE8B,UAAU,CAAC9B;QACf;QAAA;QACAgC,EAAE,EAAE;UACHnJ,MAAM,EAAE2B,QAAQ,GAAG,iBAAiB,GAAG,iBAAiB;UACxD5B,KAAK,EAAE,MAAM;UACbqJ,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpB3I,GAAG,EAAE,KAAK;UACV4I,OAAO,EAAE,QAAQ;UACjBC,SAAS,EAAE,YAAY;UACvBxC,eAAe,EAAEkC,UAAU,CAACb,KAAK,CAACrB,eAAe,GAAGkC,UAAU,CAACb,KAAK,CAACrB,eAAe,GAAGhE,UAAU;UACjGyG,cAAc,EAAE,QAAQ;UACxB;UACA,0BAA0B,EAAE;YAC3BJ,OAAO,EAAE;UACV;QACD,CAAE;QACFK,YAAY,EAAGC,CAAC,IAAKzE,qBAAqB,CAACyE,CAAC,CAACvD,aAAa,CAACgB,EAAE,CAAE;QAAA4B,QAAA,GAE9DE,UAAU,CAACU,OAAO,CAACX,GAAG,CAAET,IAAS,iBACjCpJ,OAAA,CAACd,GAAG;UAEH8K,EAAE,EAAE;YACH7H,QAAQ,EAAE,UAAU;YACpB8H,OAAO,EAAE,MAAM;YACfI,cAAc,EAAE,QAAQ;YACxBH,UAAU,EAAE,QAAQ;YACpB,sBAAsB,EAAE;cACvB;cACAO,OAAO,EAAE;YACV;UACD,CAAE;UACFH,YAAY,EAAEA,CAAA,KAAMxE,qBAAqB,CAACgE,UAAU,CAAC9B,EAAE,CAAE;UACzD0C,YAAY,EAAEA,CAAA,KAAM9E,eAAe,CAAC,EAAE,CAAE;UAAAgE,QAAA,gBAExC5J,OAAA,CAACb,MAAM;YACN6I,EAAE,EAAEoB,IAAI,CAACpB,EAAG;YACZ2C,OAAO,EAAE,WAAY;YACrBX,EAAE,EAAE;cACHY,UAAU,EAAE,0BAA0B;cACtCT,OAAO,EAAE,uBAAuB;cAChCU,YAAY,EAAE,KAAK;cACnBnD,KAAK,EAAE0B,IAAI,CAACH,KAAK,CAACvB,KAAK,IAAI,MAAM;cACjCoD,MAAM,EAAE1B,IAAI,CAACH,KAAK,CAAC8B,WAAW,GAC3B,aAAa3B,IAAI,CAACH,KAAK,CAAC8B,WAAW,EAAE,GACrC,MAAM;cACTC,aAAa,EAAE,MAAM;cACrBpD,eAAe,EAAEwB,IAAI,CAACH,KAAK,CAACrB,eAAe;cAC3CqD,SAAS,EAAE,MAAM;cACjB,SAAS,EAAE;gBACVA,SAAS,EAAE;cACZ;YACD,CAAE;YACFC,OAAO,EAAGX,CAAC,IAAK3D,WAAW,CAAC2D,CAAC,EAAEnB,IAAI,CAACpB,EAAE,CAAE;YAAA4B,QAAA,eAExC5J,OAAA,CAACX,UAAU;cAAC4J,KAAK,EAAE;gBAAEvB,KAAK,EAAE5D;cAAa,CAAE;cAAA8F,QAAA,EAAER,IAAI,CAACN;YAAI;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,EAGRxB,UAAU,CAACU,OAAO,CAACe,MAAM,GAAG,CAAC,iBAC7BvL,OAAA,CAACV,UAAU;YACVkM,IAAI,EAAC,OAAO;YACZC,SAAS,EAAC,aAAa;YACvBzB,EAAE,EAAE;cACH7H,QAAQ,EAAE,UAAU;cACpBuJ,KAAK,EAAE,OAAO;cACdhL,GAAG,EAAE,GAAG;cACRiL,SAAS,EAAE,kBAAkB;cAC7B/D,eAAe,EAAE,MAAM;cACvBqD,SAAS,EAAE,gCAAgC;cAC3CR,OAAO,EAAE,CAAC;cAAE;cACZmB,UAAU,EAAE,mBAAmB;cAAE;cACjCC,MAAM,EAAE,CAAC;cACT1B,OAAO,EAAE,gBAAgB;cACzB,SAAS,EAAE;gBACVvC,eAAe,EAAE,MAAM;gBACvBqD,SAAS,EAAE;cACZ,CAAC;cACDa,IAAI,EAAE;gBACLjL,MAAM,EAAE;cACT,CAAC;cACD,OAAO,EAAE;gBACRD,KAAK,EAAE,MAAM;gBAAE;gBACfC,MAAM,EAAE,MAAM;gBAAE;gBAChBkL,IAAI,EAAE;kBACLC,IAAI,EAAC;gBACN;cACD;YACD,CAAE;YACFd,OAAO,EAAEA,CAAA,KAAM/H,YAAY,CAAC2G,UAAU,CAAC9B,EAAE,EAAEoB,IAAI,CAACpB,EAAE,CAAE;YAAA4B,QAAA,eAEpD5J,OAAA;cAAMiM,uBAAuB,EAAE;gBAAEC,MAAM,EAAEzM;cAAW;YAAE;cAAA0L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CACZ;QAAA,GAxEIlC,IAAI,CAACpB,EAAE;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyER,CACL,CAAC,EAED,CAAC/H,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,KAChFsG,UAAU,CAACU,OAAO,CAACe,MAAM,GAAG,CAAC,gBAC5BvL,OAAA,CAACV,UAAU;UACVmM,SAAS,EAAC,iBAAiB;UAC3BzB,EAAE,EAAE;YACHpC,eAAe,EAAE,SAAS;YAC1BuE,MAAM,EAAE,SAAS;YACjBN,MAAM,EAAE,IAAI;YACZ1B,OAAO,EAAE,gBAAgB;YACzBF,OAAO,EAAE,MAAM;YACf,SAAS,EAAE;cACVrC,eAAe,EAAE;YAClB;UACD;UACA;UAAA;UACAsD,OAAO,EAAEA,CAAA,KAAM;YACdvC,kBAAkB,CAACmB,UAAU,CAAC9B,EAAE,CAAC;YACjC,IAAIrF,OAAO,EAAE;cACZA,OAAO,CAAC,CAAC;YACV;YACAwE,WAAW,CAAC,CAAC;UACd,CAAE;UAAAyC,QAAA,eAEF5J,OAAA,CAACH,OAAO;YACPuM,QAAQ,EAAC,OAAO;YAChBpC,EAAE,EAAE;cAAEtC,KAAK,EAAE;YAAO;UAAE;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,GACV,IAAI;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAER,CAAC,CAAC,eACFtL,OAAA,CAACZ,OAAO;MACHqM,SAAS,EAAC,eAAe;MAC7BzD,EAAE,EAAEA,EAAG;MACPF,IAAI,EAAEA,IAAK;MACX7C,QAAQ,EAAEA,QAAS;MACnBoH,OAAO,EAAElF,WAAY;MACrBmF,YAAY,EAAE;QACbC,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACb,CAAE;MACFC,eAAe,EAAE;QAChBF,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACb,CAAE;MACFE,SAAS,EAAE;QACVC,IAAI,EAAE;UACL3C,EAAE,EAAE;YACH6B,MAAM,EAAGe,KAAK,IAAKA,KAAK,CAACf,MAAM,CAACgB,OAAO,GAAG,IAAI;YAC9C,GAAGxG;UACJ;QACD;MACD;MACA;MAAA;MAAAuD,QAAA,eAEA5J,OAAA,CAACd,GAAG;QACH8K,EAAE,EAAE;UACHC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpB3I,GAAG,EAAE,KAAK;UACV4I,OAAO,EAAE;QACV,CAAE;QAAAP,QAAA,gBA+CF5J,OAAA,CAACT,OAAO;UAACuN,KAAK,EAAEtI,SAAS,CAAC,UAAU,CAAE;UAACuI,KAAK;UAAAnD,QAAA,eAC3C5J,OAAA,CAACV,UAAU;YACVkM,IAAI,EAAC,OAAO;YACZN,OAAO,EAAE3B,sBAAuB;YAAAK,QAAA,eAEhC5J,OAAA;cAAMiM,uBAAuB,EAAE;gBAAEC,MAAM,EAAEvM;cAAa;YAAE;cAAAwL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EACT,CAAC/H,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,kBAC/ExD,OAAA,CAACT,OAAO;UAACuN,KAAK,EAAEtI,SAAS,CAAC,kBAAkB,CAAE;UAACuI,KAAK;UAAAnD,QAAA,eACnD5J,OAAA,CAACV,UAAU;YACVkM,IAAI,EAAC,OAAO;YACZN,OAAO,EAAE1D,0BAA2B;YAAAoC,QAAA,eAEpC5J,OAAA;cACCiJ,KAAK,EAAE;gBACNrB,eAAe,EAAEzC,aAAa;gBAC9B0F,YAAY,EAAE,MAAM;gBACpBjK,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdoJ,OAAO,EAAE,cAAc;gBACvB+C,SAAS,EAAE;cACZ;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACT,EACA,CAAC/H,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,kBAC/ExD,OAAA,CAACT,OAAO;UAACuN,KAAK,EAAErK,eAAe,GAAG+B,SAAS,CAAC,4CAA4C,CAAC,GAAGA,SAAS,CAAC,cAAc,CAAE;UAACuI,KAAK;UAAAnD,QAAA,eAC3H5J,OAAA,CAACV,UAAU;YACVkM,IAAI,EAAC,OAAO;YACZN,OAAO,EAAEA,CAAA,KAAM;cACdlI,oBAAoB,CAAC6C,kBAAkB,CAAC;cACxC,IAAIlD,OAAO,EAAE;gBACZA,OAAO,CAAC,CAAC;cACV;cACAwE,WAAW,CAAC,CAAC;YACd,CAAE;YACF8F,QAAQ,EAAExK,eAAgB;YAAAmH,QAAA,eAE1B5J,OAAA;cACCiM,uBAAuB,EAAE;gBAAEC,MAAM,EAAExM;cAAS,CAAE;cAC9CuJ,KAAK,EAAE;gBAAEwB,OAAO,EAAEhI,eAAe,GAAG,GAAG,GAAG;cAAE;YAAE;cAAA0I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACT,EACA,CAAC/H,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,kBAC/ExD,OAAA,CAACT,OAAO;UAACuN,KAAK,EAAEtI,SAAS,CAAC,eAAe,CAAE;UAACuI,KAAK;UAAAnD,QAAA,eAChD5J,OAAA,CAACV,UAAU;YACVkM,IAAI,EAAC,OAAO;YACZN,OAAO,EAAE7B,oBAAqB;YAAAO,QAAA,eAE9B5J,OAAA;cACCiM,uBAAuB,EAAE;gBAAEC,MAAM,EAAEzM;cAAW,CAAE;cAChDwJ,KAAK,EAAE;gBAAE+D,SAAS,EAAE;cAAO;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGVtL,OAAA,CAACZ,OAAO;MACP0I,IAAI,EAAEI,eAAgB;MACtBjD,QAAQ,EAAEI,mBAAoB;MAC9BgH,OAAO,EAAExE,sBAAuB;MAChCyE,YAAY,EAAE;QACbC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACb,CAAE;MACFC,eAAe,EAAE;QAChBF,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACb,CAAE;MAAA5C,QAAA,eAEF5J,OAAA,CAACd,GAAG;QAAA0K,QAAA,gBACH5J,OAAA,CAACR,YAAY;UACZkI,KAAK,EAAEwB,qBAAsB;UAC7BgE,QAAQ,EAAEzF;QAAkB;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACFtL,OAAA;UAAA4J,QAAA,EACE;AACP;AACA;AACA;AACA;QAAK;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,eACT,CAAC;AAEL,CAAC;AAAC1I,EAAA,CAhjBIP,aAAkM;EAAA,QAgCnMzC,cAAc,EACOE,cAAc;AAAA;AAAAqN,EAAA,GAjClC9K,aAAkM;AAkjBxM,eAAeA,aAAa;AAAC,IAAA8K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}