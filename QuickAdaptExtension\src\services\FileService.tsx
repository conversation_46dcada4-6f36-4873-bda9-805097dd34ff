import { AxiosResponse } from "axios";
import { adminApiService, userApiService } from "./APIService";
import {FileUpload} from "../models/FileUpload"

export const getAllFiles = async (skip:any,limit:any,filters: any,orderByField:any): Promise<any | null> =>
    {
    try {
        const requestBody = {
            skip:skip>=0 ? skip : 0,
            limit:limit>0? limit : 12 ,
            filters: filters ? filters : "",
            orderByFields: orderByField,
        };
		const response = await userApiService.post<FileUpload[]>('/FileUpload/GetAllFiles', requestBody);
		  return response.data;
		} catch (error) {
		  console.error('Error fetching all users:', error);
		  throw error;
		}
};


export const uploadSRSFile = async (accountId: string,formData: FormData) => {
	try {
		const response = await userApiService.post(
			`/Assistant/UploadSRSs?accountId=${encodeURIComponent(accountId)}`,
			formData,
			{
				headers: {
					'Content-Type': 'multipart/form-data',
				},
			}
		);
		return response;
	} catch (error) {
		// console.error('Error uploading SRS file:', error);
		throw error;
	}
};

export const uploadXpathsFile = async (accountId: string,formData: FormData) => {
	try {
		const response = await userApiService.post(
			`/Assistant/UploadXpaths?accountId=${encodeURIComponent(accountId)}`,
			formData,
			{
				headers: {
					'Content-Type': 'multipart/form-data',
				},
			}
		);
		return response;
	} catch (error) {
		// console.error('Error uploading SRS file:', error);
		throw error;
	}
};
