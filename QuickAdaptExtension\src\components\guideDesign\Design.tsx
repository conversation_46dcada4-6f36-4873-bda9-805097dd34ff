import React, { useEffect, useState } from "react";
// import Draggable from "react-draggable";
import { Button, Box, Typography, IconButton, Select, MenuItem, Step } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import DesignServicesIcon from "@mui/icons-material/DesignServices";
import ViewModuleIcon from "@mui/icons-material/ViewModule";
import CodeIcon from "@mui/icons-material/Code";
import CanvasSettings from "./CanvasSettings";
import Elementssettings from "../guideSetting/ElementsSettings";
import OverlaySettings from "./Overlay";
import CustomCSS from "./CustomCss";
import PageInteractions from "../guideBanners/selectedpopupfields/PageInteraction";
import AnimationSettings from "./Animation";
import "./Canvas.module.css";
import useDrawerStore from "../../store/drawerStore";
import Tooltip from "@mui/material/Tooltip";
import HotspotSettings from "../hotspot/HotspotSettings";
import { animation, elements, Hotspoticon, overlay, Reselect } from "../../assets/icons/icons";
import TooltipCanvasSettings from "../Tooltips/designFields/TooltipCanvasSettings";
import { TouchAppSharp, KeyboardTabSharp } from "@mui/icons-material";
import { GetGudeDetailsByGuideId } from "../../services/GuideListServices";
import userSession from "../../store/userSession";
import ChecklistCanvasSettings from "../checklist/ChecklistCanvasSettings";
import LauncherSettings from "../checklist/LauncherSettings";
import Checkpoints from "../checklist/Chekpoints";
import TitleSubTitle from "../checklist/TitleSubTitle";
import '../../styles/rtl_styles.scss';
import { useTranslation } from 'react-i18next';

const DesignMenu = ({
	width,
	height,
	overlays,
	setOverLays,
	backgroundC,
	setBackgroundC,
	Bposition,
	setBposition,
	bpadding,
	setbPadding,
	Bbordercolor,
	setBBorderColor,
	BborderSize,
	setBBorderSize,
	zindeex,
	setZindeex,
	setDesignPopup,
	selectedTemplate,
	designPopup,
	initialGuideData,
	updatedGuideData,
	handleSaveGuide,
	resetHeightofBanner,
}: // hotspotPopup
// padding,
// borderRadius,
// borderColor,
// backgroundColor,
// selectedPosition,
// setSelectedPosition,
// setBorderColor,
// setBackgroundColor,
// setWidth,
// setHeight,
// setPadding,
// setBorderRadius,
// //selectedTemplate,
// position,
// setPosition,
// radius,
// setRadius,
// borderSize,
// setBorderSize,
any) => {
	const { t: translate } = useTranslation();
	// State to control the visibility of CanvasSettings
	const [showCanvasSettings, setShowCanvasSettings] = useState(false);
	const [showChecklistCanvasSettings, setShowChecklistCanvasSettings] = useState(false);
	//const [showTooltipCanvasSettings, setShowTooltipCanvasSettings] = useState(false);
	const [showOverlay, setOverlaySettings] = useState(false);
	const [showElementsSettings, setShowElementsSettings] = useState(false);
	const [showAnimation, setshowAnimation] = useState(false);
	const [showCustomCSS, setShowCustomCSS] = useState(false);
	const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
	const [reselectElement, setReselectElement] = useState(false);
	const [goToNextElement, setGoToNextElement] = useState(false);
	// const [elementClick, setElementClick] = useState(false);
	//const [dropdownValue, setDropdownValue] = useState("");
	const [isOpen, setIsOpen] = useState(true);
	const { setCurrentGuideId, currentGuideId, getCurrentGuideId } = userSession((state: any) => state);
	const {
		//selectedTemplate,
		padding,
		setPadding,
		position,
		setPosition,
		radius,
		setRadius,
		borderSize,
		setBorderSize,
		setBorderColor,
		borderColor,
		setBackgroundColor,
		backgroundColor,
		overlayEnabled,
		setOverlayEnabled,
		setZiindex,
		setguidesSettingspopup,
		setHotspotPopup,
		setTitlePopup,
		titlePopup,
		hotspotPopup,
		showTooltipCanvasSettings,
		setShowTooltipCanvasSettings,
		setTooltipBackgroundcolor,
		setTooltipBordercolor,
		setTooltipBorderradius,
		setTooltipBordersize,
		CANVAS_DEFAULT_VALUE,
		savedGuideData,
		ButtonsDropdown,
		setButtonsDropdown,
		elementSelected,
		setElementSelected,
		currentHoveredElement,
		elementClick,
		setElementClick,
		elementButtonName,
		setElementButtonName,
		updateDesignelementInTooltip,
		toolTipGuideMetaData,
		elementbuttonClick,
		SetElementButtonClick,
		buttonClick,
		setButtonClick,
		currentStep,
		highlightedButton,
		setHighlightedButton,
		setSelectActions,
		updateTooltipButtonAction,
		mapButtonSection,
		btnidss,
		selectedTemplateTour,
		progress,
		setProgress,
		setSelectedOption,
		dropdownValue,
		setDropdownValue,
setIsUnSavedChanges,
		showLauncherSettings,
		setShowLauncherSettings,
		checkpointsPopup,
		setCheckPointsPopup,
		createWithAI,
		interactionData
	} = useDrawerStore((state: any) => state);
	const setbtnidss = useDrawerStore((state) => state.setbtnidss);

	useEffect(() => {
		setShowCanvasSettings(false);
		setShowChecklistCanvasSettings(false);
		setShowTooltipCanvasSettings(false);
		setOverlaySettings(false);
		setShowElementsSettings(false);
		setshowAnimation(false);
		setShowCustomCSS(false);
		setHotspotPopup(false);
		setTitlePopup(false);
		setShowLauncherSettings(false);
	}, [selectedTemplate, selectedTemplateTour]);
	// const overlayEnabled = useDrawerStore((state) => state.overlayEnabled);
	// const setOverlayEnabled = useDrawerStore((state) => state.setOverlayEnabled);
	const toggleCanvasSettings = () => {
		if (
			selectedTemplate === "Tooltip" ||
			selectedTemplate === "Hotspot" ||
			selectedTemplateTour === "Tooltip" ||
			selectedTemplateTour === "Hotspot"
		) {
			setShowTooltipCanvasSettings(!showTooltipCanvasSettings);
		}
		else if (selectedTemplate === "Checklist")
		{
			setShowChecklistCanvasSettings(!showChecklistCanvasSettings);
		}
		else {
			setShowCanvasSettings(!showCanvasSettings);
		}
	};
	const toggleOverlaySettings = () => {
		setMenuPopup(false);
		setOverlaySettings(!showOverlay);
	};
	const handleHotspotClick = () => {
		//setguidesSettingspopup(false); // Close any other popups
		setHotspotPopup(true); // Open the hotspot popup
		setTimeout(() => {
			setHotspotPopup(true); // Ensure the popup is rendered
		}, 0);
	};

	const handleTitlePopup = () =>
	{
		setTitlePopup(true);
	}
	const handleCheckPointPopup = () =>
	{
		setCheckPointsPopup(true);
	}

	// useEffect(() => {
	// 	setTimeout(() => {
	// 		setHotspotPopup(true); // Ensure the popup is rendered
	// 	}, 0);
	// }, [hotspotPopup]);

	// Removed useEffect that was resetting dropdownValue on currentStep change
	// This was causing the dropdown to show "Select an option" even after selection


	const toggleReselectElement = () => {
		//setReselectElement(!reselectElement);
		//setTooltipXaxis("4");
		//setTooltipYaxis("4");
		//setTooltipPosition("middle-center");
		//setTooltipBackgroundcolor("");
		//setTooltipBordercolor("");
		setTooltipBorderradius("8");
		//setTooltipBordersize("1");
		//setTooltipPadding("4");
		//setTooltipWidth("400");
		//updateCanvasInTooltip(CANVAS_DEFAULT_VALUE);
		setElementSelected(true);
		setIsTooltipPopup(false);
		setShowTooltipCanvasSettings(false);
	};

	useEffect(() => {

		const fetchGuideDetails = async () => {
		  if (currentGuideId != "" && currentGuideId != null) {
			// First, check the current toolTipGuideMetaData for the most up-to-date state
			const tooltipMetadata = toolTipGuideMetaData?.[currentStep - 1];

			if (tooltipMetadata?.design?.gotoNext) {
			  // Use the current metadata as the source of truth
			  const hasButtonClick = tooltipMetadata.design.gotoNext.ButtonId &&
				tooltipMetadata.design.gotoNext.ButtonId.trim() !== "";

			  console.log("useEffect: Has button click:", hasButtonClick, "ButtonId:", tooltipMetadata.design.gotoNext.ButtonId);

			  if (hasButtonClick) {
				setElementClick("button");
				setButtonClick(true);
				SetElementButtonClick(true);
				// Use ButtonId for dropdown value, not ButtonName
				setDropdownValue(tooltipMetadata.design.gotoNext.ButtonId || "");
				setElementButtonName(tooltipMetadata.design.gotoNext.ButtonName || tooltipMetadata.design.gotoNext.buttonName || "");
				setbtnidss(tooltipMetadata.design.gotoNext.ButtonId || "");

				console.log("useEffect: Set button click mode with values:", {
					elementClick: "button",
					buttonClick: true,
					dropdownValue: tooltipMetadata.design.gotoNext.ButtonId,
					elementButtonName: tooltipMetadata.design.gotoNext.ButtonName,
					btnidss: tooltipMetadata.design.gotoNext.ButtonId
				});
			  } else {
				setElementClick("element");
				setButtonClick(false);
				SetElementButtonClick(false);
				setDropdownValue("");
				setElementButtonName("");
				setbtnidss("");

				console.log("useEffect: Set element click mode");
			  }
			} else {
			  // Fallback to fetching from database if metadata doesn't exist
			  const data = await GetGudeDetailsByGuideId(currentGuideId, createWithAI, interactionData);
			  const guideStep = data?.GuideDetails?.GuideStep?.[currentStep - 1];

			  if (guideStep?.Design?.GotoNext) {
				const hasButtonClick = guideStep.Design.GotoNext.ButtonId &&
				  guideStep.Design.GotoNext.ButtonId.trim() !== "";

				if (hasButtonClick) {
				  setElementClick("button");
				  setButtonClick(true);
				  SetElementButtonClick(true);
				  // Use ButtonId for dropdown value, not ButtonName
				  setDropdownValue(guideStep.Design.GotoNext.ButtonId || "");
				  setElementButtonName(guideStep.Design.GotoNext.ButtonName || "");
				  setbtnidss(guideStep.Design.GotoNext.ButtonId || "");
				} else {
				  setElementClick("element");
				  setButtonClick(false);
				  SetElementButtonClick(false);
				  setDropdownValue("");
				  setElementButtonName("");
				  setbtnidss("");
				}
			  } else {
				setElementClick("element");
				setButtonClick(false);
				SetElementButtonClick(false);
				setDropdownValue("");
				setElementButtonName("");
				setbtnidss("");
			  }
			}
		  }
		};
		fetchGuideDetails();
	  }, [currentStep, toolTipGuideMetaData]);


	const removeAppliedStyleOfEle = (element: HTMLElement) => {
		element.removeAttribute("disabled");
		element.style.outline = "";
		element.style.pointerEvents = "unset";
	};
	const [menuPopup, setMenuPopup] = useState(true);
	const onReselectElement = () => {
		// setTooltipXaxis("4");
		// setTooltipYaxis("4");
		// setTooltipPosition("middle-center");
		// setTooltipBackgroundcolor("");
		// setTooltipBordercolor("");
		// setTooltipBorderradius("4");
		// setTooltipBordersize("1");
		// setTooltipPadding("4");
		// setTooltipWidth("400");
		//updateCanvasInTooltip(CANVAS_DEFAULT_VALUE);
		//setElementSelected(true);
		// setElementSelected(false);
		// setIsTooltipPopup(false);
		// setShowTooltipCanvasSettings(false);
		const existingHotspot = document.getElementById("hotspotBlinkCreation");
		const existingTooltip = document.getElementById("Tooltip-unique");
		setIsUnSavedChanges(false);

			// existingTooltip.remove();
			setDesignPopup(false);
			setElementSelected(false);
			currentHoveredElement && removeAppliedStyleOfEle(currentHoveredElement);

		setIsUnSavedChanges(true);
	};

	// const [highlightedButton, setHighlightedButton] = useState(null);

	// Function to handle button highlighting
	const handleButtonClick = (buttonId: any) => {
		console.log("Button clicked:", buttonId);
		setIsUnSavedChanges(true);

		handleToggleClick(buttonId);
		setHighlightedButton((prev: any) => (prev === buttonId ? null : buttonId));

		// Remove immediate save to prevent state interference
		// handleSaveGuide();
	};

	const handleLaunchSettings = () =>
	{
		setShowLauncherSettings(true);
		}
	const handleToggleClick = (type: any) => {
		console.log("handleToggleClick called with type:", type);

		if (type === 1) {
			// Switching to "element click"
			console.log("Switching to element click mode");
			setElementClick("element");
			setElementButtonName("");
			setDropdownValue("");
			setbtnidss("");
			setButtonClick(false);
			SetElementButtonClick(false);

			// Update the gotoNext object to reflect "element click" state
			const updatedCanvasSettings = {
				NextStep: "element",
				ButtonId: "",
				ElementPath: "",
				ButtonName: "",
				Id: ""
			};
			updateDesignelementInTooltip(updatedCanvasSettings);

		} else if (type === 2) {
			// Switching to "button click"
			console.log("Switching to button click mode");
			setElementClick("button");
			setButtonClick(true);
			SetElementButtonClick(true);

			// CRITICAL FIX: When switching to button click, restore the dropdown state from metadata
			const tooltipMetadata = toolTipGuideMetaData?.[currentStep - 1];
			console.log("Current tooltip metadata:", tooltipMetadata);

			if (tooltipMetadata?.design?.gotoNext?.ButtonId) {
				const buttonId = tooltipMetadata.design.gotoNext.ButtonId;
				const buttonName = tooltipMetadata.design.gotoNext.ButtonName;

				// Update all related state variables to ensure dropdown shows correctly
				setDropdownValue(buttonId);
				setElementButtonName(buttonName || "");
				setbtnidss(buttonId);

				console.log("Restored button click state when switching to button mode:", {
					buttonId,
					buttonName,
					dropdownValue: buttonId
				});
			} else {
				console.log("No existing button click data found in metadata");
			}
		}
	};
	const DesignelementInTooltip = (value: any, name: any) => {
		const updatedCanvasSettings = {
			NextStep: elementClick,
			ButtonId: value,
			ElementPath: "",
			ButtonName: name?.name,
			Id: value, // Add the Id property to match what's expected in Tooltips.tsx
		};

		updateDesignelementInTooltip(updatedCanvasSettings);
	};

	const handleDropdownChange = (event: any) => {
		const selectedValue = event.target.value;
		console.log("Dropdown changed to:", selectedValue);

		// Find the button container dynamically instead of using hardcoded index
		const buttonContainer = toolTipGuideMetaData[currentStep - 1]?.containers?.find(
			(container: any) => container.type === "button"
		);
		const selectedButton = buttonContainer?.buttons?.find(
			(button: any) => button.id === selectedValue
		);

		console.log("Selected button:", selectedButton);
		console.log("Button container:", buttonContainer);

		// Update all relevant state variables
		setDropdownValue(selectedValue);
		setElementButtonName(selectedButton?.name || selectedValue); // Use button name, fallback to ID
		setbtnidss(selectedValue);
		setElementClick("button");

		// Update the design metadata with both ID and name for proper persistence
		DesignelementInTooltip(selectedValue, selectedButton);

		// Mark as unsaved changes
		setIsUnSavedChanges(true);

		console.log("Updated state after dropdown change:", {
			dropdownValue: selectedValue,
			elementButtonName: selectedButton?.name || selectedValue,
			btnidss: selectedValue,
			elementClick: "button"
		});
	};

	const toggleElementsSettings = () => {
		setMenuPopup(false);
		setShowElementsSettings(true);
		//setDesignPopup(false);
		return (
			<>
				{showElementsSettings && designPopup && (
					<Box>
						<Elementssettings
							setShowElementsSettings={setShowElementsSettings}
							setDesignPopup={setDesignPopup}
						/>
					</Box>
				)}
				;
			</>
		);
	};

	const toggleCustomCSS = () => {
		setShowCustomCSS(!showCustomCSS); // Toggle CustomCSS visibility
	};
	const toggleAnimation = () => {
		setshowAnimation(!showAnimation); // Toggle CustomCSS visibility
	};

	const handleClose = () => {
		setIsOpen(false); // Close the popup when close button is clicked
		setDesignPopup(false);
	};

	const handleDismissDataChange = (data: any) => {};
	if (!isOpen) return null;
	const handleStatusChange = (status: boolean) => {
		setOverlayEnabled(status);
	};

	return (
		//<Draggable>
		<div
			id="qadpt-designpopup"
			className="qadpt-designpopup"
		>
			<div className="qadpt-content">
				<div className="qadpt-design-header">
					<div className="qadpt-title">{translate("Design")}</div>
					<IconButton
						size="small"
						aria-label={translate("close")}
						onClick={handleClose}
					>
						<CloseIcon />
					</IconButton>
				</div>


				{titlePopup && (
					<>
						<TitleSubTitle/>
					</>
				)}

				{showLauncherSettings && (
					<LauncherSettings/>

				)}
				{menuPopup && (
					<>
						<div className="qadpt-controls">
							{(selectedTemplate === "Tooltip" || selectedTemplateTour === "Tooltip") && (
								<Box
									className=" qadpt-control-box"
									onClick={onReselectElement}
									sx={{cursor:"pointer"}}
								>
									<Typography
										className="qadpt-control-label"
										sx={{ color: "#495e58" }}
									>
										{translate("Reselect Element")}
									</Typography>
									<span
										className="qadpt-reselect-icon"
										dangerouslySetInnerHTML={{ __html: Reselect }}
										style={{ padding: "5px", marginRight: "10px" }}
									/>
								</Box>
							)}
							{(selectedTemplate === "Tooltip" || selectedTemplateTour === "Tooltip") && (
								<div
									// className="qadpt-design-btn"
									style={{
										marginBottom: "8px",
										borderRadius: "12px",
										padding: "8px 12px",
										background: "#eae2e2",
										textTransform: "none",
									}}
								>
									{/* <Button
						className="qadpt-design-btn"
						onClick={toggleGoToNextElement}
						startIcon={<KeyboardTabIcon />}
					>
						Go to next step
					</Button> */}
									<div style={{ display: "flex", justifyContent: "flex-start", alignItems: "center" }} className="qadpt-gtnext">
										<span
											style={{
												background: "rgba(95, 158, 160, 0.2)",
												borderRadius: "100px",
												padding: "4px 8px",
											}}
										>
											<KeyboardTabSharp
												style={{
													color: "var(--primarycolor)",
													height: "21px",
													width: "21px",
													// borderRadius: "50px",
													// background: "rgba(95, 158, 160, 0.2)",
												}}
											/>
										</span>
										<Typography sx={{ color: "#444444", fontWeight: "600" }}>{translate("Go to next step")}</Typography>
									</div>

									<div style={{ display: "flex", gap: "6px", marginTop: "10px" }}>
										{/* Button 1 */}
										<div
											onClick={() => handleButtonClick(1)}
											style={{
												display: "flex",
												justifyContent: "flex-start",
												alignItems: "center",
												height: "40px",
												borderRadius: "8px",
												cursor: "pointer",
												backgroundColor:
													elementClick === "element" ? "rgba(95, 158, 160, 0.2)" : "rgb(196, 193, 193, 0.3)",

												border: elementClick === "element" ? "1px solid var(--primarycolor)" : "1px solid transparent",
												width: "95px",
											}}
										>
											{/* <KeyboardTabSharp
										style={{
											color: "var(--primarycolor)",
											borderRadius: "50px",
											marginRight: "8px",
										}}
									/> */}
											<Typography sx={{ color: "#1c1b1f", padding: "0 6px", fontSize: "12px !important" }}>
												{translate("Element Click")}
											</Typography>
										</div>

										{/* Button 2 */}
										<div
											onClick={() => handleButtonClick(2)}
											style={{
												display: "flex",
												justifyContent: "flex-start",
												alignItems: "center",
												height: "40px",
												borderRadius: "8px",
												cursor: "pointer",
												backgroundColor:
													elementClick !== "element" ? "rgba(95, 158, 160, 0.2)" : "rgb(196, 193, 193, 0.3)",

												border: elementClick !== "element" ? "1px solid var(--primarycolor)" : "1px solid transparent",
												width: "95px",
											}}
										>
											{/* <KeyboardTabSharp
										style={{
											color: "var(--primarycolor)",
											borderRadius: "50px",
											marginRight: "8px",
										}}
									/> */}
											<Typography sx={{ color: "#1c1b1f", padding: "0 11px", fontSize: "12px !important" }}>
												{translate("Button Click")}
											</Typography>
										</div>
									</div>
									{buttonClick && (
										<div>
											<Box
												className="qadpt-chos-btn"
												sx={{
													margin: "0 !important",
													marginBottom: "8px",
													borderRadius: "12px",
													background: "#eae2e2",
													textTransform: "none",
												}}
											>
												<Typography sx={{  padding: "4px", color: "#495e58a" }}>
													{translate("Choose Button")}
												</Typography>
												{(() => {
													// Find the button container dynamically instead of using hardcoded index
													const buttonContainer = toolTipGuideMetaData?.[currentStep - 1]?.containers?.find(
														(container: any) => container.type === "button"
													);
													return buttonContainer?.buttons ? (
														<>
															<Select
																// The dropdown value is always the button's ID, matching MenuItem values
																value={(() => {
																  // Primary: Use the ButtonId from metadata as the source of truth
																  const designButtonId = toolTipGuideMetaData[currentStep - 1]?.design?.gotoNext?.ButtonId;
																  if (designButtonId && buttonContainer?.buttons?.some((button: any) => button.id === designButtonId)) {
																	return designButtonId;
																  }

																  // Secondary: Use btnidss if it matches a valid button
																  if (btnidss && buttonContainer?.buttons?.some((button: any) => button.id === btnidss)) {
																	return btnidss;
																  }

																  // Tertiary: Use dropdownValue if it matches a valid button
																  if (dropdownValue && buttonContainer?.buttons?.some((button: any) => button.id === dropdownValue)) {
																	return dropdownValue;
																  }
																  // Fallback: if elementButtonName matches a button name, use its id
																  if (elementButtonName && buttonContainer?.buttons?.length) {
																	const match = buttonContainer.buttons.find((button: any) => button.name === elementButtonName);
																	if (match) return match.id;
																  }
																  // Fallback: if ButtonId is present but ButtonName is missing, use ButtonId and display the name by lookup
																  if (designButtonId && buttonContainer?.buttons?.length) {
																	const match = buttonContainer.buttons.find((button: any) => button.id === designButtonId);
																	if (match) return designButtonId;
																  }
																  // Default to empty string
																  return "";
																})()}
																onChange={handleDropdownChange}
																displayEmpty
																style={{ width: "100%" }}
																sx={{
																  "& .MuiSvgIcon-root": {
																	height: "20px",
																	width: "20px",
																	top: "10px",
																  },
																}}
																renderValue={(selected) => {
																	// Always display the button name for the selected ID
																	if (!selected) return translate("Select an option");

																	const btn = buttonContainer?.buttons?.find((button: any) => button.id === selected);
																	return btn?.name || translate("Select an option");
																}}
															>
																<MenuItem value="" disabled>
																	{translate("Select an option")}
																</MenuItem>
																{buttonContainer.buttons.map(
																  (button: any, buttonIndex: number) => (
																	<MenuItem key={buttonIndex} value={button.id}>
																	  {button.name}
																	</MenuItem>
																  )
																)}
															</Select>
															{/* The selected button name is shown in the dropdown, so no need to display it separately below. */}
														</>
													) : null;
												})()}{" "}
												{/* Prevent rendering until data exists */}
											</Box>
										</div>
									)}
								</div>
							)}
							{selectedTemplate === "Hotspot" || selectedTemplateTour === "Hotspot" ? (
								<Button
									className="qadpt-design-btn"
									onClick={handleHotspotClick} // Trigger the hotspot popup
								>
									<span
										className="qadpt-hotsicon"
										dangerouslySetInnerHTML={{ __html: Hotspoticon }}
									/>
									<Typography
										sx={{fontWeight: "600 !important"}}
									>
										{translate("Hotspot")}
									</Typography>
								</Button>
							) : (
								""
							)}
							{(selectedTemplate === "Checklist") && (
								<>
								<Button
										className="qadpt-design-btn"
										onClick={handleCheckPointPopup}

								startIcon={<DesignServicesIcon />}
							>
										{translate("Steps")}
								</Button>

								<Button
										className="qadpt-design-btn"
										onClick={handleTitlePopup}
								startIcon={<span
									dangerouslySetInnerHTML={{ __html: elements }}
									style={{ height: "23px" }}
								/>}
							>
										{translate("Title & SubTitle")}
									</Button>
									</>

							)}


							{checkpointsPopup && (
								<>
									<Checkpoints/>
								</>

							)}


							{/* Buttons with icons */}
							<Button
								className="qadpt-design-btn"
								onClick={toggleCanvasSettings}
								startIcon={<span
									dangerouslySetInnerHTML={{ __html: overlay }}
									style={{ height: "23px" }}
								/>}
							>
								{translate("Canvas")}
							</Button>

							{selectedTemplate != "Checklist" && (
								<span>
									<Button
										className="qadpt-design-btn"
										onClick={toggleElementsSettings}
										startIcon={
											<span
												dangerouslySetInnerHTML={{ __html: elements }}
												style={{ height: "23px" }}
											/>
										}
									>
										{translate("Elements")}
									</Button>

								{(["Tooltip", "Announcement"].includes(selectedTemplate) ||
									(selectedTemplate === "Tour" && ["Tooltip", "Announcement"].includes(selectedTemplateTour))) && (
											<Button
												className="qadpt-design-btn"
												onClick={toggleOverlaySettings}
												startIcon={
													<span
														dangerouslySetInnerHTML={{ __html: overlay }}
														style={{ height: "23px" }}
													/>
												}
											// sx={{
											// 	opacity: selectedTemplate === "Banner" ? 0.5 : 1,
											// }}
											>
											{translate("Overlay")}
											</Button>
										)}
								</span>
							)}

							{/* {selectedTemplate != "Checklist" && (
								<Tooltip
									arrow 
									title={translate("Coming soon")}
									PopperProps={{
										sx: {
											zIndex: 9999,
										},
									}}
								>
									<span>
										<Button
											disabled
											className="qadpt-design-btn"
											startIcon={
												<span
													dangerouslySetInnerHTML={{ __html: animation }}
													style={{ height: "23px" }}
												/>
											}
											sx={{
												opacity: 0.5,
											}}
										>
											{translate("Animation")}
										</Button>
									</span>
								</Tooltip>
							)} */}
							{selectedTemplate === "Checklist" && (
								<Button
									className="qadpt-design-btn"
									onClick={handleLaunchSettings}
									startIcon={<span
										dangerouslySetInnerHTML={{ __html: animation }}
										style={{ height: "23px" }}
									/>}
								>
									{translate("Launcher")}
								</Button>
							)}
						</div>
					</>
				)}
			</div>
			{hotspotPopup && (
				<Box>
					<HotspotSettings />
				</Box>
			)}

			{showCanvasSettings && (selectedTemplate === "Banner" || selectedTemplateTour === "Banner") ? (
				<PageInteractions
					setShowCanvasSettings={setShowCanvasSettings}
					backgroundC={backgroundC}
					setBackgroundC={setBackgroundC}
					Bposition={Bposition}
					setBposition={setBposition}
					bpadding={bpadding}
					setbPadding={setbPadding}
					Bbordercolor={Bbordercolor}
					setBBorderColor={setBBorderColor}
					BborderSize={BborderSize}
					setBBorderSize={setBBorderSize}
					zindeex={zindeex}
					setZindeex={setZindeex}
					resetHeightofBanner={resetHeightofBanner}
				/>
			) : (
				showCanvasSettings && (
					<Box>
						<CanvasSettings
							zindeex={zindeex}
							setZindeex={setZindeex}
							setShowCanvasSettings={setShowCanvasSettings}
							// width={width}
							// height={height}
							// padding={padding}
							// borderRadius={borderRadius}
							// borderColor={borderColor}
							// backgroundColor={backgroundColor}
							// selectedPosition={selectedPosition}
							// setSelectedPosition={setSelectedPosition}
							// setBorderColor={setBorderColor}
							// setBackgroundColor={setBackgroundColor}
							// setWidth={setWidth}
							// setHeight={setHeight}
							// setPadding={setPadding}
							// setBorderRadius={setBorderRadius}
						/>
					</Box>
				)
			)}

			{showTooltipCanvasSettings &&
			(selectedTemplate === "Tooltip" ||
				selectedTemplate === "Hotspot" ||
				selectedTemplateTour === "Tooltip" ||
				selectedTemplateTour === "Hotspot") ? (
				<TooltipCanvasSettings
					setShowTooltipCanvasSettings={setShowTooltipCanvasSettings}
					backgroundC={backgroundC}
					setBackgroundC={setBackgroundC}
					Bposition={Bposition}
					setBposition={setBposition}
					bpadding={bpadding}
					setbPadding={setbPadding}
					Bbordercolor={Bbordercolor}
					setBBorderColor={setBBorderColor}
					BborderSize={BborderSize}
					setBBorderSize={setBBorderSize}
					zindeex={zindeex}
					setZindeex={setZindeex}
				/>
			) : (
				""
			)}



{showChecklistCanvasSettings &&
			(selectedTemplate === "Checklist"
			) ? (
				<ChecklistCanvasSettings
					setShowChecklistCanvasSettings={setShowChecklistCanvasSettings}
					backgroundC={backgroundC}
					setBackgroundC={setBackgroundC}
					Bposition={Bposition}
					setBposition={setBposition}
					bpadding={bpadding}
					setbPadding={setbPadding}
					Bbordercolor={Bbordercolor}
					setBBorderColor={setBBorderColor}
					BborderSize={BborderSize}
					setBBorderSize={setBBorderSize}
					zindeex={zindeex}
					setZindeex={setZindeex}
				/>
			) : (
				""
			)}




			{showOverlay && (
				<Box>
					<OverlaySettings
						setOverlaySettings={setOverlaySettings}
						selectedTemplate={selectedTemplate}
						onStatusChange={handleStatusChange}
						setOverLays={setOverLays}
						setDesignPopup={setDesignPopup}
						anchorEl={anchorEl}
						setMenuPopup={setMenuPopup}
					/>
				</Box>
			)}
			{showElementsSettings && designPopup && (
				<Box>
					<Elementssettings
						setShowElementsSettings={setShowElementsSettings}
						setDesignPopup={setDesignPopup}
						setMenuPopup={setMenuPopup}
						resetHeightofBanner={resetHeightofBanner}
					/>
				</Box>
			)}
			{showCustomCSS && (
				<Box>
					<CustomCSS />
				</Box>
			)}
			{showAnimation && (
				<Box>
					<AnimationSettings selectedTemplate={selectedTemplate} />
				</Box>
			)}
		</div>

		//</Draggable>
	);
};

export default DesignMenu;
function setTooltipXaxis(arg0: string) {
	throw new Error("Function not implemented.");
}

function setTooltipYaxis(arg0: string) {
	throw new Error("Function not implemented.");
}

function setTooltipPosition(arg0: string) {
	throw new Error("Function not implemented.");
}

function setTooltipBorderradius(arg0: string) {
	throw new Error("Function not implemented.");
}

function setTooltipPadding(arg0: string) {
	throw new Error("Function not implemented.");
}

function setTooltipWidth(arg0: string) {
	throw new Error("Function not implemented.");
}

function updateCanvasInTooltip(CANVAS_DEFAULT_VALUE: any) {
	throw new Error("Function not implemented.");
}

function setElementSelected(arg0: boolean) {
	throw new Error("Function not implemented.");
}

function setIsTooltipPopup(arg0: boolean) {
	throw new Error("Function not implemented.");
}
