import React, { useEffect, useState } from "react";
import { Box, Typography, Popover, IconButton, TextField, MenuItem, Button, Tooltip } from "@mui/material";
import RemoveIcon from "@mui/icons-material/Remove";
import AddIcon from "@mui/icons-material/Add";
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import Modal from '@mui/material/Modal';
import { useTranslation } from 'react-i18next';

import DriveFolderUploadIcon from '@mui/icons-material/DriveFolderUpload';
import BackupIcon from '@mui/icons-material/Backup';
import { getAllFiles } from "../../services/FileService";
import { FileUpload } from "../../models/FileUpload";



const SelectImageFromApplication = ({ isOpen, handleModelClose, onImageSelect, handleImageUpload, setFormOfUpload, formOfUpload,handleReplaceImage, isReplaceImage, onImageUrlSelect, showUrlInput = true, showFileList = true }: any) => {
	const { t: translate } = useTranslation();

	const [files, setFiles] = useState<FileUpload[]>([]);
	const [imageUrl, setImageUrl] = useState("");
	const [skip, setSkip] = useState(0);
	const [limit, setLimit] = useState(9);
	const [totalCount, setTotalCount] = useState(0); // Set initial value to enable Next button
	const [filters, setFilters] = useState({});
	const getAllFilesData = async () => {
		try {
			const response = await getAllFiles(skip, limit,"","");
			if (response) {
				const files = response?.results;
				const count = response?._counts;
				const uploads: FileUpload[] = files.map((file:any) => ({
					ImageId: file.Id, 
					FileName: file.Name || null,
					Url: file.Url || '',
				}));
				setFiles(uploads);
				setTotalCount(count);
			}
		} catch (error) {
		}
	}

	const onPreviousClick = () => {
		if (skip > 0) {
			setSkip(Math.max(0, skip - limit));
		}
	};

	const onNextClick = () => {
		if (files.length === limit) {
			setSkip(skip + limit);
		}
	};

	useEffect(() => {
		getAllFilesData();
	}, [skip, limit]);

	return (<>
		<Modal open={isOpen} onClose={handleModelClose} sx={{zIndex:"999999 !important"}}>
			<Box sx={{
					position: "absolute",
					top: "50%",
					left: "50%",
					transform: "translate(-50%, -50%)",
					width: 500,
					bgcolor: "background.paper",
					boxShadow: 24,
					borderRadius: 2,
					p: 3,
					maxHeight: "500px",
					overflow: "hidden",
					zIndex: "9999"
				}}>
 				{showUrlInput && (
 				<Box mb={2}>
 					<Typography variant="subtitle1">{translate("Enter Image URL")}</Typography>
 					<Box display="flex" gap={1} mt={1}>
 						<TextField
 							fullWidth
 							variant="outlined"
 							size="small"
 							value={imageUrl}
 							onChange={e => setImageUrl(e.target.value)}
 							placeholder={translate("Paste image link here")}
 						/>
 						<Button
 							variant="contained"
 							disabled={!imageUrl}
 							onClick={() => {
 								if (onImageUrlSelect) {
 									onImageUrlSelect(imageUrl);
 									setImageUrl("");
 								}
 							}}
 						>
 							{translate("Confirm")}
 						</Button>
 					</Box>
 				</Box>
 				)}
				{showFileList && (
				<Box>
					<Typography variant="h6" sx={{ mb: 2, textAlign: "center" }}>{translate("Select a File")}</Typography>
				{/* Images Grid with Modern Pagination */}
				<Box sx={{ 
					display: "flex", 
					flexDirection: "column", 
					alignItems: "center", 
					gap: 2 
				}}>
					{/* Grid */}
					<Box sx={{ 
						display: "grid",
						gridTemplateColumns: "repeat(3, 1fr)",
						rowGap: 0.5,        // ✅ reduced vertical spacing
						columnGap: 0.5,     // ✅ reduced horizontal spacing 
						p: 1,
						backgroundColor: "rgba(0,0,0,0.02)",
						borderRadius: 1,
						width: "100%",
						maxWidth: 400,
						height: "auto"     // ✅ auto height, no scroll needed
					}}>
						{
							files && files.length > 0 ? (
								files.map((file, index) => (
									<Box 
										key={index} 
										sx={{ 
											width: "100%", 
											aspectRatio: "4 / 3", // Maintain 4:3 ratio
											cursor: "pointer",
											borderRadius: 1,
											overflow: "hidden",
											border: "1px solid #e0e0e0",
											transition: "transform 0.2s, box-shadow 0.2s",
											"&:hover": {
												transform: "scale(1.03)",
												boxShadow: 2
											}
										}} 
									>
										<img 
											src={file?.Url} 
											style={{ 
												width: "100%", 
												height: "100%",
												objectFit: "cover",
												display: "block"
											}} 
											onClick={() => { onImageSelect(file) }} 
											alt={translate("Uploaded file image")} 
										/>
									</Box>
								))
							) : (
								Array.from({ length: 9 }).map((_, index) => (
									<Box 
										key={index}
										sx={{ 
											width: "100%", 
											aspectRatio: "4 / 3",
											backgroundColor: "#f5f5f5",
											borderRadius: 1,
											display: "flex",
											alignItems: "center",
											justifyContent: "center",
											border: "1px dashed #ccc"
										}}
									>
										<Typography variant="caption" color="text.secondary">
											{index === 4 ? translate("No Images") : ""}
										</Typography>
									</Box>
								))
							)
						}
					</Box>

					{/* Pagination */}
					<Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
						<Button
							variant="outlined"
							startIcon={<ArrowBackIcon />}
							disabled={skip === 0}
							onClick={onPreviousClick}
							sx={{
								textTransform: "none",
								borderRadius: "999px",
								fontWeight: 500,
							}}
						>
							{translate("Previous")}
						</Button>

						<Button
							variant="outlined"
							endIcon={<ArrowForwardIcon />}
							disabled={files.length < limit}
							onClick={onNextClick}
							sx={{
								textTransform: "none",
								borderRadius: "999px",
								fontWeight: 500,
							}}
						>
							{translate("Next")}
						</Button>
					</Box>

					{/* Page Info */}
					{totalCount > 0 && (
						<Typography variant="caption" sx={{ mt: 1, color: "text.secondary" }}>
							{translate("Page")} {Math.floor(skip / limit) + 1} {translate("of")} {Math.ceil(totalCount / limit)} • {totalCount} {translate("images total")}
						</Typography>
					)}
				</Box>

					
					{/* Page Info */}
					{totalCount > 0 && (
						<Typography variant="caption" sx={{ display: "block", textAlign: "center", mt: 1, color: "text.secondary" }}>
							Page {Math.floor(skip / limit) + 1} of {Math.ceil(totalCount / limit)} • {totalCount} images total
						</Typography>
					)}
				</Box>
				)}
			</Box>
		</Modal>
	</>);
}


export default SelectImageFromApplication;