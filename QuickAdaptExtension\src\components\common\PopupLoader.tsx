import React from 'react';
import './PopupLoader.css';

interface PopupLoaderProps {
  isVisible: boolean;
}

const PopupLoader: React.FC<PopupLoaderProps> = ({ isVisible }) => {
  if (!isVisible) return null;

  return (
    <div className="qadpt-popup-loader">
      <div className="qadpt-loader-container">
        <div className="qadpt-loader-spinner"></div>
        <div className="qadpt-loader-text">QuickAdopt</div>
      </div>
    </div>
  );
};

export default PopupLoader;
